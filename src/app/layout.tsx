import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Suspense } from 'react'
import './globals.css'
import HeaderSection from '@/components/layout/header/HeaderSection'
import FooterSection from '@/components/layout/footer/FooterSection'
import ScrollToTopButton from '@/components/shared/ScrollToTopButton'
import { defaultMetadata } from './metadata'
import GoogleAnalyticsScript from '../components/GoogleAnalyticsScript'
import PageViewTracker from '@/components/analytics/PageViewTracker'
import GoogleAdsPageViewTracker from '@/components/analytics/GoogleAdsPageViewTracker'
import { GTM_ID } from '@/utils/analytics'
import { Providers } from './providers'
import Chatbot from '@/components/Chatbot'
const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = defaultMetadata

// Force dynamic rendering to avoid static generation issues with client components
export const dynamic = 'force-dynamic'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="192x192" href="/favicon-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/favicon-512x512.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#ffffff" />
        <GoogleAnalyticsScript />
      </head>
      <body className={`${inter.className} antialiased`}>
        {/* Google Tag Manager (noscript) */}
        <noscript dangerouslySetInnerHTML={{
          __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${GTM_ID}"
          height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
        }} />
        {/* End Google Tag Manager (noscript) */}

        <Providers>
          <div className="flex flex-col min-h-screen bg-white">
            <PageViewTracker />
            <GoogleAdsPageViewTracker />
            <HeaderSection />
            <main className="flex-grow relative">
              {children}
            </main>
            <FooterSection />
            <ScrollToTopButton />
            <Chatbot />
          </div>
        </Providers>
      </body>
    </html>
  )
}
