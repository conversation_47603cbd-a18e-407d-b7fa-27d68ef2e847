import { Metadata } from 'next'

interface SEOProps {
  title: string
  description: string
  keywords?: string[]
  ogImage?: string
}

export function generateSEOMetadata({
  title,
  description,
  keywords = [],
  ogImage = '/images/og-image.jpg',
}: SEOProps): Metadata {
  return {
    title,
    description,
    keywords: [
      'preschool',
      'early education',
      'childcare',
      'kindergarten',
      'daycare',
      'child development',
      'early learning',
      ...keywords,
    ],
    openGraph: {
      title,
      description,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      type: 'website',
      locale: 'en_US',
      siteName: 'Tulip Kids',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [ogImage],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code', // Add your Google verification code
    },
  }
}
