'use client';

import Image from 'next/image';
import Link from 'next/link';
import { ChevronDown, Search, X, MessageCircle, School2, GraduationCap } from 'lucide-react';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [openCategories, setOpenCategories] = useState<Record<string, boolean>>({});
  const [activeProgram, setActiveProgram] = useState<'preschool' | 'afterschool'>('preschool');
  const [searchFocused, setSearchFocused] = useState(false);

  // Filter FAQs based on search query
  const filterFAQs = (faqs: typeof usaProgramsFAQs.preschool) => {
    if (!searchQuery) return faqs;
    
    return faqs.map(category => ({
      ...category,
      items: category.items.filter(
        item =>
          item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.answer.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    })).filter(category => category.items.length > 0);
  };

  const filteredPreschoolFAQs = filterFAQs(usaProgramsFAQs.preschool);
  const filteredAfterSchoolFAQs = filterFAQs(usaProgramsFAQs.afterSchool);

  const toggleCategory = (categoryName: string) => {
    setOpenCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName],
    }));
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 mt-20 mx-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/programs/hero-programs.jpg"
            alt="Tulip Kids FAQ"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">FAQ</span>
                </li>
              </ol>
            </nav>

            <div className="max-w-3xl">
              <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                Help Center
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Frequently Asked Questions
              </h1>
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed mb-8">
                Find comprehensive answers to common questions about our programs, enrollment process, and services at Tulip Kids
              </p>
              
              <div className="relative max-w-2xl">
                <input
                  type="text"
                  placeholder="Search for answers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setSearchFocused(true)}
                  onBlur={() => setSearchFocused(false)}
                  className="w-full px-6 py-4 pr-12 rounded-xl text-gray-900 placeholder-gray-500 bg-white/95 backdrop-blur-sm shadow-lg focus:outline-none focus:ring-2 focus:ring-[#009990] transition-all duration-300"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  >
                    <X size={20} />
                  </button>
                )}
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            {/* Program Selection */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex gap-6 mb-16 justify-center"
            >
              <button
                onClick={() => setActiveProgram('preschool')}
                className={`group relative flex items-center gap-3 px-8 py-4 rounded-xl transition-all duration-300 ${
                  activeProgram === 'preschool'
                    ? 'bg-white shadow-lg'
                    : 'hover:bg-white/50'
                }`}
              >
                <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-colors duration-300 ${
                  activeProgram === 'preschool' ? 'bg-[#074799] text-white' : 'bg-gray-100 text-gray-500 group-hover:bg-gray-200'
                }`}>
                  <School2 size={24} />
                </div>
                <div className="text-left">
                  <span className={`block font-semibold transition-colors duration-300 ${
                    activeProgram === 'preschool' ? 'text-[#074799]' : 'text-gray-600'
                  }`}>
                    Preschool
                  </span>
                  <span className="text-sm text-gray-500">Ages 2-5</span>
                </div>
              </button>

              <button
                onClick={() => setActiveProgram('afterschool')}
                className={`group relative flex items-center gap-3 px-8 py-4 rounded-xl transition-all duration-300 ${
                  activeProgram === 'afterschool'
                    ? 'bg-white shadow-lg'
                    : 'hover:bg-white/50'
                }`}
              >
                <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-colors duration-300 ${
                  activeProgram === 'afterschool' ? 'bg-[#009990] text-white' : 'bg-gray-100 text-gray-500 group-hover:bg-gray-200'
                }`}>
                  <GraduationCap size={24} />
                </div>
                <div className="text-left">
                  <span className={`block font-semibold transition-colors duration-300 ${
                    activeProgram === 'afterschool' ? 'text-[#009990]' : 'text-gray-600'
                  }`}>
                    After School
                  </span>
                  <span className="text-sm text-gray-500">Grades K-5</span>
                </div>
              </button>
            </motion.div>

            {/* FAQ Categories */}
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              className="space-y-6"
            >
              <AnimatePresence mode="wait">
                {(activeProgram === 'preschool' ? filteredPreschoolFAQs : filteredAfterSchoolFAQs).map(
                  (category, categoryIndex) => (
                    <motion.div
                      key={category.category}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: categoryIndex * 0.1 }}
                      className="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100"
                    >
                      <button
                        onClick={() => toggleCategory(category.category)}
                        className="w-full px-8 py-6 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                      >
                        <h3 className="text-xl font-semibold text-gray-900">
                          {category.category}
                        </h3>
                        <ChevronDown
                          className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${
                            openCategories[category.category] ? 'transform rotate-180' : ''
                          }`}
                        />
                      </button>
                      <AnimatePresence>
                        {openCategories[category.category] && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="divide-y divide-gray-100">
                              {category.items.map((item, itemIndex) => (
                                <div key={itemIndex} className="p-8">
                                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                                    {item.question}
                                  </h4>
                                  <p className="text-gray-600 leading-relaxed">
                                    {item.answer}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  )
                )}
              </AnimatePresence>

              {/* No Results Message */}
              <AnimatePresence>
                {((activeProgram === 'preschool' ? filteredPreschoolFAQs : filteredAfterSchoolFAQs)
                  .length === 0) && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="text-center py-16"
                  >
                    <p className="text-gray-500 text-xl">
                      No results found for "{searchQuery}"
                    </p>
                    <p className="text-gray-400 mt-2">
                      Try adjusting your search terms or browse all questions below
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-3xl mx-auto text-center"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-[#074799]/10 text-[#074799] mb-8">
              <MessageCircle size={32} />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Can't find the answer you're looking for? Our friendly team is here to help.
            </p>
            <Link
              href="/contact"
              className="inline-flex items-center justify-center px-8 py-4 rounded-xl bg-gradient-to-r from-[#074799] to-[#009990] text-white hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl"
            >
              Contact Our Team
            </Link>
          </motion.div>
        </div>
      </section>
    </main>
  );
}
