import { Metadata } from 'next'
import HomeHeroSection from '@/components/shared/HomeHeroSection'
import WhyChooseTulip from '@/components/shared/WhyChooseTulip'
import ParentTestimonials from '@/components/shared/ParentTestimonials'
import HomeFAQSection from '@/components/sections/HomeFAQSection'
import ContactForm from '@/components/shared/ContactForm'
import ScrollToTopButton from '@/components/shared/ScrollToTopButton'
import LocationDirectory from '@/components/shared/LocationDirectory'
import BlogPreview from '@/components/blog/BlogPreview'
import LocationModal from '@/components/shared/LocationModal'

export const metadata: Metadata = {
  
  title: 'Best Preschool for Kids, Nursery, Kindergarten & Play School - Tulip Kids',
  description: 'Tulip Kids is one of the best preschools. We offer Playgroup, Nursery & Kindergarten for kids. Visit our nearest Play school for your child\'s admission.',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com'
  },
  openGraph: {
    title: 'Best Preschool for Kids, Nursery, Kindergarten & Play School - Tulip Kids',
    description: 'Tulip Kids is one of the best preschools. We offer Playgroup, Nursery & Kindergarten for kids. Visit our nearest Play school for your child\'s admission.',
    url: 'https://www.tulipkidsinc.com',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Preschool for Kids, Nursery, Kindergarten & Play School - Tulip Kids',
    description: 'Tulip Kids is one of the best preschools. We offer Playgroup, Nursery & Kindergarten for kids. Visit our nearest Play school for your child\'s admission.'
  }

}

export default function Home() {
  return (
    <div className="home-container">
      <LocationModal />
      <HomeHeroSection />
      <WhyChooseTulip />
      <LocationDirectory />
      <ParentTestimonials />
      <HomeFAQSection />
      <BlogPreview />
    </div>
  )
}
