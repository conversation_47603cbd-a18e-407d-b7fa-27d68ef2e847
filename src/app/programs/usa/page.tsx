import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { preschoolPrograms } from '@/data/programs/preschool';

export const metadata: Metadata = {
  title: 'USA Programs | Tulip Kids',
  description: 'Explore our comprehensive early education programs including Pre-K, Preschool, Jr. Preschool, and Infant/Toddler care.',
};

export default function USAProgramsPage() {
  return (
    <main className="pt-[120px]">
      {/* Hero Section */}
      <section className="relative h-[400px] flex items-center justify-center overflow-hidden">
        <Image
          src="/images/programs/hero-programs.jpg"
          alt="Tulip Kids Programs"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-[#001A6E]/80" />
        <div className="relative container mx-auto px-4 text-center text-white">
          <h1 className="text-5xl font-bold mb-6">Our Programs</h1>
          <p className="text-xl max-w-2xl mx-auto">
            Comprehensive early education programs designed to nurture your child's development at every stage
          </p>
        </div>
      </section>

      {/* Programs Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {preschoolPrograms.map((program) => (
              <Link
                key={program.id}
                href={`/programs/usa/${program.id}`}
                className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="relative h-64">
                  {program.gallery && program.gallery[0] && (
                    <Image
                      src={program.gallery[0].src}
                      alt={program.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  )}
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#009990] text-white px-4 py-1 rounded-full text-sm">
                      Ages {program.ageRange}
                    </span>
                  </div>
                </div>
                <div className="p-8">
                  <h2 className="text-2xl font-bold text-[#001A6E] mb-3 group-hover:text-[#009990] transition-colors">
                    {program.title}
                  </h2>
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {program.subtitle}
                  </p>
                  <div className="flex items-center text-[#009990] font-medium">
                    Learn More 
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-2 transition-transform" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-[#001A6E] mb-6">
              Why Choose Tulip Kids?
            </h2>
            <p className="text-lg text-gray-600 mb-12">
              Our programs are designed to provide the best possible start for your child's educational journey
            </p>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-xl shadow-md">
                <div className="w-12 h-12 bg-[#E8F5E9] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-[#009990]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Structured Curriculum</h3>
                <p className="text-gray-600">
                  Age-appropriate learning materials and activities
                </p>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-md">
                <div className="w-12 h-12 bg-[#E8F5E9] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-[#009990]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Expert Teachers</h3>
                <p className="text-gray-600">
                  Qualified and experienced educators
                </p>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-md">
                <div className="w-12 h-12 bg-[#E8F5E9] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-[#009990]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Safe Environment</h3>
                <p className="text-gray-600">
                  Secure facilities with modern safety measures
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
