
import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'After School Programs in USA | Tulip Kids',
  description: 'Tulip Kids offers after-school programs in USA for school-age children to continue learning outside of school. Check out our after school program.',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/programs/usa/after-school'
  },
  keywords: [
    'after school program',
    'child care',
    'academic support',
    'extracurricular activities',
    'enrichment program',
    'homework help',
    'STEAM activities',
    'Bay Area after school'
  ],
  openGraph: {
  
    title: 'After School Programs in USA | Tulip Kids',
    description: 'Tulip Kids offers after-school programs in USA for school-age children to continue learning outside of school. Check out our after school program.',
    url: 'https://www.tulipkidsinc.com/programs/usa/after-school',
    siteName: 'Tulip Kids',
    type: 'website',
    images: ['/images/programs/after-school.jpg']
  },
  twitter: {
    card: 'summary_large_image',
    title: 'After School Programs in USA | Tulip Kids',
    description: 'Tulip Kids offers after-school programs in USA for school-age children to continue learning outside of school. Check out our after school program.'

  }
};

export default function AfterSchoolLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
