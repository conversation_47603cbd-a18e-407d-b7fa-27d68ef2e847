'use client';

import React from 'react';
import { usaLocations } from '@/data/locations';
import { ArrowUpRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  CheckCircle2, 
  MapPin, 
  <PERSON>rkles, 
  Brain, 
  Book, 
  Palette, 
  Code, 
  Heart,
  ArrowRight,
  Clock,
  Users,
  GraduationCap
} from 'lucide-react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';

// Filter only locations that are exclusively after-school programs
const afterSchoolLocations = Object.values(usaLocations).filter(loc => 
  loc.type?.includes('afterSchool') && !loc.type?.includes('preschool')
);

console.log('After School Locations:', afterSchoolLocations.map(loc => loc.name));

if (afterSchoolLocations.length === 0) {
  console.warn('No after-school locations found in the data');
}

export default function AfterSchoolProgramPage() {
  const features = [
    {
      title: 'Academic Excellence',
      description: 'Comprehensive academic support and enrichment programs',
      icon: Book,
      color: '#009990',
      items: [
        {
          title: 'Homework Support',
          description: 'Dedicated time and guidance for completing homework assignments'
        },
        {
          title: 'Math & Science',
          description: 'Advanced math concepts and hands-on science experiments'
        },
        {
          title: 'Reading & Writing',
          description: 'Literature appreciation and creative writing workshops'
        },
        {
          title: 'Individual Attention',
          description: 'Personalized academic guidance and progress tracking'
        }
      ]
    },
    {
      title: 'Creative Expression',
      description: 'Diverse range of artistic and cultural activities',
      icon: Palette,
      color: '#6366F1',
      items: [
        {
          title: 'Visual Arts',
          description: 'Drawing, painting, and craft workshops'
        },
        {
          title: 'Performing Arts',
          description: 'Dance, drama, and music activities'
        },
        {
          title: 'Cultural Programs',
          description: 'Celebrating diversity through cultural events'
        },
        {
          title: 'Creative Projects',
          description: 'Collaborative art and design projects'
        }
      ]
    },
    {
      title: 'STEAM & Innovation',
      description: 'Cutting-edge technology and engineering programs',
      icon: Brain,
      color: '#EC4899',
      items: [
        {
          title: 'Coding & Robotics',
          description: 'Introduction to programming and robotics'
        },
        {
          title: 'Science Projects',
          description: 'Interactive experiments and discoveries'
        },
        {
          title: 'Engineering Challenges',
          description: 'Problem-solving and design thinking'
        },
        {
          title: 'Digital Skills',
          description: 'Age-appropriate technology education'
        }
      ]
    },
    {
      title: 'Personal Growth',
      description: 'Focus on physical health and emotional development',
      icon: Heart,
      color: '#F43F5E',
      items: [
        {
          title: 'Physical Fitness',
          description: 'Sports, yoga, and outdoor activities'
        },
        {
          title: 'Social Skills',
          description: 'Team building and communication development'
        },
        {
          title: 'Mindfulness',
          description: 'Stress management and emotional awareness'
        },
        {
          title: 'Leadership',
          description: 'Student-led projects and responsibilities'
        }
      ]
    }
  ];

  return (
    <main className="page-container">
      {/* Hero Section */}
      <section className="relative bg-[#001A6E]/90 rounded-[48px] mx-8 mt-8 mb-16 overflow-hidden min-h-[600px]">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/programs/after-school.jpg"
            alt="Tulip Kids After School Programs"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E] via-[#001A6E]/95 to-transparent"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-8 pt-32 pb-20">
          <div className="max-w-4xl">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-3 text-sm mb-8">
              <Link href="/" className="text-white/60 hover:text-white transition-colors">
                Home
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <Link href="/programs" className="text-white/60 hover:text-white transition-colors">
                Programs
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <span className="text-white">After School</span>
            </nav>

            <div className="text-white space-y-8">
              {/* Badge */}
              <div className="inline-block">
                <span className="bg-[#009990] text-white px-6 py-2 rounded-full text-sm font-medium">
                  After School Program
                </span>
              </div>

              {/* Main Content */}
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  Enriching After School Experience
                </h1>
                <p className="text-xl text-white/80 leading-relaxed max-w-3xl">
                  A comprehensive program that combines academic support with engaging activities, ensuring your child's growth beyond regular school hours.
                </p>
              </div>

              {/* Key Points */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/10 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Flexible Hours</h3>
                    <p className="text-white/70">Extended care available until 6:30 PM</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/10 flex items-center justify-center flex-shrink-0">
                    <Users className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Small Groups</h3>
                    <p className="text-white/70">Personalized attention & support</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/10 flex items-center justify-center flex-shrink-0">
                    <GraduationCap className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Expert Staff</h3>
                    <p className="text-white/70">Qualified educators & mentors</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Program Features</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                Comprehensive After School Care
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Our program is designed to provide a perfect balance of academic support, creative activities, and personal development.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all border border-gray-100"
                >
                  <div className="flex items-start gap-6 mb-8">
                    <div 
                      className="w-16 h-16 rounded-2xl flex items-center justify-center flex-shrink-0"
                      style={{ backgroundColor: `${feature.color}10` }}
                    >
                      {React.createElement(feature.icon, { 
                        className: "w-8 h-8", 
                        style: { color: feature.color } 
                      })}
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-[#001A6E] mb-2">{feature.title}</h3>
                      <p className="text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                  
                  <div className="grid sm:grid-cols-2 gap-6">
                    {feature.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="space-y-2">
                        <div className="flex items-start gap-3">
                          <CheckCircle2 
                            className="w-5 h-5 flex-shrink-0 mt-1" 
                            style={{ color: feature.color }}
                          />
                          <div>
                            <h4 className="font-semibold text-gray-900">{item.title}</h4>
                            <p className="text-sm text-gray-600">{item.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Our Centers Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Our Locations</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                Find an After School Program Near You
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Discover our after-school programs across various locations, each offering a unique blend of academic support and enrichment activities.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
              {afterSchoolLocations.map((location, index) => (
                <div 
                  key={index}
                  className="bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all border border-gray-100 overflow-hidden w-full max-w-md"
                >
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={location.heroImage}
                      alt={location.heroImageAlt}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="bg-[#009990] text-white text-sm font-medium px-3 py-1 rounded-full">
                        After School
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h4 className="text-xl font-bold text-[#001A6E] mb-2">
                      {location.name}
                    </h4>
                    
                    <div className="flex items-start gap-2 text-gray-600 mb-4">
                      <MapPin className="w-5 h-5 flex-shrink-0 mt-1" />
                      <div>
                        <p className="font-medium text-gray-900 mb-1">{location.location}</p>
                        <address className="not-italic text-sm">
                          {location.address?.street && (
                            <>
                              {location.address.street}<br />
                              {location.address.city}, {location.address.state} {location.address.zip}
                            </>
                          )}
                        </address>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-6">
                      {location.features?.map((feature, featureIndex) => (
                        <span 
                          key={featureIndex}
                          className="bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <Link 
                        href={`/locations/usa/${location.slug}`}
                        className="inline-flex items-center text-[#009990] font-medium hover:text-[#007A73] transition-colors"
                      >
                        View Details
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>

                      <Link
                        href={`/contact?location=${location.slug}&program=after-school`}
                        className="bg-[#009990] text-white px-4 py-2 rounded-lg hover:bg-[#007A73] transition-colors"
                      >
                        Enroll Now
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white via-gray-50/30 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6">
              <span className="text-[#009990] font-medium">FAQ</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
              Common Questions About After School
            </h2>
            <p className="text-lg text-gray-600">
              Find answers to frequently asked questions about our after school program
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {usaProgramsFAQs.afterSchool.slice(0, 2).map((category, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-[#074799] mb-4">
                  {category.category}
                </h3>
                <Accordion type="single" collapsible className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <AccordionItem
                      key={itemIndex}
                      value={`${index}-${itemIndex}`}
                      className="border border-gray-100 rounded-xl px-4 hover:border-[#009990]/30 transition-colors duration-300"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        <span className="text-gray-800 font-medium">{item.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600">
                        {item.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link 
              href="/faq" 
              className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-[#074799] to-[#009990] text-white hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl"
            >
              Browse All FAQs
              <ArrowRight size={20} />
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
