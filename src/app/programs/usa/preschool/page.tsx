import React from 'react';
import { Metadata } from 'next';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import ProgramTemplate from '@/components/templates/ProgramTemplate';

export const metadata: Metadata = {
  title: 'Preschool Programs in USA | Tulip Kids',
  description: 'Discover Tulip Kids\' preschool programs in the USA, designed to foster early learning and social skills in a fun, nurturing environment. Enroll your child today!',
  keywords: ['preschool programs USA', 'early learning USA', 'child education USA', 'Tulip Kids preschool', 'USA preschool curriculum'],
  openGraph: {
    title: 'Preschool Programs in USA | Tulip Kids',
    description: 'Discover Tulip Kids\' preschool programs in the USA, designed to foster early learning and social skills in a fun, nurturing environment.',
    url: 'https://www.tulipkidsinc.com/programs/usa/preschool',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

const PreschoolUSAPage = () => {
  const program = {
    id: 'preschool-program',
    title: 'Preschool Programs',
    subtitle: 'Comprehensive Early Education',
    description: 'Our Preschool Program focuses on developing essential skills through play-based learning, fostering curiosity, and building a strong foundation for future academic success.',
    features: [
      {
        title: 'Comprehensive Learning',
        description: 'Structured curriculum focusing on cognitive, social, and emotional development'
      },
      {
        title: 'Skilled Teachers',
        description: 'Experienced educators dedicated to early childhood development'
      },
      {
        title: 'Safe Environment',
        description: 'Secure and stimulating spaces designed for learning and play'
      }
    ],
    curriculum: [
      {
        title: 'Core Learning Areas',
        items: [
          'Language and Literacy',
          'Mathematics',
          'Science and Nature',
          'Arts and Creativity',
          'Physical Development'
        ]
      }
    ],
    schedule: {
      days: 'Monday - Friday',
      hours: '7:00 AM - 6:00 PM'
    },
    learningOutcomes: [
      'Strong foundation in early literacy and numeracy',
      'Enhanced social and emotional skills',
      'Developed fine and gross motor skills',
      'Improved problem-solving abilities',
      'Preparation for kindergarten success'
    ],
    icons: [
      {
        icon: 'BookOpen',
        label: 'Literacy & Learning'
      },
      {
        icon: 'Brain',
        label: 'Cognitive Development'
      },
      {
        icon: 'HeartHandshake',
        label: 'Social Skills'
      }
    ]
  };

  return (
    <>
      <ProgramTemplate program={program} />

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white via-gray-50/30 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6">
              <span className="text-[#009990] font-medium">FAQ</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
              Common Questions About Preschool
            </h2>
            <p className="text-lg text-gray-600">
              Find answers to frequently asked questions about our preschool program
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {usaProgramsFAQs.preschool.slice(0, 2).map((category, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-[#074799] mb-4">
                  {category.category}
                </h3>
                <Accordion type="single" collapsible className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <AccordionItem
                      key={itemIndex}
                      value={`${index}-${itemIndex}`}
                      className="border border-gray-100 rounded-xl px-4 hover:border-[#009990]/30 transition-colors duration-300"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        <span className="text-gray-800 font-medium">{item.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600">
                        {item.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link 
              href="/faq" 
              className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-[#074799] to-[#009990] text-white hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl"
            >
              Browse All FAQs
              <ArrowRight size={20} />
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default PreschoolUSAPage;
