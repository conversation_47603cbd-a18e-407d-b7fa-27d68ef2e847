import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import {
  BookOpen,
  Brain,
  Users,
  School2,
  GraduationCap,
  ClipboardCheck,
  MapPin,
  Phone,
  Mail,
  Award,
  Building2,
  Heart,
  Scale,
  BookCheck,
  Users2
} from 'lucide-react';
import ContactForm from '@/components/shared/ContactForm';

export const metadata: Metadata = {
  title: 'Teacher Training Programs | Tulip Kids',
  description: 'Join Tulip <PERSON>\' teacher training programs to enhance your skills in early childhood education. Inspire young minds and build a rewarding career with us.',
  keywords: ['teacher training', 'early childhood education', 'preschool teacher training', 'Tulip Kids careers', 'education careers'],
  openGraph: {
    title: 'Teacher Training Programs | Tulip Kids',
    description: 'Join <PERSON>lip <PERSON>\' teacher training programs to enhance your skills in early childhood education. Inspire young minds and build a rewarding career with us.',
    url: 'https://www.tulipkidsinc.com/programs/teachers-training',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

const whatFeatures = [
  {
    icon: Brain,
    title: 'Child Development',
    description: 'Understanding developmental milestones and age-appropriate learning'
  },
  {
    icon: BookOpen,
    title: 'Educational Theories',
    description: 'Modern pedagogical approaches and learning frameworks'
  },
  {
    icon: Users,
    title: 'Classroom Management',
    description: 'Creating an effective and inclusive learning environment'
  },
  {
    icon: School2,
    title: 'Teaching Methods',
    description: 'Innovative techniques for engaging young learners'
  },
  {
    icon: GraduationCap,
    title: 'Inclusive Education',
    description: 'Supporting diverse learning needs and abilities'
  },
  {
    icon: ClipboardCheck,
    title: 'Assessment & Evaluation',
    description: 'Methods to track and support student progress'
  }
];

const whyChoose = [
  {
    icon: Brain,
    title: 'Child Development',
    description: 'Expert guidance in cognitive, emotional, social, and physical development'
  },
  {
    icon: Scale,
    title: 'Educational Equity',
    description: 'Promoting inclusive and accessible education for all'
  },
  {
    icon: Award,
    title: 'Professional Standards',
    description: 'Meeting and exceeding industry standards'
  },
  {
    icon: BookCheck,
    title: 'Research & Best Practices',
    description: 'Evidence-based approaches to early childhood education'
  },
  {
    icon: Heart,
    title: 'Community Impact',
    description: 'Creating positive change through education'
  }
];

const whoShould = [
  {
    icon: GraduationCap,
    title: 'Pre-service Teachers',
    description: 'Starting your journey in early childhood education'
  },
  {
    icon: Users,
    title: 'In-service Teachers',
    description: 'Enhancing your existing teaching skills'
  },
  {
    icon: Building2,
    title: 'Administrators',
    description: 'Leading educational institutions effectively'
  },
  {
    icon: Users2,
    title: 'Specialists',
    description: 'Focusing on specific areas of childhood development'
  },
  {
    icon: Scale,
    title: 'Policy Makers',
    description: 'Shaping the future of early childhood education'
  }
];

const locations = [
  {
    icon: GraduationCap,
    title: 'Online Learning',
    description: 'Learn at your own pace with our comprehensive online platform'
  },
  {
    icon: Building2,
    title: 'In-Person Training',
    description: 'Join us at Tulip Kids International, 11 Bima Nagar, Indore MP'
  },
  {
    icon: Users,
    title: 'Hybrid Learning',
    description: 'Combine online learning with in-person workshops'
  }
];

const getBenefits = (title: string) => {
  switch(title) {
    case 'Pre-service Teachers':
      return [
        'Gain a deeper understanding of child development and learning theories',
        'Develop effective teaching methods and classroom management skills',
        'Build a strong foundation for a successful teaching career'
      ];
    case 'In-service Teachers':
      return [
        'Enhance your teaching skills and stay updated on best practices',
        'Improve student outcomes and increase parent satisfaction',
        'Advance your career and take on leadership roles'
      ];
    case 'Administrators':
      return [
        'Develop strategic leadership skills and effective management practices',
        'Improve school culture and increase staff morale',
        'Enhance student outcomes and increase parent engagement'
      ];
    case 'Specialists':
      return [
        'Gain specialized knowledge and skills in a specific area of childhood development',
        'Develop effective strategies for supporting diverse learning needs',
        'Enhance your career prospects and increase earning potential'
      ];
    case 'Policy Makers':
      return [
        'Develop a deeper understanding of early childhood education and its impact on society',
        'Create effective policies and programs that support young children and families',
        'Improve outcomes for vulnerable populations and reduce inequality'
      ];
    default:
      return [];
  }
};

const getFeatureDetails = (title: string) => {
  switch(title) {
    case 'Child Development':
      return [
        'Cognitive development',
        'Emotional development',
        'Social development',
        'Physical development'
      ];
    case 'Educational Theories':
      return [
        'Constructivist theory',
        'Social constructivist theory',
        'Behaviorist theory'
      ];
    case 'Classroom Management':
      return [
        'Creating a positive learning environment',
        'Managing classroom behavior',
        'Encouraging student participation'
      ];
    case 'Teaching Methods':
      return [
        'Project-based learning',
        'Inquiry-based learning',
        'Technology integration'
      ];
    case 'Inclusive Education':
      return [
        'Supporting diverse learning needs',
        'Creating inclusive lesson plans',
        'Using assistive technology'
      ];
    case 'Assessment & Evaluation':
      return [
        'Formative assessment',
        'Summative assessment',
        'Using assessment data to inform instruction'
      ];
    default:
      return [];
  }
};

export default function TeachersTraining() {
  return (
    <div className="min-h-screen pt-24">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/programs/teachers-training.jpg"
            alt="Teacher Training Program at Tulip Kids"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/programs" className="hover:text-white transition-colors">
                    Programs
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">Teachers Training</span>
                </li>
              </ol>
            </nav>
            <div className="space-y-6">
              <div>
                <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                  Professional Development
                </span>
                <h1 className="text-4xl md:text-5xl font-bold text-white">
                  Empowering Teachers, Shaping Future Generations
                </h1>
              </div>
              <p className="text-xl text-white/90 max-w-2xl">
                Join our comprehensive teacher training program and master the art of early childhood education through hands-on experience and expert guidance.
              </p>
              <div className="flex gap-4 pt-4">
                <Link
                  href="/programs/teachers-training/register"
                  className="bg-[#009990] text-white px-8 py-3 rounded-lg hover:bg-[#008880] hover:shadow-lg hover:scale-105 transition-all duration-300"
                >
                  Start Your Journey
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Overview Cards Section */}
      <section className="py-16 -mt-8 relative z-20">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-12">
            <h2 className="text-[42px] font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#074799] to-[#009990]">
              Program Overview
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover our comprehensive teacher training program designed to empower educators at every level.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* What Card */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <BookOpen className="w-5 h-5 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E]">What</h3>
              </div>
              <p className="text-gray-600 mb-4">
                A comprehensive curriculum covering child development, educational theories, classroom management, and effective teaching methods.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Child Development
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Educational Theories
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Classroom Management
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Teaching Methods
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Inclusive Education
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Assessment and Evaluation
                </li>
              </ul>
            </div>

            {/* Why Card */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <Heart className="w-5 h-5 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E]">Why</h3>
              </div>
              <p className="text-gray-600 mb-4">
                High-quality early childhood education is crucial for cognitive, emotional, social, and physical development.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Child Development
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Educational Equity
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Professional Standards
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Research and Best Practices
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Community Impact
                </li>
              </ul>
            </div>

            {/* Who Card */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <Users2 className="w-5 h-5 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E]">Who</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Our program caters to a diverse group of education professionals.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Pre-service Teachers
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  In-service Teachers
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Administrators
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Specialists
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Policy Makers
                </li>
              </ul>
            </div>

            {/* Where Card */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E]">Where</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Flexible learning options to suit your schedule.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Online Learning
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  In-person at Tulip Kids International
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                  Hybrid Learning Options
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* What We Offer Section */}
      <section className="py-24 bg-gradient-to-br from-[#001A6E] to-[#074799] relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-[#009990] mix-blend-overlay filter blur-[120px] rounded-full"></div>
          <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-[#E6FFF9] mix-blend-overlay filter blur-[120px] rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 md:px-8 relative z-10">
          {/* Header */}
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12 mb-20">
            <div className="lg:w-1/2">
              <h2 className="text-[42px] font-bold text-white leading-tight">
                Comprehensive Early Education Training Program
              </h2>
            </div>
            <div className="lg:w-1/2">
              <p className="text-xl text-white/80">
                Our curriculum is designed to equip educators with the knowledge and skills needed to create engaging, effective learning environments for young minds.
              </p>
            </div>
          </div>

          {/* Main Content */}
          <div className="relative">
            {/* Center Feature */}
            <div className="bg-white/10 backdrop-blur-sm rounded-[40px] p-12 mb-12">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-6">Core Curriculum Focus</h3>
                  <div className="space-y-4">
                    {whatFeatures.slice(0, 3).map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-4 text-white/80">
                        <div className="w-2 h-2 bg-[#009990] rounded-full"></div>
                        <span>{feature.title}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#009990]/20 to-transparent rounded-3xl"></div>
                  <div className="relative bg-white/5 rounded-3xl p-8">
                    <div className="grid grid-cols-2 gap-6">
                      {[
                        'Interactive Learning',
                        'Practical Experience',
                        'Modern Techniques',
                        'Professional Growth'
                      ].map((item, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                            <div className="w-3 h-3 bg-[#009990] rounded-full"></div>
                          </div>
                          <span className="text-white/90 text-sm font-medium">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {whatFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="relative group"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-[#009990] to-[#074799] rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 h-full hover:bg-transparent transition-colors duration-300">
                    <div className="flex flex-col h-full">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-2xl flex items-center justify-center mb-6">
                        <feature.icon className="w-6 h-6 text-[#009990]" />
                      </div>
                      <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                      <p className="text-white/70 mb-6 flex-grow">{feature.description}</p>
                      <div className="space-y-2">
                        {getFeatureDetails(feature.title).slice(0, 2).map((detail, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                            <span className="text-sm text-white/60">{detail}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Who Should Join Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-[#E6FFF9]/10 to-white"></div>
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-[#009990]/10 to-transparent rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-[#074799]/5 to-transparent rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>

        <div className="container mx-auto px-4 md:px-8 relative z-10">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-block">
              <span className="inline-block bg-[#E6FFF9] text-[#009990] text-sm font-semibold px-4 py-2 rounded-full mb-4">
                Join Our Community
              </span>
            </div>
            <h2 className="text-[42px] font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#074799] to-[#009990]">
              Who Should Join Our Program
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive program is designed for education professionals at every stage of their career journey.
            </p>
          </div>

          {/* Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {whoShould.map((person, index) => (
              <div
                key={index}
                className="group relative bg-white rounded-2xl p-8 transition-all duration-300
                         hover:shadow-[0_0_30px_rgba(0,153,144,0.1)] hover:-translate-y-1"
              >
                {/* Card Header */}
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-14 h-14 bg-[#E6FFF9] rounded-xl flex items-center justify-center
                                group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                    <person.icon className="w-7 h-7 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#001A6E] group-hover:text-[#074799] transition-colors">
                      {person.title}
                    </h3>
                    <p className="text-gray-500 mt-1 text-sm">
                      {person.description}
                    </p>
                  </div>
                </div>

                {/* Benefits */}
                <div className="space-y-4">
                  <div className="h-px bg-gradient-to-r from-[#E6FFF9] to-transparent"></div>
                  <div>
                    <h4 className="font-semibold text-[#009990] text-sm uppercase tracking-wider mb-3">
                      What You'll Gain
                    </h4>
                    <ul className="space-y-3">
                      {getBenefits(person.title).map((benefit, idx) => (
                        <li key={idx} className="flex items-start gap-3 text-sm text-gray-600 group/item">
                          <div className="w-5 h-5 bg-[#E6FFF9] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5
                                      group-hover/item:scale-110 transition-transform">
                            <div className="w-1.5 h-1.5 bg-[#009990] rounded-full"></div>
                          </div>
                          <span className="group-hover/item:text-gray-900 transition-colors">
                            {benefit}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Hover Border Effect */}
                <div className="absolute inset-0 rounded-2xl transition-all duration-300
                              bg-gradient-to-br from-[#074799]/0 to-[#009990]/0 group-hover:from-[#074799]/5 group-hover:to-[#009990]/5
                              opacity-0 group-hover:opacity-100"></div>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="text-center mt-12">
            <Link
              href="/programs/teachers-training/register"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-[#074799] to-[#009990] text-white
                        px-8 py-4 rounded-full font-semibold hover:shadow-lg hover:scale-105 transition-all duration-300"
            >
              Start Your Journey
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Training Locations Section */}
      <section className="py-24 bg-gradient-to-b from-white via-gray-50 to-white">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-16">
            <h2 className="text-[42px] font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#074799] to-[#009990]">
              Training Locations
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible learning options to suit your schedule.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {locations.map((location, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-8">
                <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                  <location.icon className="w-8 h-8 text-[#009990]" />
                </div>
                <h3 className="text-2xl font-bold text-[#001A6E] mb-4 text-center">{location.title}</h3>
                <p className="text-gray-600 text-center">{location.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4 md:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-[42px] font-bold mb-6 text-[#001A6E]">
                Get in Touch
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Have questions about our teacher training program? We're here to help you get started on your journey.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="w-14 h-14 bg-[#E6FFF9] rounded-2xl flex items-center justify-center mb-6 mx-auto">
                  <MapPin className="w-7 h-7 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E] mb-3 text-center">Indore, India</h3>
                <p className="text-gray-600 text-center">+91 9575545200</p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="w-14 h-14 bg-[#E6FFF9] rounded-2xl flex items-center justify-center mb-6 mx-auto">
                  <Phone className="w-7 h-7 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E] mb-3 text-center">Call Us</h3>
                <p className="text-gray-600 text-center">+91 95755 45952</p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="w-14 h-14 bg-[#E6FFF9] rounded-2xl flex items-center justify-center mb-6 mx-auto">
                  <Mail className="w-7 h-7 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E] mb-3 text-center">Email Us</h3>
                <p className="text-gray-600 text-center"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
