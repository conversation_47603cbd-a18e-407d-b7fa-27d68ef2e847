import Image from 'next/image';
import Link from 'next/link';
import TeacherTrainingForm from '@/components/TeacherTrainingForm';

export default function TeacherTrainingRegistration() {
  return (
    <main className="page-container min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/programs/teachers-training.jpg"
            alt="Teacher Training Program Registration at Tulip Kids"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="text-white/60 mx-2">→</span>
                </li>
                <li>
                  <Link href="/programs/teachers-training" className="hover:text-white transition-colors">
                    Teacher Training
                  </Link>
                </li>
                <li>
                  <span className="text-white/60 mx-2">→</span>
                </li>
                <li>
                  <span className="text-white">Registration</span>
                </li>
              </ol>
            </nav>
            <span className="inline-block px-3 py-1 bg-[#009990] text-white text-sm rounded-full mb-4">
              Teacher Training Program
            </span>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Start Your Teaching Journey
            </h1>
            <p className="text-xl text-white/90 max-w-2xl">
              Join our comprehensive teacher training program and transform your passion for teaching into expertise.
            </p>
          </div>
        </div>
      </section>

      {/* Form Section */}
      <section className="py-16">
        <TeacherTrainingForm />
      </section>
    </main>
  );
}
