import { Metadata } from 'next';
import IndiaProgramTemplate from '@/components/templates/IndiaProgramTemplate';

export const metadata: Metadata = {
  title: 'Preschool Programs in India | Tulip Kids',
  description: 'Tulip Kids provides top-quality preschool programs in India, including playgroup, nursery, and kindergarten. Give your child the best start in life. Enroll now!',
  keywords: ['preschool programs India', 'playgroup India', 'nursery education India', 'kindergarten India', 'Tulip Kids preschool'],
  openGraph: {
    title: 'Preschool Programs in India | Tulip Kids',
    description: 'Tulip Kids provides top-quality preschool programs in India, including playgroup, nursery, and kindergarten. Give your child the best start in life.',
    url: 'https://www.tulipkidsinc.com/programs/india/preschool',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

export default function PreschoolProgramIndiaPage() {
  const program = {
    id: 'preschool-program-india',
    title: 'Pre School Program',
    subtitle: 'Holistic Early Education',
    description: 'Our Pre School program provides a nurturing environment where children develop essential skills through structured play-based learning and activities.',
    ageRange: '2 - 3.5 years',
    features: [
      {
        title: 'Comprehensive Learning',
        description: 'Structured curriculum focusing on cognitive, social, and emotional development'
      },
      {
        title: 'Expert Teachers',
        description: 'Experienced educators dedicated to early childhood development'
      },
      {
        title: 'Safe Environment',
        description: 'Secure and stimulating spaces designed for learning and play'
      }
    ],
    curriculum: [
      {
        title: 'Core Learning Areas',
        items: [
          'Language and Literacy (English & Hindi)',
          'Mathematics',
          'Science and Nature',
          'Arts and Creativity',
          'Physical Development'
        ]
      }
    ],
    schedule: {
      days: 'Monday - Friday',
      hours: '9:00 AM - 3:30 PM'
    },
    learningOutcomes: [
      'Strong foundation in early literacy and numeracy',
      'Enhanced social and emotional skills',
      'Developed fine and gross motor skills',
      'Improved problem-solving abilities',
      'Preparation for next level success'
    ],
    icons: [
      {
        icon: 'BookOpen',
        label: 'Literacy & Learning'
      },
      {
        icon: 'Brain',
        label: 'Cognitive Development'
      },
      {
        icon: 'HeartHandshake',
        label: 'Social Skills'
      }
    ],
    country: 'india' as const,
    heroImage: '/images/programs/preschool-india.jpg',
    heroImageAlt: 'Children engaged in learning activities at Tulip Kids India Pre School',
    indiaPrograms: [
      {
        title: "Jr Pre School",
        ageRange: "2 - 3 years",
        path: "/programs/india/junior-preschool",
        description: "Early developmental program focusing on sensory learning and basic skills",
        color: "from-[#FF6B6B] to-[#FF8E8E]"
      },
      {
        title: "Pre School",
        ageRange: "3 - 4 years",
        path: "/programs/india/preschool",
        description: "Foundational learning program with play-based curriculum",
        color: "from-[#4ECDC4] to-[#45B7AF]"
      },
      {
        title: "Pre K",
        ageRange: "4 - 5 years",
        path: "/programs/india/pre-k",
        description: "Advanced preschool program preparing for kindergarten",
        color: "from-[#96C93D] to-[#7EAB2E]"
      },
      {
        title: "Kindergarten",
        ageRange: "5 - 6 years",
        path: "/programs/india/kindergarten",
        description: "Comprehensive kindergarten education program",
        color: "from-[#A18CD1] to-[#8675C4]"
      }
    ]
  };

  return <IndiaProgramTemplate program={program} />;
}
