import { Metadata } from 'next'
import LocationsPage from '@/components/shared/LocationsPage'

export const metadata: Metadata = {
  title: 'Our Locations | Tulip Kids',
  description: 'Find Tulip Kids preschool locations across the United States and India.',
  keywords: ['preschool locations', 'childcare centers', 'Bay Area preschool', 'India preschool', 'Tulip Kids locations'],
  openGraph: {
    title: 'Tulip Kids Locations',
    description: 'Find a Tulip Kids preschool location near you.',
    type: 'website',
    url: 'https://tulipkidsinc.com/locations',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tulip Kids Locations',
    description: 'Find a Tulip Kids preschool location near you.',
  },
}

export default function Page() {
  return <LocationsPage />
}
