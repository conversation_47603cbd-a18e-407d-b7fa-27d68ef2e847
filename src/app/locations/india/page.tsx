'use client'

import React, { useState } from 'react';
import { indiaLocations } from '@/data/locations';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowUpRight } from 'lucide-react';
import { MapPin, Phone, Mail, CheckCircle2, ArrowRight, Shield, School2, GraduationCap, Home, TreePine, Smartphone, Plus, Minus } from 'lucide-react'

const locations = [
  {
    id: 1,
    name: 'Indore - Anand Bazaar',
    image: '/images/locations/India.jpg',
    address: '11, Bima Nagar, Anand Bazaar, Indore, MP 452001',
    phone: '+91 95755 45952',
    email: '<EMAIL>',
    type: ['preschool'],
    features: [
      'Modern Learning Environment',
      'Outdoor Play Area',
      'Safe & Secure Campus'
    ]
  },
  {
    id: 2,
    name: 'Indore - Nipania',
    image: '/images/locations/nipania.jpg',
    address: '27, Samar Park Colony, Nipania, Indore, MP 452010',
    phone: '+91 9575545200',
    email: '<EMAIL>',
    type: ['preschool'],
    features: [
      'State-of-the-art Facilities',
      'Dedicated Activity Areas',
      'Professional Staff'
    ]
  }
]

const FAQItem = ({ question, answer }: { question: string; answer: string }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex justify-between items-center w-full py-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg font-semibold">{question}</span>
        {isOpen ? (
          <Minus className="w-5 h-5 text-[#009990]" />
        ) : (
          <Plus className="w-5 h-5 text-[#009990]" />
        )}
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

const FAQSection = ({ faq }: { faq: Array<{ question: string; answer: string }> }) => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-8">
        <h2 className="text-4xl font-bold mb-12 text-center">Frequently Asked Questions</h2>
        <div className="max-w-3xl mx-auto">
          {faq.map((item, index) => (
            <FAQItem key={index} question={item.question} answer={item.answer} />
          ))}
        </div>
      </div>
    </section>
  );
}

export default function IndiaLandingPage() {
  return (
    <main>
      {/* Hero Section */}
      <section className="relative bg-[#001A6E]/90 rounded-[48px] mx-8 mt-8 mb-16 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/taj-mehal.jpg"
            alt="Tulip Kids India - Early Education Center"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 to-[#001A6E]/80"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-8 pt-32 pb-20">
          <div className="max-w-4xl">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-3 text-sm mb-8">
              <Link href="/" className="text-white/60 hover:text-white transition-colors">
                Home
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <Link href="/locations" className="text-white/60 hover:text-white transition-colors">
                Locations
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <span className="text-white">India</span>
            </nav>

            <div className="text-white space-y-8">
              {/* Badge */}
              <div className="inline-block">
                <span className="bg-[#009990] text-white px-6 py-2 rounded-full text-sm font-medium">
                  Welcome to Tulip Kids India
                </span>
              </div>

              {/* Main Content */}
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  Shaping Young Minds for a Promising Future
                </h1>
                <p className="text-xl text-white/80 leading-relaxed max-w-3xl">
                  Experience world-class early childhood education with a perfect blend of modern teaching methods and Indian cultural values.
                </p>
              </div>

              {/* CTA Button */}
              <div>
                <Link
                  href="/locations/india/bima-nagar"
                  className="bg-[#009990] hover:bg-[#009990]/90 text-white px-8 py-3 rounded-full font-medium transition-colors inline-flex items-center gap-2 mr-4"
                >
                  Visit Bima Nagar
                  <ArrowRight className="w-4 h-4" />
                </Link>
                <Link
                  href="/locations/india/nipania"
                  className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-full font-medium transition-colors inline-flex items-center gap-2"
                >
                  Visit Nipania
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Tulip Kids</h2>
            <p className="text-lg text-gray-600">
              Experience excellence in early childhood education with our comprehensive approach to learning,
              safety, and development in a nurturing environment that embraces Indian values.
            </p>
          </div>
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold">Holistic Development</h3>
              </div>
              <p className="text-gray-600">
                Our curriculum focuses on cognitive, social, emotional, and physical development while incorporating Indian cultural values.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center">
                  <Shield className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold">Safe Environment</h3>
              </div>
              <p className="text-gray-600">
                State-of-the-art security systems and trained staff ensure your child's safety and well-being throughout their time with us.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center">
                  <School2 className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold">Expert Educators</h3>
              </div>
              <p className="text-gray-600">
                Our experienced teachers are passionate about early childhood education and committed to nurturing each child's potential.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <span className="text-[#009990] font-medium mb-4 block">OUR PROGRAMS</span>
            <h2 className="text-4xl font-bold mb-6 text-[#002B5B]">Nurturing Young Minds</h2>
            <p className="text-lg text-gray-600">
              Age-appropriate programs designed to foster growth, creativity, and learning in a nurturing environment
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {/* Junior Pre-School Card */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#009990]/5 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#009990]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-[#E8F9F3] rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-[#009990]/20 transition-colors duration-300">
                  <GraduationCap className="w-10 h-10 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">Junior Pre-School</h3>
                <p className="text-[#009990] font-medium mb-6">2-3 years</p>
                <ul className="text-gray-600 text-left space-y-4">
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Nurturing, responsive caregiving</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-75">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Age-appropriate activities</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-100">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Safe learning environment</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Preschool Program Card */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#009990]/5 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#009990]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-[#E8F9F3] rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-[#009990]/20 transition-colors duration-300">
                  <School2 className="w-10 h-10 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">Preschool Program</h3>
                <p className="text-[#009990] font-medium mb-6">3-4 years</p>
                <ul className="text-gray-600 text-left space-y-4">
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Structured learning activities</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-75">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Social skill development</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-100">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Motor skill development</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Pre-K Program Card */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#009990]/5 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#009990]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-[#E8F9F3] rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-[#009990]/20 transition-colors duration-300">
                  <Shield className="w-10 h-10 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">Pre-K Program</h3>
                <p className="text-[#009990] font-medium mb-6">4-5 years</p>
                <ul className="text-gray-600 text-left space-y-4">
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Advanced learning concepts</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-75">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Critical thinking skills</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-100">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>School readiness</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Kindergarten Card */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#009990]/5 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#009990]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-20 h-20 bg-[#E8F9F3] rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-[#009990]/20 transition-colors duration-300">
                  <School2 className="w-10 h-10 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">Kindergarten</h3>
                <p className="text-[#009990] font-medium mb-6">5-6 years</p>
                <ul className="text-gray-600 text-left space-y-4">
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>School readiness skills</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-75">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Advanced concepts</span>
                  </li>
                  <li className="flex items-start gap-3 group-hover:transform group-hover:translate-x-1 transition-transform duration-200 delay-100">
                    <CheckCircle2 className="w-5 h-5 text-[#009990] mt-0.5 flex-shrink-0" />
                    <span>Independent learning</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Locations Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h1 className="text-4xl font-bold mb-6">Our India Locations</h1>
            <p className="text-lg text-gray-600">
              Discover our premier early childhood education centers in Indore, Madhya Pradesh.
              We offer preschool programs in state-of-the-art facilities designed to inspire
              learning and growth with Indian cultural values.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {locations.map((location) => (
              <div key={location.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="relative h-56">
                  <Image
                    src={location.image}
                    alt={location.name}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4 flex gap-2">
                    {location.type.includes('preschool') && (
                      <span className="bg-[#009990] text-white px-3 py-1 rounded-full text-sm font-medium">
                        Preschool
                      </span>
                    )}
                  </div>
                </div>
                <div className="p-8">
                  <h3 className="text-2xl font-semibold mb-4">{location.name}</h3>
                  <div className="flex items-start gap-3 text-gray-600 mb-4">
                    <MapPin className="w-6 h-6 mt-1 flex-shrink-0 text-[#009990]" />
                    <p className="text-lg">{location.address}</p>
                  </div>
                  <div className="flex items-center gap-3 text-gray-600 mb-4">
                    <Phone className="w-6 h-6 flex-shrink-0 text-[#009990]" />
                    <p className="text-lg">{location.phone}</p>
                  </div>
                  <div className="flex items-center gap-3 text-gray-600 mb-6">
                    <Mail className="w-6 h-6 flex-shrink-0 text-[#009990]" />
                    <p className="text-lg">{location.email}</p>
                  </div>
                  <Link
                    href={`/locations/india/${location.id === 1 ? 'bima-nagar' : 'nipania'}`}
                    className="inline-flex items-center text-[#009990] hover:text-[#074799] font-medium text-lg"
                  >
                    Enquire Now <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link
              href="/locations"
              className="inline-flex items-center px-6 py-3 text-base font-medium text-[#009990] hover:text-[#074799]"
            >
              View All Locations <ArrowRight className="w-4 h-4 ml-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Facilities Section */}
      <section className="py-20 bg-[#F8FAFC]">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="max-w-3xl mx-auto text-center mb-20">
            <span className="text-[#009990] font-medium mb-4 block">WORLD-CLASS FACILITIES</span>
            <h2 className="text-4xl font-bold mb-6 text-[#002B5B]">Our School Facilities</h2>
            <p className="text-lg text-gray-600 leading-relaxed">
              One of the most important decisions you are going to make for your child is selecting a preschool for them.
              The kind of preschool you pick will affect your child's physical, cognitive, and socio-emotional development
              and its pace. Facilities at our pre-school ensure just that.
            </p>
          </div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* Left Column - Image Gallery */}
            <div className="relative grid grid-cols-2 gap-4 h-[600px]">
              <div className="space-y-4">
                <div className="relative h-72 rounded-2xl overflow-hidden shadow-lg group">
                  <Image
                    src="/images/india/gallery/tulip-playarea.jpg"
                    alt="Tulip Play Area"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h4 className="text-white font-semibold text-lg">Creative Play Area</h4>
                    <p className="text-white/80 text-sm">Designed for physical development</p>
                  </div>
                </div>
                <div className="relative h-64 rounded-2xl overflow-hidden shadow-lg group">
                  <Image
                    src="/images/india/gallery/tulip-library.png"
                    alt="Library Area"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h4 className="text-white font-semibold text-lg">Modern Library Area</h4>
                    <p className="text-white/80 text-sm">Safe space for reading activities</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4 pt-8">
                <div className="relative h-64 rounded-2xl overflow-hidden shadow-lg group">
                  <Image
                    src="/images/india/gallery/tulip-safe.jpg"
                    alt="Safe Environment"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h4 className="text-white font-semibold text-lg">Safe Environment</h4>
                    <p className="text-white/80 text-sm">Safe & secure learning environment</p>
                  </div>
                </div>
                <div className="relative h-72 rounded-2xl overflow-hidden shadow-lg group">
                  <Image
                    src="/images/india/gallery/tulip-tech.jpg"
                    alt="Modern Technology"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h4 className="text-white font-semibold text-lg">Interactive Learning</h4>
                    <p className="text-white/80 text-sm">Technology-enabled education</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Features */}
            <div className="space-y-8 lg:pt-8">
              {/* Feature 1 */}
              <div className="group bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#E8F9F3] rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <Home className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">
                      Carefully Designed Rooms
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      Our classrooms are thoughtfully designed to create an optimal learning environment that
                      promotes creativity, exploration, and development.
                    </p>
                  </div>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="group bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#E8F9F3] rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <TreePine className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">
                      Large Outdoor Playarea
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      We know children need space to learn, grow and play and so we have large outdoor play area with
                      soft surface. Non-toxic and age appropriate toys and play sets are kept for physical growth.
                    </p>
                  </div>
                </div>
              </div>

              {/* Feature 3 */}
              <div className="group bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#E8F9F3] rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <Shield className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">
                      Safe and Enabling Environment
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      Safety is our top priority. Our facilities are equipped with modern security systems and
                      our staff is trained to maintain a secure, nurturing environment.
                    </p>
                  </div>
                </div>
              </div>

              {/* Feature 4 */}
              <div className="group bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#E8F9F3] rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <Smartphone className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#002B5B] mb-2 group-hover:text-[#009990] transition-colors duration-300">
                      Cutting Edge Technology
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      We utilize modern technology for enhanced learning experiences and maintain real-time
                      communication with parents about their child's progress and activities.
                    </p>
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <div className="pt-4">
                <Link
                  href="/contact?country=india&location=india"
                  className="inline-flex items-center justify-center w-full bg-[#009990] text-white px-8 py-3 rounded-xl hover:bg-[#007c75] transition-colors duration-200 font-medium text-lg"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">
                Nurturing Young Minds Since 2010
              </h2>
              <p className="text-lg text-gray-600">
                At Tulip Kids India, we blend international early education standards with Indian cultural values.
                Our journey began with a mission to provide high-quality, personalized education
                that nurtures both academic excellence and cultural awareness.
              </p>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <School2 className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Cultural Integration</h3>
                    <p className="text-gray-600">
                      Our curriculum seamlessly integrates Indian cultural values with modern educational methods.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Safety First</h3>
                    <p className="text-gray-600">
                      Safety is our top priority. Our facilities are equipped with modern security systems and
                      our staff is trained to maintain a secure, nurturing environment.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <GraduationCap className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Expert Educators</h3>
                    <p className="text-gray-600">
                      Our teachers are highly qualified professionals trained in both modern and traditional teaching methods.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="relative h-48 rounded-lg overflow-hidden">
                  <Image
                    src="/images/india/gallery/1.jpg"
                    alt="Cultural Activities"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative h-64 rounded-lg overflow-hidden">
                  <Image
                    src="/images/india/gallery/02.jpg"
                    alt="Learning Environment"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-4 pt-8">
                <div className="relative h-64 rounded-lg overflow-hidden">
                  <Image
                    src="/images/india/gallery/03.jpg"
                    alt="Student Activities"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative h-48 rounded-lg overflow-hidden">
                  <Image
                    src="/images/india/gallery/04.jpg"
                    alt="Outdoor Learning"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#074799] text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-6">Begin Your Child's Learning Journey</h2>
            <p className="text-xl mb-8">
              Schedule a tour of our facilities and discover why Tulip Kids is the perfect blend
              of modern education and Indian values for your child's early years.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact?country=india&location=india"
                className="inline-flex items-center justify-center px-8 py-4 border border-transparent rounded-md shadow-lg text-lg font-medium text-[#074799] bg-white hover:bg-[#009990] hover:text-white transform hover:scale-105 transition-all duration-300 ease-in-out"
              >
                Schedule a Tour
              </Link>

            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection
        faq={[
          {
            question: "What curriculum do you follow?",
            answer: "We follow an integrated curriculum that combines international early education standards with Indian cultural values and traditions. Our program is designed to prepare children for both Indian and international schooling systems."
          },
          {
            question: "What are your operating hours?",
            answer: "Our centers are open Monday through Friday from 09:00 AM to 03:30 PM."
          },
          {
            question: "What safety measures do you have?",
            answer: "We maintain strict security protocols including CCTV surveillance, biometric access, and trained security personnel. All staff undergo thorough background checks and regular safety training."
          }
        ]}
      />
    </main>
  )
}
