import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Best PreSchool in India | PlayGroups and Nursery School',
  description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in India. We offer playgroup, nursery & kindergarten for kids. For franchise contact us!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/india'
  },
  openGraph: {
    title: 'Best PreSchool in India | PlayGroups and Nursery School',
    description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in India. We offer playgroup, nursery & kindergarten for kids. For franchise contact us!',
    url: 'https://www.tulipkidsinc.com/locations/india',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best PreSchool in India | PlayGroups and Nursery School',
    description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in India. We offer playgroup, nursery & kindergarten for kids. For franchise contact us!'
  }
};

export default function IndiaLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
