import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Best Preschools in Mountain House, CA | Tulip Kids',
  description: 'Tulip Kids Play school, one of the best preschools in Mountain House, CA, offering exceptional early education, nurturing environment, and holistic development.',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/mountain-house'
  },
  openGraph: {
    title: 'Best Preschools in Mountain House, CA | Tulip Kids',
    description: 'Tulip Kids Play school, one of the best preschools in Mountain House, CA, offering exceptional early education, nurturing environment, and holistic development.',
    url: 'https://www.tulipkidsinc.com/locations/usa/mountain-house',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Preschools in Mountain House, CA | Tulip Kids',
    description: 'Tulip Kids Play school, one of the best preschools in Mountain House, CA, offering exceptional early education, nurturing environment, and holistic development.'
  }
};

export default function MountainHouseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
