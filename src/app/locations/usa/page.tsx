'use client';

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import {
  MapPin,
  Phone,
  Mail,
  ArrowRight,
  School2,
  Shield,
  GraduationCap,
  Award,
  Users,
  Building,
  CheckCircle2,
  TreePine,
  Building2
} from 'lucide-react'
import ExcellenceSection from '@/components/sections/ExcellenceSection';

const locations = [
  {
    id: 'willow-ave',
    name: 'Sunnyvale - Willow Ave',
    address: '1159 Willow Ave',
    city: 'Sunnyvale',
    state: 'CA',
    zip: '94086',
    phones: {
      preschool: '(*************',
      afterSchool: '(*************'
    },
    image: '/images/locations/Willow-ave.jpg',
    type: ['preschool', 'afterSchool'],
    enrollmentLinks: {
      preschool: 'https://schools.procareconnect.com/register/tulip-kids-willow-ave-sunnyvale-94086',
      afterSchool: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c'
    }
  },
  {
    id: 'lawrence-station',
    name: 'Sunnyvale - Lawrence Station',
    address: '1279 Lawrence Station Rd',
    city: 'Sunnyvale',
    state: 'CA',
    zip: '94089',
    phones: {
      preschool: '************'
    },
    image: '/images/locations/lawrence-station-road.png',
    type: ['preschool'],
    enrollmentLinks: {
      preschool: 'https://schools.procareconnect.com/register/tulip-kids-ls-sunnyvale-94086'
    }
  },
  {
    id: 'el-camino',
    name: 'Santa Clara - El Camino Real',
    address: '2280 El Camino Real',
    city: 'Santa Clara',
    state: 'CA',
    zip: '95050',
    phones: {
      preschool: '************'
    },
    image: '/images/locations/El Camino Real.webp',
    type: ['preschool'],
    enrollmentLinks: {
      preschool: 'https://schools.procareconnect.com/register/2596080c-cc3c-43bc-8d2b-3835b893d805'
    }
  },
  {
    id: 'cottle-road',
    name: 'San Jose - Cottle Road',
    address: '6097 Cottle Rd',
    city: 'San Jose',
    state: 'CA',
    zip: '95123',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Cottle Road.jpg',
    type: ['preschool'],
    enrollmentLinks: {
      preschool: 'https://schools.procareconnect.com/register/673683fa-25cf-4328-a931-4d6c5d4ebfad'
    }
  },
  {
    id: 'mountain-house',
    name: 'Mountain House',
    address: '768 N Montebello St',
    city: 'Mountain House',
    state: 'CA',
    zip: '95391',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Mountain House.jpg',
    type: ['preschool'],
    enrollmentLinks: {
      preschool: 'https://schools.procareconnect.com/register/3ea394f5-2b40-4150-85d1-2ce734c856ae'
    }
  },
  {
    id: 'grafton-st-after',
    name: 'Dublin - Grafton St',
    address: '4078 Grafton St',
    city: 'Dublin',
    state: 'CA',
    zip: '94568',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Dublin.jpg',
    type: ['preschool', 'afterSchool'],
    enrollmentLinks: {
      preschool: '/contact',
      afterSchool: '/contact'
    }
  }
];

export default function USALandingPage() {
  return (
    <main>
      {/* Hero Section */}
      <section className="relative bg-[#001A6E]/90 rounded-[48px] mx-8 mt-8 mb-16 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/locations/san-francisco.jpg"
            alt="Tulip Kids USA - Early Education Centers"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E] via-[#001A6E]/95 to-transparent"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-8 pt-32 pb-20">
          <div className="max-w-4xl">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-3 text-sm mb-8">
              <Link href="/" className="text-white/60 hover:text-white transition-colors">
                Home
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <Link href="/locations" className="text-white/60 hover:text-white transition-colors">
                Locations
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <span className="text-white">USA</span>
            </nav>

            <div className="text-white space-y-8">
              {/* Badge */}
              <div className="inline-block">
                <span className="bg-[#009990] text-white px-6 py-2 rounded-full text-sm font-medium">
                  Welcome to Tulip Kids USA
                </span>
              </div>

              {/* Main Content */}
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  Premium Early Education in the Bay Area
                </h1>
                <p className="text-xl text-white/80 leading-relaxed max-w-3xl">
                  Discover our state-of-the-art preschool and after-school centers across the San Francisco Bay Area, providing exceptional care and education.
                </p>
              </div>

              {/* CTA Button */}
              <div>
                <Link
                  href="/locations"
                  className="bg-[#009990] hover:bg-[#009990]/90 text-white px-8 py-3 rounded-full font-medium transition-colors inline-flex items-center gap-2"
                >
                  View Locations
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Locations Grid */}
      <section id="locations" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Our Centers</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                Find a Location Near You
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Choose from our multiple locations across the Bay Area, each offering a unique blend of education and care tailored to your child's needs.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {locations.map((location) => (
                <div key={location.id} className="bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow overflow-hidden group">
                  <div className="relative h-48">
                    <Image
                      src={location.image}
                      alt={location.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 right-4 flex gap-2">
                      {location.type.includes('preschool') && (
                        <span className="bg-[#009990] text-white text-xs px-3 py-1 rounded-full">Preschool</span>
                      )}
                      {location.type.includes('afterSchool') && (
                        <span className="bg-[#001A6E] text-white text-xs px-3 py-1 rounded-full">After School</span>
                      )}
                    </div>
                  </div>
                  <div className="p-6">
                    {/* Location Name with Icon */}
                    <Link
                      href={`/locations/usa/${location.id}`}
                      className="group flex items-center gap-2 mb-4 hover:text-[#009990] transition-colors duration-300"
                    >
                      <Building2 className="w-6 h-6 text-[#009990]" />
                      <h3 className="text-xl font-semibold text-[#001A6E] group-hover:text-[#009990]">
                        {location.name}
                      </h3>
                    </Link>

                    {/* Location Details */}
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-[#009990] mt-1 flex-shrink-0" />
                        <div className="text-gray-600">
                          <p>{location.address}</p>
                          <p>{location.city}, {location.state} {location.zip}</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Phone className="w-5 h-5 text-[#009990] mt-1 flex-shrink-0" />
                        <div className="text-gray-600">
                          {location.phones.preschool && (
                            <p>Preschool: {location.phones.preschool}</p>
                          )}
                          {location.phones.afterSchool && (
                            <p>After School: {location.phones.afterSchool}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Enrollment Links */}
                    <div className="flex flex-col gap-2 mt-4">
                      {/* For locations with both Preschool and After School programs */}
                      {location.type.includes('preschool') && location.type.includes('afterSchool') ? (
                        <>
                          {location.enrollmentLinks?.preschool && (
                            <a
                              href={location.enrollmentLinks.preschool}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-4 py-2 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors duration-300"
                            >
                              Enroll in Preschool
                            </a>
                          )}
                          {location.enrollmentLinks?.afterSchool && (
                            <a
                              href={location.enrollmentLinks.afterSchool}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-4 py-2 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors duration-300"
                            >
                              Enroll in After School
                            </a>
                          )}
                        </>
                      ) : (
                        /* For locations with single program */
                        <>
                          {location.type.includes('preschool') && location.enrollmentLinks?.preschool && (
                            <a
                              href={location.enrollmentLinks.preschool}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-4 py-2 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors duration-300"
                            >
                              Enroll Now
                            </a>
                          )}
                          {location.type.includes('afterSchool') && location.enrollmentLinks?.afterSchool && (
                            <a
                              href={location.enrollmentLinks.afterSchool}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-4 py-2 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors duration-300"
                            >
                              Enroll Now
                            </a>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Why Choose Us</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                Excellence in Early Education
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                At Tulip Kids USA, we're committed to providing the highest quality early education and care for your children.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                  <GraduationCap className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold text-[#001A6E] mb-3">Expert Educators</h3>
                <p className="text-gray-600">
                  Highly qualified teachers dedicated to nurturing your child's growth and development.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                  <Shield className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold text-[#001A6E] mb-3">Safe Environment</h3>
                <p className="text-gray-600">
                  State-of-the-art security systems and strict safety protocols for peace of mind.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                  <School2 className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold text-[#001A6E] mb-3">Modern Facilities</h3>
                <p className="text-gray-600">
                  Well-equipped classrooms and outdoor spaces designed for learning and play.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                  <TreePine className="w-6 h-6 text-[#009990]" />
                </div>
                <h3 className="text-xl font-semibold text-[#001A6E] mb-3">Bay Area Presence</h3>
                <p className="text-gray-600">
                  Multiple convenient locations across the San Francisco Bay Area.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
