import { Metadata } from 'next';
import React from 'react';

export async function generateMetadata(): Promise<Metadata> {
  return {
    metadataBase: new URL('https://tulipkidsinc.com'),
    title: 'Tulip Kids Academy | Pre-School at Sunnyvale | Best Play Schools, Kindergarten in CA',
    description: 'Tulip Kids Preschools in Sunnyvale, is the leading choice for Play School, Nursery, and Kindergarten education for kids. We have 15 years of excellence and 8+ schools across 2 countries. Get Admission Today!',
    alternates: {
      canonical: 'https://www.tulipkidsinc.com/locations/usa/willow-ave'
    },
    openGraph: {
      title: 'Tulip Kids Academy | Pre-School at Sunnyvale | Best Play Schools, Kindergarten in CA',
      description: 'Tulip Kids Preschools in Sunnyvale, is the leading choice for Play School, Nursery, and Kindergarten education for kids. We have 15 years of excellence and 8+ schools across 2 countries. Get Admission Today!',
      url: 'https://www.tulipkidsinc.com/locations/usa/willow-ave',
      siteName: 'Tulip Kids',
      type: 'website',
      images: [
        {
          url: 'https://www.tulipkidsinc.com/images/willow-ave-pre/willo-ave-pre%201.webp',
          width: 800,
          height: 600,
          alt: 'Tulip Kids Academy at Sunnyvale'
        }
      ]
    }
  };
}

// JSON-LD Schema
const jsonLd = {
  '@context': 'http://www.schema.org',
  '@type': 'LocalBusiness',
  'name': 'Tulip Kids Academy | Pre-School at Sunnyvale | Best Play Schools, Kindergarten in CA',
  'url': 'https://www.tulipkidsinc.com/locations/usa/willow-ave',
  'logo': 'https://www.tulipkidsinc.com/images/tulip-logo.png',
  'image': 'https://www.tulipkidsinc.com/images/willow-ave-pre/willo-ave-pre%201.webp',
  'description': 'Tulip Kids Preschools in Sunnyvale, is the leading choice for Play School, Nursery, and Kindergarten education for kids. We have 15 years of excellence and 8+ schools across 2 countries. Get Admission Today!',
  'priceRange': 'US Dollar',
  'aggregateRating': {
    '@type': 'AggregateRating',
    'ratingValue': '4.3/5',
    'reviewCount': '6'
  },
  'address': {
    '@type': 'PostalAddress',
    'streetAddress': 'Address: 1159 Willow Ave, Sunnyvale',
    'addressLocality': 'Sunnyvale',
    'addressRegion': 'CA',
    'postalCode': '94086',
    'addressCountry': 'United States'
  },
  'telephone': '******-245-0602',
  'geo': {
    '@type': 'GeoCoordinates',
    'latitude': '37.36824',
    'longitude': '-121.99796'
  },
  'openingHours': ['Mon-Fri 8:00-18:00', 'Sun-Sat Closed'],
  'contactPoint': [
    {
      '@type': 'ContactPoint',
      'telephone': '******-245-0602',
      'contactType': 'customer support',
      'areaServed': ['IN'],
      'availableLanguage': ['English']
    },
    {
      '@type': 'ContactPoint',
      'telephone': '******-245-0602',
      'contactType': 'technical support',
      'areaServed': ['USA'],
      'availableLanguage': ['English']
    }
  ],
  'sameAs': [
    'https://www.facebook.com/people/Tulip-Kids-Inc/100057178641270/',
    'https://www.instagram.com/tulip_kids_inc'
  ]
};

export default function WillowAveLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      {children}
    </>
  );
}
