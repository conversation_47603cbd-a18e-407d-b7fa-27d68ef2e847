import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Grafton St After School | Safe & Enriching After-School Program',
  description: 'Welcome to Grafton St After School! A safe, engaging program offering homework support, enrichment activities, and supervised play for school-age children. Join us today!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/grafton-st-after'
  },
  openGraph: {
    title: 'Grafton St After School | Safe & Enriching After-School Program',
    description: 'Welcome to Grafton St After School! A safe, engaging program offering homework support, enrichment activities, and supervised play for school-age children. Join us today!',
    url: 'https://www.tulipkidsinc.com/locations/usa/grafton-st-after',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Grafton St After School | Safe & Enriching After-School Program',
    description: 'Welcome to Grafton St After School! A safe, engaging program offering homework support, enrichment activities, and supervised play for school-age children. Join us today!'
  }
};

export default function GraftonStAfterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
