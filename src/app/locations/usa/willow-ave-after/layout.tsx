import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Tulip Kids Inc After School Program - Willow Ave After School, USA',
  description: 'Discover the Tulip Kids Inc After School Program at our Willow Ave location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/willow-ave-after'
  },
  openGraph: {
    title: 'Tulip Kids Inc After School Program - Willow Ave After School, USA',
    description: 'Discover the Tulip Kids Inc After School Program at our Willow Ave location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!',
    url: 'https://www.tulipkidsinc.com/locations/usa/willow-ave-after',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tulip Kids Inc After School Program - Willow Ave Location, USA',
    description: 'Discover the Tulip Kids Inc After School Program at our Willow Ave location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!'
  }
};

export default function WillowAveAfterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
