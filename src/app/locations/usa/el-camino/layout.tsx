import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Best Preschools in Santa Clara, CA | Tulip Kids',
  description: 'Looking for preschool for kids? Tulip Kids is one of the Best Preschools in Santa Clara, CA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/el-camino'
  },
  openGraph: {
    title: 'Best Preschools in Santa Clara, CA | Tulip Kids',
    description: 'Looking for preschool for kids? Tulip Kids is one of the Best Preschools in Santa Clara, CA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!',
    url: 'https://www.tulipkidsinc.com/locations/usa/el-camino',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Preschools in Santa Clara, CA | Tulip Kids',
    description: 'Looking for preschool for kids? Tulip Kids is one of the Best Preschools in Santa Clara, CA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!'
  }
};

export default function ElCaminoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
