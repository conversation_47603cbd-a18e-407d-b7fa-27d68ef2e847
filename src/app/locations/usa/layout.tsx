import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Best Pre School in USA | Play Groups and Kindergarten',
  description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in USA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa'
  },
  openGraph: {
    title: 'Best Pre School in USA | Play Groups and Kindergarten',
    description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in USA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!',
    url: 'https://www.tulipkidsinc.com/locations/usa',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Pre School in USA | Play Groups and Kindergarten',
    description: 'Looking for preschool for kids? Tulip Kids is one of the best play school in USA. We offer playgroup, nursery & kindergarten for children. For franchise contact us!'
  }
};

export default function USALayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
