'use client'
import React, { useState } from 'react';

const FranchiseLeadForm: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Basic validation
    if (!name || !email || !phone) {
      setError('All fields are required');
      return;
    }
    // Handle form submission logic here
    console.log('Form submitted:', { name, email, phone });
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2>Franchise Lead Capture</h2>
      {error && <p className="error">{error}</p>}
      <input
        type="text"
        placeholder="Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
        required
      />
      <input
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      <input
        type="tel"
        placeholder="Phone"
        value={phone}
        onChange={(e) => setPhone(e.target.value)}
        required
      />
      <button type="submit">Submit</button>
    </form>
  );
};

export default FranchiseLeadForm;