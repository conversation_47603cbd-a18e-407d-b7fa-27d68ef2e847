'use client';
import React, { useState } from 'react';
// import Franchi<PERSON>LeadForm from '@app/franchise/FranchiseLeadForm';

import {
  Award,
  Users,
  BookOpen,
  Play,
  ChevronRight,
  Banknote,
  CheckCircle,
  Map,
  ArrowRight,
  Download
} from 'lucide-react';


// Dummy Data
const journeySteps = [
  { title: 'Step 1: Enquiry', description: 'Fill out the form to show your interest.' },
  { title: 'Step 2: Evaluation', description: 'Our team evaluates your application.' },
  { title: 'Step 3: Agreement', description: 'Sign the franchise agreement.' },
  { title: 'Step 4: Training', description: 'Undergo comprehensive training.' },
  { title: 'Step 5: Launch', description: 'Launch your franchise with support.' }
];

const successStories = [
  { image: '/images/story1.jpg', name: '<PERSON><PERSON><PERSON>', location: 'Indore', achievement: 'Achieved break-even in 8 months' },
  { image: '/images/story2.jpg', name: '<PERSON><PERSON><PERSON>ed<PERSON>', location: 'Willow Ave, USA', achievement: 'Exceeded revenue targets by 30%' }
];

const faqs = [
  { question: 'What is the initial investment?', answer: 'The initial investment ranges from ₹12L to ₹25L.', hasLink: false },
  { question: 'What support do you offer?', answer: 'We offer comprehensive training and ongoing support.', hasLink: true }
];

const investmentData = [
  { name: 'Franchise Fee', value: 40 },
  { name: 'Training & Support', value: 20 },
  { name: 'Setup Costs', value: 25 },
  { name: 'Miscellaneous', value: 15 }
];

const Franchise = () => {
  const [quizStep, setQuizStep] = useState(0);
  const [formData, setFormData] = useState({ investment: '', location: '', space: '' });
  const [roiData, setRoiData] = useState({ location: '', investment: '' });

  const handleQuizSubmit = () => {
    console.log('Form submitted:', formData);
  };

  return (
    <div>
      {/* Hero Section */}

<div className="relative min-h-[84vh] bg-cover bg-center" style={{ backgroundImage: "url('/images/taj-mehal.jpg')" }}>
  <div className="absolute inset-0 bg-black opacity-70"></div> {/* Overlay */}
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16 mt-32 relative z-10">
    <div className="grid lg:grid-cols-2 gap-12 items-center">


            {/* Left Column */}
            <div className="text-white space-y-6">
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Own a Tulip Kids Franchise – Shape Futures, Build Wealth
              </h1>
              <p className="text-xl md:text-2xl opacity-90">
                Join India's Fastest-Growing Preschool Network with 90% ROI in 2 Years
              </p>
              <div className="flex gap-4 items-center">
                <button 
                  className="bg-white text-[#074799] px-6 py-3 rounded-full font-semibold hover:bg-[#E1FFBB] transition-all duration-300"
                >
                  Apply Now for Franchise Details
                </button>
                <button className="flex items-center gap-2 text-white hover:text-[#E1FFBB] transition-all duration-300">
                  <Play className="w-6 h-6" /> Watch Video
                </button>
              </div>
              <div className="flex gap-6 pt-6">
                <div className="flex items-center gap-2">
                  <Award className="w-6 h-6" />
                  <span>Best Preschool Franchise 2025</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-6 h-6" />
                  <span>10+ Franchise Partners</span>
                </div>
              </div>
            </div>
            {/* Right Column - Quick Qualifier Form */}
            <div id="qualifierForm" className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-[#074799] mb-6">Is a Tulip Kids Franchise Right for You?</h2>
              <div className="space-y-6">
                {quizStep === 0 && (
                  <Step 
                    label="Investment Range" 
                    options={['<10L', '10-15L', '15-20L', '20-25L', '>25L']} 
                    field="investment" 
                    formData={formData} 
                    setFormData={setFormData} 
                    setStep={() => setQuizStep(1)} 
                  />
                )}
                {quizStep === 1 && (
                  <Step 
                    label="Preferred Location" 
                    options={['Metro City', 'Tier 2 City', 'Town']} 
                    field="location" 
                    formData={formData} 
                    setFormData={setFormData} 
                    setStep={() => setQuizStep(2)} 
                  />
                )}
                {quizStep === 2 && (
                  <Step 
                    label="Available Space" 
                    options={['1500-2000', '2000-2500', '2500-3000']} 
                    field="space" 
                    formData={formData} 
                    setFormData={setFormData} 
                    setStep={handleQuizSubmit} 
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Why Tulip Kids Section */}
      <section className="bg-[#F9FAFB] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Why 85% of Our Franchisees Renew After 5 Years
          </h2>
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <USPCard icon={Banknote} title="Minimum Royalty Fee" description="Low royalty fees for the first year of operations" color="bg-[#E1FFBB]" />
            <USPCard icon={Users} title="360° Support" description="Comprehensive training, marketing & tech support" color="bg-[#FFE4D6]" />
            <USPCard icon={BookOpen} title="Proven Curriculum" description="Globally recognized early education framework" color="bg-[#D5F5FF]" />
          </div>
          {/* Video Testimonial */}
          <div className="relative rounded-2xl overflow-hidden bg-[#074799] text-white p-8 cursor-pointer hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-center space-x-4">
              <Play className="w-16 h-16" />
              <div>
                <h3 className="text-xl font-bold mb-2">Watch Franchisee Success Story</h3>
                <p>See how our partners achieved success in just 60 seconds</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Competition Comparison Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Tulip Kids vs Other Preschool Franchises
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-[#F9FAFB]">
                  <th className="py-4 px-6 text-left font-bold">Metric</th>
                  <th className="py-4 px-6 text-left font-bold">Tulip Kids</th>
                  <th className="py-4 px-6 text-left font-bold">Other Franchises</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b hover:bg-[#F9FAFB] transition-colors duration-300">
                  <td className="py-4 px-6">Initial Investment</td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 text-[#009990]" />
                      <span className="font-bold text-[#074799]">₹12L - ₹30L</span>
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">₹25L - ₹50L</td>
                </tr>
                <tr className="border-b hover:bg-[#F9FAFB] transition-colors duration-300">
                  <td className="py-4 px-6">Break-even Time</td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 text-[#009990]" />
                      <span className="font-bold text-[#074799]">6 Months</span>
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">14 Months</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* Investment Breakdown Section */}
      <section className="bg-[#F9FAFB] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Franchise Model Transparency
          </h2>
          <div className="grid md:grid-cols-2 gap-12">
            {/* Investment Breakdown */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-[#074799] mb-6">Total Investment: ₹12L - ₹30L</h3>
              <div className="space-y-4">
                {investmentData.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-[#074799]"></div>
                      <span className="text-gray-600">{item.name}</span>
                    </div>
                    <span className="font-bold text-[#074799]">{item.value}%</span>
                  </div>
                ))}
              </div>
            </div>
            {/* ROI Calculator */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-[#074799] mb-6">ROI Calculator</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-600 mb-2">Location Type</label>
                  <select 
                    className="w-full p-3 border rounded-xl focus:ring-2 focus:ring-[#074799]"
                    value={roiData.location}
                    onChange={(e) => setRoiData({ ...roiData, location: e.target.value })}
                  >
                    <option value="">Select Location</option>
                    <option value="metro">Metro City</option>
                    <option value="tier2">Tier 2 City</option>
                    <option value="tier3">Tier 3 City</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-600 mb-2">Initial Investment</label>
                  <select 
                    className="w-full p-3 border rounded-xl focus:ring-2 focus:ring-[#074799]"
                    value={roiData.investment}
                    onChange={(e) => setRoiData({ ...roiData, investment: e.target.value })}
                  >
                    <option value="">Select Investment Range</option>
                    <option value="12-15">₹12L - ₹15L</option>
                    <option value="15-20">₹15L - ₹20L</option>
                    <option value="20-30">₹20L - ₹30L</option>
                  </select>
                </div>
                <button className="w-full bg-[#074799] text-white py-3 rounded-xl hover:bg-[#009990] transition-all duration-300">
                  Calculate ROI
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Location Mapping Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Available Territories
          </h2>
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="grid md:grid-cols-3 gap-4 mb-8">
              {['Madhya Pradesh', 'Delhi', 'Maharashtra'].map((state) => (
                <button
                  key={state}
                  className="p-4 rounded-lg bg-gray-100 hover:bg-[#E1FFBB] transition-all duration-300"
                >
                  {state}
                </button>
              ))}
            </div>
            <button className="w-full bg-[#074799] text-white py-3 rounded-xl hover:bg-[#009990] transition-all duration-300 flex items-center justify-center gap-2">
              <Map className="w-5 h-5" />
              Lock Your Exclusive Territory → 24-Hour Hold
            </button>
          </div>
        </div>
      </section>

      {/* Franchise Journey Section */}
      <section className="bg-[#F9FAFB] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Your 5-Step Franchise Journey
          </h2>
          <div className="relative">
            <div className="absolute top-0 left-1/2 w-px h-full bg-[#074799] -translate-x-1/2 md:block hidden"></div>
            <div className="space-y-12">
              {journeySteps.map((step, index) => (
                <div key={index} className="relative flex items-center gap-8">
                  <div className="bg-[#074799] text-white rounded-full w-12 h-12 flex items-center justify-center flex-shrink-0 z-10">
                    {index + 1}
                  </div>
                  <div className="bg-white rounded-xl p-6 shadow-lg flex-grow">
                    <h3 className="font-bold text-[#074799] text-xl mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Franchisee Wall of Fame */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Franchisee Wall of Fame
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {successStories.map((story, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8">
                <img 
                  src={story.image} 
                  alt={story.name} 
                  className="w-full h-48 object-cover rounded-xl mb-4"
                />
                <h3 className="text-xl font-bold text-[#074799]">{story.name}, {story.location}</h3>
                <p className="text-gray-600">{story.achievement}</p>
              </div>
            ))}
          </div>
          <div className="text-center mt-8">
            <button className="inline-flex items-center gap-2 text-[#074799] font-bold hover:text-[#009990] transition-all duration-300">
              <Download className="w-5 h-5" /> Download Indore Franchise Success PDF
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-[#F9FAFB] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#074799] text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-[#074799] mb-2">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
                {faq.hasLink && (
                  <button className="text-[#009990] font-bold mt-2 flex items-center gap-2 hover:text-[#074799] transition-all duration-300">
                    Download Comparison <Download className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="bg-gradient-to-r from-[#074799] to-[#009990] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Only 3 Franchise Slots Left in Your State – Apply Before June 30!
          </h2>
          <button className="bg-white text-[#074799] px-8 py-4 rounded-full font-bold hover:bg-[#E1FFBB] transition-all duration-300 inline-flex items-center gap-2">
            Schedule a 1:1 Video Call with Team <ArrowRight className="w-5 h-5" />
          </button>
          <p className="text-white mt-4 opacity-90">Get ₹50K Waiver on Early Applications</p>
        </div>
      </section>
    </div>
  );
};

interface StepProps {
  label: string;
  options: string[];
  field: keyof { investment: string; location: string; space: string; };
  formData: { investment: string; location: string; space: string; };
  setFormData: (data: { investment: string; location: string; space: string; }) => void;
  setStep: () => void;
}

const Step: React.FC<StepProps> = ({ label, options, field, formData, setFormData, setStep }) => (
  <div className="space-y-4">
    <label className="block text-gray-700">{label}</label>
    <select 
      className="w-full p-3 border rounded-xl focus:ring-2 focus:ring-[#074799]"
      value={formData[field]}
      onChange={(e) => setFormData({ ...formData, [field]: e.target.value })}
    >
      <option value="">Select {label}</option>
      {options.map(option => <option key={option} value={option}>{option}</option>)}
    </select>
    <button 
      className="w-full bg-[#074799] text-white py-3 rounded-xl flex items-center justify-center gap-2 hover:bg-[#009990] transition-all duration-300"
      onClick={setStep}
    >
      Next <ChevronRight className="w-4 h-4" />
    </button>
  </div>
);

interface USPCardProps {
  icon: React.ComponentType<React.SVGAttributes<SVGElement>>;
  title: string;
  description: string;
  color: string;
}

const USPCard: React.FC<USPCardProps> = ({ icon: Icon, title, description, color }) => (
  <div className={`${color} rounded-2xl p-6 hover:shadow-xl transition-all duration-300`}> 
    <div className="flex flex-col items-center text-center">
      <Icon className="w-12 h-12 text-[#074799] mb-4" />
      <h3 className="text-xl font-bold text-[#074799] mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  </div>
);

export default Franchise;
