import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Preschool Franchise Opportunities | Partner with Tulip Kids',
  description: 'Explore preschool franchise opportunities with Tulip Kids, a trusted name in early childhood education. Join us to inspire young minds and build a successful educational venture.',
  keywords: ['preschool franchise', 'education franchise', 'Tulip Kids franchise', 'business opportunity', 'school franchise'],
  openGraph: {
    title: 'Preschool Franchise Opportunities | Partner with Tulip Kids',
    description: 'Explore preschool franchise opportunities with Tulip Kids, a trusted name in early childhood education. Join us to inspire young minds and build a successful educational venture.',
    url: 'https://www.tulipkidsinc.com/franchise',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

export const keywords = [
  'preschool franchise',
  'education franchise',
  'Tulip Kids franchise',
  'business opportunity',
  'school franchise',
  'playschool franchise',
  'franchise opportunities',
  'education business',
  'start a preschool',
  'early education franchise',
  'childcare franchise'
];
