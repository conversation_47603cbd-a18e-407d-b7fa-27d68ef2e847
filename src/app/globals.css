@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
  --primary-50: 245, 248, 255;
  --primary-600: 26, 75, 255;
  --primary-700: 0, 51, 230;
  --header-height: 104px;
}

/* Base Styles */
html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Layout */
#__next {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  width: 100%;
  position: relative;
}

/* Page Layout */
.page-container {
  padding-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}

/* Home Page Layout */
.home-container {
  min-height: 100vh;
}

/* Other Pages Layout */
.other-pages-container {
  padding-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}

.page-section {
  padding: 4rem 0;
}

.page-section-sm {
  padding: 2rem 0;
}

/* Components */
@layer components {
  .nav-link {
    @apply text-gray-700 transition-colors duration-200 font-medium;
  }

  .nav-link:hover {
    color: rgb(var(--primary-600));
  }

  .btn-primary {
    @apply px-6 py-2 rounded-full font-medium shadow-md transition-all duration-200;
    background-color: rgb(var(--primary-600));
    color: white;
  }

  .btn-primary:hover {
    background-color: rgb(var(--primary-700));
    @apply shadow-lg;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-gray-700 transition-colors duration-200;
  }

  .dropdown-item:hover {
    background-color: rgb(var(--primary-50));
    color: rgb(var(--primary-600));
  }
}

/* Container */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Layout Adjustments */
main {
  margin-top: 0;
}

/* Header layout */
.header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
}

.top-bar {
  height: 2.5rem;
  background-color: #001A6E;
}

.main-header {
  height: 5rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
}

/* Logo container */
.logo-container {
  position: relative;
  margin-left: -1.5rem;
}

@media (min-width: 1024px) {
  .logo-container {
    margin-left: -2rem;
  }
}

/* Navigation styles */
.nav-item {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.nav-item:hover {
  color: #074799;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #074799;
  transform: scaleX(0);
  transition: transform 0.2s ease;
}

.nav-item:hover::after {
  transform: scaleX(1);
}

/* Dropdown styles */
.dropdown-menu {
  top: 100%;
  left: 0;
  min-width: 14rem;
  padding: 0.5rem 0;
  margin-top: 0;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #4a5568;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f7fafc;
  color: #074799;
}

/* Hero section spacing */
.hero-section {
  margin-top: -7.5rem;
  padding-top: 7.5rem;
}

/* Logo Container */
.logo-wrapper {
  position: relative;
  height: 60px;
  width: 120px;
  margin-right: 2rem;
  overflow: visible;
}

/* Top bar styles */
.top-bar-dropdown {
  @apply absolute top-full left-0 mt-1 bg-white rounded-lg shadow-lg py-2 w-48 z-50;
  animation: dropdownFade 0.2s ease-out;
}

.top-bar-dropdown button {
  @apply w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 hover:text-[#074799] flex items-center justify-between;
  transition: all 0.2s ease;
}

.top-bar-dropdown button .indicator {
  @apply w-2 h-2 rounded-full bg-[#009990];
  transition: all 0.2s ease;
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: var(--delay, 0ms);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Ensure dropdowns are above other content */
.z-dropdown {
  z-index: 1000;
}

/* Mobile Navigation Styles */
@media (max-width: 1023px) {
  .mobile-nav-enter {
    opacity: 0;
    transform: translateY(-10px);
  }

  .mobile-nav-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 200ms, transform 200ms;
  }

  .mobile-nav-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .mobile-nav-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 200ms, transform 200ms;
  }

  /* Mobile dropdown animations */
  .mobile-dropdown-enter {
    max-height: 0;
    opacity: 0;
  }

  .mobile-dropdown-enter-active {
    max-height: 1000px;
    opacity: 1;
    transition: max-height 300ms ease-in-out, opacity 200ms ease-in-out;
  }

  .mobile-dropdown-exit {
    max-height: 1000px;
    opacity: 1;
  }

  .mobile-dropdown-exit-active {
    max-height: 0;
    opacity: 0;
    transition: max-height 300ms ease-in-out, opacity 200ms ease-in-out;
  }
}

/* Improved mobile menu styles */
.mobile-menu {
  @apply fixed inset-0 bg-white z-[101] overflow-y-auto;
  top: 120px; /* Height of header */
}

.mobile-menu-item {
  @apply py-4 text-lg text-gray-700 font-medium transition-colors;
}

.mobile-menu-item:hover {
  @apply text-[#074799];
}

.mobile-submenu {
  @apply bg-gray-50 rounded-lg my-2 px-4;
}

.mobile-submenu-item {
  @apply py-3 text-gray-700 transition-colors;
}

.mobile-submenu-item:hover {
  @apply text-[#074799];
}
