'use client'

import { ReactNode } from 'react'
import { SearchParamsProvider } from '@/components/utils/SearchParamsProvider'
import ErrorBoundary from '@/components/utils/ErrorBoundary'
import { Suspense } from 'react'

export function Providers({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary>
      <SearchParamsProvider>
        <Suspense fallback={<div>Loading application...</div>}>
          {children}
        </Suspense>
      </SearchParamsProvider>
    </ErrorBoundary>
  )
}
