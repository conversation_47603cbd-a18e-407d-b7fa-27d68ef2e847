import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Preschool Blog and Tips for Parents | Tulip Kids',
  description: 'Explore our blog for insights on early childhood education, fun activities, and parenting tips to nurture your child\'s growth and learning.',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/blog'
  },
  openGraph: {
    title: 'Preschool Blog and Tips for Parents | Tulip Kids',
    description: 'Explore our blog for insights on early childhood education, fun activities, and parenting tips to nurture your child\'s growth and learning.',
    url: 'https://www.tulipkidsinc.com/blog',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Preschool Blog and Tips for Parents | Tulip Kids',
    description: 'Explore our blog for insights on early childhood education, fun activities, and parenting tips to nurture your child\'s growth and learning.'
  }
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
