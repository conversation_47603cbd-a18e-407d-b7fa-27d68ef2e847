import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Preschool Blog & Parenting Tips | Tulip Kids',
  description: 'Explore Tulip Kids\' blog for expert tips on early childhood education, fun learning activities, and parenting advice to support your child\'s growth and development.',
  keywords: ['preschool blog', 'parenting tips', 'early childhood education', 'child development', 'learning activities', 'Tulip Kids blog'],
  openGraph: {
    title: 'Preschool Blog & Parenting Tips | Tulip Kids',
    description: 'Explore Tulip Kids\' blog for expert tips on early childhood education, fun learning activities, and parenting advice to support your child\'s growth and development.',
    url: 'https://www.tulipkidsinc.com/blog',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

export const keywords = ['preschool blog', 'parenting tips', 'early childhood education', 'child development', 'learning activities', 'Tulip Kids blog'];
