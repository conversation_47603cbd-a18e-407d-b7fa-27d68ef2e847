import BlogList from '@/components/blog/BlogList'

export default function BlogPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* Header Section with Pattern Background */}
      <div className="relative bg-[#001A6E] mt-[88px] pt-16 pb-16 px-4">
        <div 
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
        <div className="container mx-auto max-w-4xl relative flex flex-col items-center">
          <div className="bg-[#074799] px-8 py-3 rounded-lg mb-6 w-fit">
            <h1 className="text-4xl md:text-5xl font-bold text-white text-center">
              Our Blog
            </h1>
          </div>
          <div className="bg-[#074799]/80 px-6 py-4 rounded-lg max-w-2xl w-full">
            <p className="text-lg md:text-xl text-white text-center">
              Discover insights about early childhood education, parenting tips, and activities 
              to help your child grow and learn.
            </p>
          </div>
        </div>
      </div>

      {/* Blog Posts Section */}
      <div className="container mx-auto px-4 py-12">
        <BlogList />
      </div>
    </main>
  )
}
