import { NextResponse } from 'next/server';
import { sendEmail } from '@/utils/emailService';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    await sendEmail({
      subject: 'New Newsletter Subscription',
      senderName: 'Tulip Kids Website',
      senderEmail: '<EMAIL>',
      to: [{
        email: '<EMAIL>',
        name: 'Tulip Kids Admin'
      }],
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #001A6E; color: white; padding: 20px; text-align: center;">
            <h1>New Newsletter Subscription</h1>
          </div>
          <div style="padding: 20px;">
            <p>You have received a new newsletter subscription request.</p>
            <p><strong>Subscriber Email:</strong> ${email}</p>
          </div>
        </div>
      `
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { error: 'Failed to process subscription' },
      { status: 500 }
    );
  }
}
