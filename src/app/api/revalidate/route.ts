import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import { CACHE_TAGS } from '@/lib/cache';

// Secret token for secure revalidation
const REVALIDATE_TOKEN = process.env.REVALIDATE_TOKEN;

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const tag = searchParams.get('tag');

    // Validate token
    if (!token || token !== REVALIDATE_TOKEN) {
      return NextResponse.json(
        { message: 'Invalid token' },
        { status: 401 }
      );
    }

    // Validate tag
    if (!tag || !Object.values(CACHE_TAGS).includes(tag as (typeof CACHE_TAGS)[keyof typeof CACHE_TAGS])) {
      return NextResponse.json(
        { message: 'Invalid tag' },
        { status: 400 }
      );
    }

    // Revalidate the tag
    revalidateTag(tag);

    return NextResponse.json({
      revalidated: true,
      message: `Cache for tag ${tag} revalidated successfully`,
    });
  } catch (error) {
    console.error('Error revalidating:', error);
    return NextResponse.json(
      { message: 'Error revalidating cache' },
      { status: 500 }
    );
  }
}
