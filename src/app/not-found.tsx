'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Home, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function NotFound() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="space-y-6">
          {/* Logo */}
          <div className="flex justify-center">
            <Image
              src="/images/logo.png"
              alt="Tulip Kids Logo"
              width={120}
              height={120}
              className="w-auto h-auto"
              priority
            />
          </div>

          {/* Error Message */}
          <div>
            <h1 className="text-9xl font-bold text-[#074799]">404</h1>
            <h2 className="mt-4 text-3xl font-extrabold text-gray-900">
              Page Not Found
            </h2>
            <p className="mt-2 text-lg text-gray-600">
              Sorry, we couldn't find the page you're looking for.
            </p>
          </div>

          {/* Helpful Links */}
          <div className="mt-6">
            <p className="text-base text-gray-500">
              Here are some helpful links instead:
            </p>
            <div className="mt-4 flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#074799] hover:bg-[#009990] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#074799] transition-colors duration-200"
              >
                <Home className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
              <button
                onClick={() => router.back()}
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#074799] transition-colors duration-200"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Go Back
              </button>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="pt-6 border-t border-gray-200">
          <p className="text-base text-gray-500">
            Need help? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-[#074799] hover:text-[#009990] font-medium"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
