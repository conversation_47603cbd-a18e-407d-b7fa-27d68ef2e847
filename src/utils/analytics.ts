export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA4_MEASUREMENT_ID || 'G-MC8BXC5CXB'
export const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID || 'GTM-M4BD655F'
export const GOOGLE_ADS_ID = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID || 'AW-16615339344'

type GTagEvent = {
  action: string;
  category: string;
  label: string;
  value?: number;
};

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url: string) => {
  if (typeof window !== 'undefined') {
    // Push to dataLayer for GTM
    if (window.dataLayer) {
      window.dataLayer.push({
        event: 'pageview',
        page: url,
      });
    }

    // Also update GA4 directly
    if (window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: url,
      });
    }
  }
};

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = ({ action, category, label, value }: GTagEvent) => {
  if (typeof window !== 'undefined') {
    // Push to dataLayer for GTM
    if (window.dataLayer) {
      window.dataLayer.push({
        event: action,
        eventCategory: category,
        eventLabel: label,
        eventValue: value,
      });
    }

    // Also track with GA4 directly
    if (window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value,
      });
    }
  }
};

// Simplified event tracking function
export const trackEvent = (eventName: string, eventProps: Record<string, any> = {}) => {
  if (typeof window !== 'undefined') {
    // Push to dataLayer for GTM
    if (window.dataLayer) {
      window.dataLayer.push({
        event: eventName,
        ...eventProps,
      });
    }

    // Also track with GA4 directly
    if (window.gtag) {
      window.gtag('event', eventName, eventProps);
    }
  }
};

// Google Ads conversion tracking
export type GoogleAdsPageViewParams = {
  value?: string;
  items?: Array<{
    id?: string;
    location_id?: string;
    google_business_vertical: 'education';
  }>;
};

// Track Google Ads page view conversion
export const trackGoogleAdsPageView = (params: GoogleAdsPageViewParams = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'page_view', {
      send_to: GOOGLE_ADS_ID,
      ...params
    });
  }
};

// Track Google Ads contact form conversion
export const trackContactFormConversion = (value: number = 1.0) => {
  if (typeof window !== 'undefined' && window.gtag) {
    // Log the conversion attempt for debugging
    console.log('Tracking Google Ads conversion with ID:', GOOGLE_ADS_ID);

    // Standard conversion tracking
    window.gtag('event', 'conversion', {
      send_to: `${GOOGLE_ADS_ID}/contact`,
      value: value,
      currency: 'USD',
      transaction_id: ''
    });

    // Also track as a regular event for redundancy
    window.gtag('event', 'form_submission', {
      event_category: 'Contact',
      event_label: 'Contact Form Submission',
      value: value
    });
  } else {
    console.warn('Google Ads tracking unavailable - gtag not found');
  }
};
