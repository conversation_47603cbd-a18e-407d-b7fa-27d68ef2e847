// Initialize Brevo API client
const apiKey = process.env.BREVO_API_KEY;

export interface EmailCampaignProps {
  name?: string;
  subject: string;
  senderName: string;
  senderEmail: string;
  htmlContent: string;
  to: Array<{ email: string; name?: string }>;
}

export const sendEmail = async ({
  subject,
  senderName,
  senderEmail,
  htmlContent,
  to,
}: EmailCampaignProps) => {
  try {
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': apiKey || '',
      },
      body: JSON.stringify({
        sender: {
          name: senderName,
          email: senderEmail,
        },
        to,
        subject,
        htmlContent,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send email');
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

// Example usage:
/*
await sendEmail({
  subject: "My subject",
  senderName: "From name",
  senderEmail: "<EMAIL>",
  htmlContent: "Congratulations! You successfully sent this example campaign via the Brevo API.",
  to: [{ email: "<EMAIL>", name: "Recipient Name" }]
});
*/
