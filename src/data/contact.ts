import { getAllLocations } from './locationMappings';

interface ContactLocation {
  value: string;
  label: string;
}

interface CountryInfo {
  phone: string;
  email: string;
  hours: string;
  locations: ContactLocation[];
}

export const contactInfo: Record<'usa' | 'india', CountryInfo> = {
  usa: {
    phone: '(*************',
    email: '<EMAIL>',
    hours: 'Mon-Fri: 7:00 AM - 6:00 PM',
    locations: getAllLocations('usa').map(loc => ({
      value: loc.shortName,
      label: loc.label
    }))
  },
  india: {
    phone: '91 7314999788',
    email: '<EMAIL>',
    hours: 'Mon-Sat: 9:00 AM - 6:00 PM',
    locations: getAllLocations('india').map(loc => ({
      value: loc.shortName,
      label: loc.label
    }))
  }
};
