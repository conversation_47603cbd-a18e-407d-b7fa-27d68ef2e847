export const contactInfo = {
  usa: {
    phone: '+****************',
    email: '<EMAIL>'
  },
  india: {
    phone: '+91 9575545200',
    email: '<EMAIL>'
  }
}

export interface SubNavigationItem {
  title: string;
  path: string;
  subText?: string;
  children?: SubNavigationItem[];
}

export interface NavigationItem {
  title: string;
  path: string;
  children?: SubNavigationItem[];
}

export const navigationItems: NavigationItem[] = [
  { title: 'Home', path: '/' },

  {
    title: 'Locations',
    path: '/locations',
    children: [
      {
        title: 'USA Locations',
        path: '/locations/usa',
        subText: 'United States Centers',
        children: [
          {
            title: 'Sunnyvale - Willow Ave',
            path: '/locations/usa/willow-ave',
            subText: 'California'
          },
          {
            title: 'Sunnyvale - After School',
            path: '/locations/usa/willow-ave-after',
            subText: 'California'
          },
          {
            title: 'Sunnyvale - Lawrence Station',
            path: '/locations/usa/lawrence-station',
            subText: 'California'
          },
          {
            title: 'Santa Clara - El Camino Real',
            path: '/locations/usa/el-camino',
            subText: 'California'
          },
          {
            title: 'San Jose - Cottle Road',
            path: '/locations/usa/cottle-road',
            subText: 'California'
          },
          {
            title: 'Dublin - After School',
            path: '/locations/usa/grafton-st-after',
            subText: 'California'
          },
          {
            title: 'Mountain House',
            path: '/locations/usa/mountain-house',
            subText: 'California'
          }
        ]
      },
      {
        title: 'India Locations',
        path: '/locations/india',
        subText: 'India Centers',
        children: [
          {
            title: 'Indore - Bima Nagar',
            path: '/locations/india/bima-nagar',
            subText: 'Madhya Pradesh'
          },
          {
            title: 'Indore - Nipania',
            path: '/locations/india/nipania',
            subText: 'Madhya Pradesh'
          }
        ]
      }
    ]
  },
  {
    title: 'Programs',
    path: '/programs',
    children: [
      {
        title: 'USA Programs',
        path: '/programs/usa',
        subText: 'Programs in United States',
        children: [
          {
            title: 'Preschool Programs',
            path: '/programs/usa/preschool',
            subText: 'Ages 12 months - 5 years'
          },
          {
            title: 'After School Programs',
            path: '/programs/usa/after-school',
            subText: 'Ages 5-12 years'
          }
        ]
      },
      {
        title: 'India Programs',
        path: '/programs/india',
        subText: 'Programs in India',
        children: [
          {
            title: 'Pre-School',
            path: '/programs/india/preschool',
            subText: 'Ages 2 - 3.5 years'
          }
        ]
      },
      {
        title: 'Teacher Training',
        path: '/programs/teachers-training',
        subText: 'Professional Development'
      }
    ]
  },


  {
    title: 'About',
    path: '/about',
    children: [
      {
        title: 'Vision & Mission',
        path: '/vision',
        subText: 'Our Purpose and Goals'
      },
      {
        title: 'Our Team',
        path: '/about/team',
        subText: 'Meet Our Leadership'
      },
      {
        title: 'Careers',
        path: '/careers',
        subText: 'Join our team'
      }
    ]
  },
  { title: 'Blog', path: '/blog' },
  { title: 'Contact', path: '/contact' }
]