interface EnrollmentLink {
  location: string;
  url: string;
  programType: 'preschool' | 'afterSchool' | 'summerCamp';
  slug: string; // URL-friendly location name
  displayName: string; // Human-readable location name
}

export const enrollmentLinks: EnrollmentLink[] = [
  // Pre School Programs
  {
    location: 'willow',
    displayName: 'Willow Ave',
    slug: 'willow',
    url: 'https://schools.procareconnect.com/register/tulip-kids-willow-ave-sunnyvale-94086',
    programType: 'preschool'
  },
  {
    location: 'lawrence-station',
    displayName: 'Lawrence Station',
    slug: 'lawrence-station',
    url: 'https://schools.procareconnect.com/register/tulip-kids-ls-sunnyvale-94086',
    programType: 'preschool'
  },
  {
    location: 'cottle',
    displayName: 'Cottle Rd',
    slug: 'cottle',
    url: 'https://schools.procareconnect.com/register/673683fa-25cf-4328-a931-4d6c5d4ebfad',
    programType: 'preschool'
  },
  {
    location: 'el-camino',
    displayName: 'El Camino Real',
    slug: 'el-camino',
    url: 'https://schools.procareconnect.com/register/2596080c-cc3c-43bc-8d2b-3835b893d805',
    programType: 'preschool'
  },
  {
    location: 'mountain-house',
    displayName: 'Mountain House',
    slug: 'mountain-house',
    url: 'https://schools.procareconnect.com/register/3ea394f5-2b40-4150-85d1-2ce734c856ae',
    programType: 'preschool'
  },

  // After School Programs
  {
    location: 'grafton-st-after',
    displayName: 'Grafton St',
    slug: 'grafton-st-after',
    url: '/contact',
    programType: 'afterSchool'
  },
  {
    location: 'willow',
    displayName: 'Willow Ave',
    slug: 'willow-after',
    url: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c',
    programType: 'afterSchool'
  },
  {
    location: 'willow',
    displayName: 'Willow Ave',
    slug: 'willow-after-school',
    url: 'https://schools.procareconnect.com/register/tulip-kids-willow-sunnyvale-94086',
    programType: 'afterSchool'
  },

  // Summer Camps
  {
    location: 'all',
    displayName: 'All Locations',
    slug: 'summer-camps',
    url: 'https://tulip-after-school-dublin.jumbula.com/',
    programType: 'summerCamp'
  }
];

// Helper function to get enrollment link by location slug and program type
export function getEnrollmentLink(locationSlug: string, programType: 'preschool' | 'afterSchool' | 'summerCamp'): string | null {
  const link = enrollmentLinks.find(
    link => link.slug === locationSlug && link.programType === programType
  );
  return link ? link.url : null;
}

// Helper function to get enrollment link by location display name and program type
export function getEnrollmentLinkByDisplayName(displayName: string | null | undefined, programType: 'preschool' | 'afterSchool' | 'summerCamp'): string | null {
  if (!displayName) return null;
  
  const link = enrollmentLinks.find(
    link => link.displayName.toLowerCase() === displayName.toLowerCase() && link.programType === programType
  );
  return link ? link.url : null;
}

// Helper function to get all locations for a specific program type
export function getLocationsByProgramType(programType: 'preschool' | 'afterSchool' | 'summerCamp'): { slug: string; displayName: string }[] {
  return enrollmentLinks
    .filter(link => link.programType === programType)
    .map(({ location, displayName, slug }) => ({ slug, displayName }));
}

// Helper function to check if a location has a specific program type
export function hasProgram(locationSlug: string, programType: 'preschool' | 'afterSchool' | 'summerCamp'): boolean {
  return enrollmentLinks.some(
    link => link.location === locationSlug && link.programType === programType
  );
}

// Helper function to get location details by slug
export function getLocationBySlug(slug: string): { displayName: string; programs: ('preschool' | 'afterSchool' | 'summerCamp')[] } | null {
  const locationLinks = enrollmentLinks.filter(link => link.location === slug || link.slug === slug);
  if (locationLinks.length === 0) return null;

  return {
    displayName: locationLinks[0].displayName,
    programs: locationLinks.map(link => link.programType)
  };
}

// Helper function to get all enrollment links for a location by slug
export function getAllProgramsForLocation(locationSlug: string): EnrollmentLink[] {
  return enrollmentLinks.filter(link => link.location === locationSlug || link.slug === locationSlug);
}
