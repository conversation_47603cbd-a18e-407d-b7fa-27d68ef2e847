import { locations } from '@/constants/locations'

export type ProgramType = 'preschool' | 'after-school' | 'both'
export type Country = 'usa' | 'india'

export interface LocationProgram {
  id: string
  name: string
  type: ProgramType
  country: Country
  programs: string[]
}

// US Programs
const usPrograms = ['Pre School', 'After School', 'Summer Camp']

// India Programs
const indiaPrograms = ['Pre School', 'Activity Classes']

// Map location IDs to their available programs
export const locationPrograms: Record<string, LocationProgram> = {
  'willow-ave': {
    id: 'willow-ave',
    name: 'Sunnyvale - Willow Ave',
    type: 'both',
    country: 'usa',
    programs: ['Pre School', 'After School', 'Summer Camp']
  },
  'lawrence-station': {
    id: 'lawrence-station',
    name: 'Sunnyvale - Lawrence Station Road',
    type: 'preschool',
    country: 'usa',
    programs: ['Pre School']
  },
  'el-camino': {
    id: 'el-camino',
    name: 'Santa Clara - El Camino Real',
    type: 'preschool',
    country: 'usa',
    programs: ['Pre School']
  },
  'cottle-road': {
    id: 'cottle-road',
    name: 'San Jose - Cottle Road',
    type: 'preschool',
    country: 'usa',
    programs: ['Pre School']
  },
  'grafton-st-after': {
    id: 'grafton-st-after',
    name: 'Dublin - Grafton Street',
    type: 'after-school',
    country: 'usa',
    programs: ['After School', 'Summer Camp']
  },
  'bima-nagar': {
    id: 'bima-nagar',
    name: 'Indore - Bima Nagar',
    type: 'both',
    country: 'india',
    programs: ['Pre School', 'Activity Classes']
  },
  'nipania': {
    id: 'nipania',
    name: 'Indore - Nipania',
    type: 'both',
    country: 'india',
    programs: ['Pre School', 'Activity Classes']
  }
}

// Helper function to get available programs for a location
export function getAvailableProgramsForLocation(locationId: string): string[] {
  return locationPrograms[locationId]?.programs || []
}

// Helper function to get programs by country
export function getProgramsByCountry(country: Country): string[] {
  return country === 'usa' ? usPrograms : indiaPrograms
}

// Helper function to get locations by country
export function getLocationsByCountry(country: Country): LocationProgram[] {
  return Object.values(locationPrograms).filter(location => location.country === country)
}

// Helper function to get location names by country
export function getLocationNamesByCountry(country: Country): { id: string; name: string }[] {
  return Object.values(locationPrograms)
    .filter(location => location.country === country)
    .map(location => ({
      id: location.id,
      name: location.name
    }))
}
