export interface Location {
  name: string;
  badge: string;
  heading: string;
  description: string;
  heroImage: string;
  heroImageAlt: string;
  contact: {
    phone: string;
    email: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  features: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
  facilities: Array<{
    name: string;
    description: string;
    icon: any;
  }>;
  accessibility: {
    features: Array<string>;
    additionalInfo: string;
  };
  safety: Array<{
    title: string;
    description: string;
  }>;
  community: Array<{
    title: string;
    description: string;
    image?: string;
  }>;
  faq: Array<{
    question: string;
    answer: string;
  }>;
}
