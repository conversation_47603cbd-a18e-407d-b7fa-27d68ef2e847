'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { pageview } from '@/utils/analytics'

function PageViewTrackerClient() {
  const pathname = usePathname()

  useEffect(() => {
    if (pathname) {
      // Just track the pathname for now to avoid searchParams issues
      pageview(pathname)
    }
  }, [pathname])

  // This component doesn't render anything
  return null
}

export default function PageViewTracker() {
  // This component is just a wrapper that doesn't use any client hooks directly
  return <PageViewTrackerClient />
}
