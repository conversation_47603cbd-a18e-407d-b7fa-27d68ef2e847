'use client'

import { useEffect } from 'react'
import { trackContactFormConversion } from '@/utils/analytics'

export default function ContactFormConversionTracker() {
  useEffect(() => {
    // Track the contact form conversion
    trackContactFormConversion(1.0)

    // Log for debugging
    console.log('Contact form conversion tracked')
  }, [])

  // This component doesn't render anything
  return null
}
