'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { trackGoogleAdsPageView } from '@/utils/analytics'

interface LocationPageTrackerProps {
  locationId: string;
  locationName: string;
}

export default function LocationPageTracker({
  locationId,
  locationName
}: LocationPageTrackerProps) {
  const pathname = usePathname()

  useEffect(() => {
    // Track the location page view with specific location data
    trackGoogleAdsPageView({
      value: locationName,
      items: [{
        id: locationId,
        location_id: locationId,
        google_business_vertical: 'education'
      }]
    })
  }, [pathname, locationId, locationName])

  // This component doesn't render anything
  return null
}
