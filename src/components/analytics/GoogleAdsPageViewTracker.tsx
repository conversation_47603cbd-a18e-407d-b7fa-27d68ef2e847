'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { trackGoogleAdsPageView, GoogleAdsPageViewParams } from '@/utils/analytics'

interface GoogleAdsPageViewTrackerProps {
  value?: string;
  items?: Array<{
    id?: string;
    location_id?: string;
    google_business_vertical: 'education';
  }>;
  paths?: string[]; // Optional paths to track, if not provided will track all pages
}

export default function GoogleAdsPageViewTracker({
  value,
  items = [{ google_business_vertical: 'education' }],
  paths
}: GoogleAdsPageViewTrackerProps) {
  const pathname = usePathname()

  useEffect(() => {
    // If paths is provided, only track if current path is in the list
    if (paths && !paths.includes(pathname)) {
      return
    }

    const params: GoogleAdsPageViewParams = {
      value,
      items
    }

    // Track the page view
    trackGoogleAdsPageView(params)
  }, [pathname, value, items, paths])

  // This component doesn't render anything
  return null
}
