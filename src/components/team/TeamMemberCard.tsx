'use client';

import Image from 'next/image';
import { useState } from 'react';
import { TeamMember } from '@/types/team';
import { Mail, Phone, Linkedin, ChevronDown, ChevronUp, GraduationCap, Award } from 'lucide-react';

interface TeamMemberCardProps {
  member: TeamMember;
  variant?: 'compact' | 'full';
  className?: string;
}

export default function TeamMemberCard({ member, variant = 'compact', className = '' }: TeamMemberCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={`bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 ${className}`}>
      <div className="relative">
        {/* Image Container */}
        <div className="aspect-[3/4] relative overflow-hidden rounded-t-2xl">
          <Image
            src={member.image}
            alt={member.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        
        {/* Gradient Overlay */}
        <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-[#001A6E]/90 to-transparent" />
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-2xl font-bold text-[#001A6E] mb-1">{member.name}</h3>
          <p className="text-[#009990] font-medium">{member.role}</p>
        </div>

        {variant === 'compact' ? (
          <p className="text-gray-600 line-clamp-3">{member.bio}</p>
        ) : (
          <div className="space-y-6">
            <p className="text-gray-600">{member.bio}</p>

            {/* Contact Information */}
            {member.contact && (
              <div className="space-y-3">
                {member.contact.email && (
                  <div className="flex items-center space-x-3 text-gray-600">
                    <Mail className="w-5 h-5 text-[#009990]" />
                    <a href={`mailto:${member.contact.email}`} className="hover:text-[#009990]">
                      {member.contact.email}
                    </a>
                  </div>
                )}
                {member.contact.phone && (
                  <div className="flex items-center space-x-3 text-gray-600">
                    <Phone className="w-5 h-5 text-[#009990]" />
                    <a href={`tel:${member.contact.phone}`} className="hover:text-[#009990]">
                      {member.contact.phone}
                    </a>
                  </div>
                )}
                {member.contact.linkedin && (
                  <div className="flex items-center space-x-3 text-gray-600">
                    <Linkedin className="w-5 h-5 text-[#009990]" />
                    <a href={member.contact.linkedin} target="_blank" rel="noopener noreferrer" className="hover:text-[#009990]">
                      LinkedIn Profile
                    </a>
                  </div>
                )}
              </div>
            )}

            {/* Expandable Section */}
            <div className="pt-4">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center space-x-2 text-[#009990] hover:text-[#001A6E] transition-colors duration-200"
              >
                <span>View {isExpanded ? 'Less' : 'More'}</span>
                {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </button>

              {isExpanded && (
                <div className="mt-6 space-y-6">
                  {/* Education */}
                  {member.education && member.education.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold text-[#001A6E] mb-3 flex items-center">
                        <GraduationCap className="w-5 h-5 mr-2" />
                        Education
                      </h4>
                      <ul className="space-y-2">
                        {member.education.map((edu, index) => (
                          <li key={index} className="text-gray-600">
                            <span className="font-medium">{edu.degree}</span>
                            <br />
                            {edu.institution}, {edu.year}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Certifications */}
                  {member.certifications && member.certifications.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold text-[#001A6E] mb-3 flex items-center">
                        <Award className="w-5 h-5 mr-2" />
                        Certifications
                      </h4>
                      <ul className="list-disc list-inside space-y-1">
                        {member.certifications.map((cert, index) => (
                          <li key={index} className="text-gray-600">{cert}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Specialties */}
                  {member.specialties && member.specialties.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold text-[#001A6E] mb-3">Specialties</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.specialties.map((specialty, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-[#E6FFF9] text-[#009990] rounded-full text-sm"
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
