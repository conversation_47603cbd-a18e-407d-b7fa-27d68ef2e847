'use client';

import { TeamMember } from '@/types/team';
import TeamMemberCard from './TeamMemberCard';

interface TeamGridProps {
  members: TeamMember[];
  variant?: 'compact' | 'full';
  columns?: 2 | 3 | 4;
  className?: string;
}

export default function TeamGrid({ 
  members, 
  variant = 'compact', 
  columns = 3,
  className = '' 
}: TeamGridProps) {
  const gridCols = {
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-2 lg:grid-cols-3',
    4: 'md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid grid-cols-1 ${gridCols[columns]} gap-8 ${className}`}>
      {members.map((member) => (
        <TeamMemberCard
          key={member.id}
          member={member}
          variant={variant}
        />
      ))}
    </div>
  );
}
