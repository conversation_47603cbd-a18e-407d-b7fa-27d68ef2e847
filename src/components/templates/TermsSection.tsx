'use client';

import { FileText, Scale, Clock, Users, AlertCircle, CreditCard, School2, GraduationCap, Sparkles } from 'lucide-react';

interface PolicySectionProps {
  title: string;
  content: string[];
  icon: React.ReactNode;
}

function PolicySection({ title, content, icon }: PolicySectionProps) {
  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="flex items-center gap-4 mb-6">
        <div className="p-3 rounded-xl bg-[#009990]/10">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      <div className="space-y-4">
        {content.map((paragraph, index) => (
          <p key={index} className="text-gray-600 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
}

function RefundPolicySection() {
  const refundPolicies = [
    {
      title: "Preschool Program",
      icon: <School2 className="w-6 h-6 text-blue-600" />,
      items: [
        "The registration fee is non-refundable.",
        "One month notice is required for discontinuing your child at Tulip childcare/preschool program"
      ]
    },
    {
      title: "After School Program",
      icon: <GraduationCap className="w-6 h-6 text-purple-600" />,
      items: [
        "The registration fee is non-refundable.",
        "One months' notice is required for discontinuing your child at Tulip after school program"
      ]
    },
    {
      title: "Summer Camps & Enrichment",
      icon: <Sparkles className="w-6 h-6 text-teal-600" />,
      items: [
        "100% refund if cancelled 3 weeks before the camp /class starts",
        "50% refund if cancelled 2 weeks before the camp/class starts",
        "No refund in other cases like absence / no-show.",
        "Parents will be notified if a camp/class will be cancelled due to lack of minimum enrollment needed and a choice will be given to move the child to other camp of choice (if seats are available in that choice class) or refund the camp fee."
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Refund & Cancellation Policy</h2>
      {refundPolicies.map((policy, index) => (
        <div key={index} className={`bg-white rounded-2xl p-8 shadow-sm border-l-4 border-${policy.icon.props.className.split(' ')[3]}`}>
          <div className="flex items-center gap-4 mb-6">
            <div className={`p-3 rounded-xl bg-${policy.icon.props.className.split(' ')[3]}/10`}>
              {policy.icon}
            </div>
            <h3 className="text-xl font-semibold">{policy.title}</h3>
          </div>
          <ul className="space-y-4">
            {policy.items.map((item, idx) => (
              <li key={idx} className="flex items-start gap-3">
                <span className={`w-1.5 h-1.5 rounded-full mt-2 bg-${policy.icon.props.className.split(' ')[3]}`}></span>
                <span className="text-gray-600 leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
}

export default function TermsSection() {
  const terms = [
    {
      title: "Acceptance of Terms",
      icon: <Scale className="w-6 h-6 text-[#009990]" />,
      content: [
        "By enrolling your child in any Tulip Kids program, you agree to be bound by these Terms and Conditions.",
        "These terms constitute a legally binding agreement between you and Tulip Kids regarding your use of our services.",
        "We reserve the right to modify these terms at any time, with notice provided to parents/guardians."
      ]
    },
    {
      title: "Program Hours and Attendance",
      icon: <Clock className="w-6 h-6 text-[#009990]" />,
      content: [
        "Our programs operate during specified hours, which vary by location and program type.",
        "Parents/guardians must adhere to the designated drop-off and pick-up times.",
        "Late pick-ups may result in additional fees as specified in your enrollment agreement."
      ]
    },
    {
      title: "Health and Safety",
      icon: <AlertCircle className="w-6 h-6 text-[#009990]" />,
      content: [
        "Children must be fully immunized according to state requirements.",
        "Parents must keep sick children at home to prevent the spread of illness.",
        "We maintain the right to send children home if they show signs of illness.",
        "Emergency medical treatment will be sought if necessary, with costs being the responsibility of the parent/guardian."
      ]
    },
    {
      title: "Enrollment and Registration",
      icon: <FileText className="w-6 h-6 text-[#009990]" />,
      content: [
        "Enrollment is subject to space availability and completion of all required documentation.",
        "Registration fees are non-refundable and must be paid to secure your child's spot.",
        "All required forms must be completed and submitted before the child's first day."
      ]
    },
    {
      title: "Payment Terms",
      icon: <CreditCard className="w-6 h-6 text-[#009990]" />,
      content: [
        "Tuition is due on the first of each month.",
        "Late payments may incur additional fees.",
        "We accept various payment methods including credit cards and bank transfers.",
        "Repeated late payments may result in suspension of services."
      ]
    },
    {
      title: "Behavior and Conduct",
      icon: <Users className="w-6 h-6 text-[#009990]" />,
      content: [
        "We maintain a zero-tolerance policy for bullying and aggressive behavior.",
        "Parents must work cooperatively with staff to address behavioral concerns.",
        "Tulip Kids reserves the right to terminate enrollment if a child's behavior poses a risk to others.",
        "Parents are expected to maintain professional conduct when interacting with staff and other families."
      ]
    }
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            {terms.map((term, index) => (
              <PolicySection 
                key={index}
                title={term.title}
                content={term.content}
                icon={term.icon}
              />
            ))}
          </div>

          {/* Refund Policy Section */}
          <div className="mt-16">
            <RefundPolicySection />
          </div>

          {/* Additional Information */}
          <div className="mt-12 p-6 bg-gray-50 rounded-xl">
            <h4 className="text-lg font-semibold mb-4 text-gray-900">Contact Information</h4>
            <p className="text-gray-600 mb-4">
              For any questions about these terms and conditions, please contact us:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
              <li>Address: 1159, Willow Ave Sunnyvale
              </li>
            </ul>
            <p className="mt-6 text-sm text-gray-500">
              Last updated: January 2025
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
