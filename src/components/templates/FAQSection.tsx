'use client';

import { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

interface FAQProps {
  faq: Array<{
    question: string;
    answer: string;
  }>;
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex justify-between items-center w-full py-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg font-semibold">{question}</span>
        {isOpen ? (
          <Minus className="w-5 h-5 text-[#009990]" />
        ) : (
          <Plus className="w-5 h-5 text-[#009990]" />
        )}
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function FAQSection({ faq }: FAQProps) {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-8">
        <div className="max-w-3xl mx-auto">
          {faq.map((item, index) => (
            <FAQItem key={index} question={item.question} answer={item.answer} />
          ))}
        </div>
      </div>
    </section>
  );
}
