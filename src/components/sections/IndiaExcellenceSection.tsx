'use client';

import { Award, GraduationCap, Users, Building, Shield, School2 } from 'lucide-react';

export default function IndiaExcellenceSection() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <span className="text-[#009990] font-medium text-sm uppercase tracking-wider mb-4 block">Why Choose Us</span>
          <h2 className="text-4xl font-bold mb-6">Excellence in Early Education</h2>
          <p className="text-lg text-gray-600">
            Experience a comprehensive approach to early childhood education where every detail 
            is designed to nurture your child's growth and development.
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <Award className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Quality Standards</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              Following international best practices in early childhood education and care.
            </p>
          </div>

          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <GraduationCap className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Specialized Programs</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              Age-appropriate curriculum designed to foster cognitive, social, and emotional growth.
            </p>
          </div>

          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <Users className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Expert Educators</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              Experienced and qualified teachers dedicated to nurturing young minds.
            </p>
          </div>

          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <Building className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Modern Facilities</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              State-of-the-art infrastructure designed for optimal learning and development.
            </p>
          </div>

          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <Shield className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Safety First</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              Comprehensive safety measures and protocols to ensure your child's wellbeing.
            </p>
          </div>

          <div className="group hover:bg-[#009990] bg-white rounded-2xl shadow-lg p-8 transition-all duration-300">
            <div className="mb-6">
              <div className="w-14 h-14 bg-[#009990]/10 group-hover:bg-white/10 rounded-xl flex items-center justify-center transition-all duration-300">
                <School2 className="w-7 h-7 text-[#009990] group-hover:text-white" />
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 group-hover:text-white">Holistic Development</h3>
            <p className="text-gray-600 group-hover:text-white/90">
              Focus on academic, physical, social, and emotional development of each child.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
