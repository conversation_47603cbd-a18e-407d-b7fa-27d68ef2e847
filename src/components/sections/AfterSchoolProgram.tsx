'use client';

import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Palette, 
  Music2, 
  Gamepad2,
  Trophy,
  Heart,
  Puzzle,
  Apple,
  Users,
  LucideIcon,
  GraduationCap,
  Clock,
  Bus
} from 'lucide-react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface Activity {
  icon: LucideIcon;
  title: string;
  description: string;
}

const AfterSchoolProgram = () => {
  const programs: Activity[] = [
    { 
      icon: BookOpen, 
      title: 'Homework Support',
      description: 'Dedicated time and assistance with daily homework assignments'
    },
    { 
      icon: Brain, 
      title: 'Academic Enrichment',
      description: 'Additional learning opportunities in math, science, and reading'
    },
    { 
      icon: Palette, 
      title: 'Arts & Crafts',
      description: 'Creative expression through various art mediums'
    },
    { 
      icon: Music2, 
      title: 'Music & Movement',
      description: 'Fun activities combining music, dance, and exercise'
    },
    { 
      icon: Gamepad2, 
      title: 'STEAM Activities',
      description: 'Engaging projects in Science, Technology, Engineering, Arts, and Math'
    },
    { 
      icon: Trophy, 
      title: 'Physical Activities',
      description: 'Sports and outdoor games for active play'
    }
  ];

  const features: Activity[] = [
    {
      icon: Clock,
      title: 'Flexible Hours',
      description: 'Care available from school dismissal until 6:30 PM'
    },
    {
      icon: Bus,
      title: 'Transportation',
      description: 'Safe transportation from local partner schools'
    },
    {
      icon: Apple,
      title: 'Healthy Snacks',
      description: 'Nutritious afternoon snacks provided daily'
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-white to-[#F8F9FA]">
      <div className="container mx-auto px-4 md:px-8">
        {/* Title Section */}
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h2 className="text-[42px] font-bold mb-6 text-[#001A6E] leading-tight">
            After School Program
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            A comprehensive after-school program designed to support academic success while fostering creativity, physical activity, and social development in a safe and engaging environment.
          </p>
        </div>

        {/* Programs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {programs.map((program, index) => {
            const Icon = program.icon;
            return (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="w-14 h-14 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-4">
                  <Icon className="w-7 h-7 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E] mb-2">{program.title}</h3>
                <p className="text-gray-600">{program.description}</p>
              </motion.div>
            );
          })}
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-3xl p-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div 
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-4"
                >
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                      <Icon className="w-6 h-6 text-[#009990]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#001A6E] mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Image Section */}
        <div className="mt-24 relative rounded-3xl overflow-hidden">
          <Image
            src="/images/after-school-program.jpg"
            alt="Children engaged in after-school activities"
            width={1200}
            height={600}
            className="object-cover w-full h-[400px] rounded-3xl"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/80 to-transparent flex items-center">
            <div className="text-white max-w-lg px-12">
              <h3 className="text-3xl font-bold mb-4">Join Our After School Program</h3>
              <p className="text-white/90 text-lg mb-6">
                Give your child the perfect balance of academic support and enriching activities in a safe, nurturing environment.
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
              >
                Schedule a Tour
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AfterSchoolProgram;
