'use client';

import { 
  <PERSON><PERSON><PERSON>, 
  Brain, 
  Paintbrush2, 
  Music2, 
  Globe2, 
  Atom,
  Trophy,
  Heart,
  Baby,
  Puzzle,
  Apple,
  Users,
  LucideIcon
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface Activity {
  icon: LucideIcon;
  title: string;
  description?: string;
}

const PreSchoolProgram = () => {
  const coreActivities: Activity[] = [
    { 
      icon: Baby, 
      title: 'Infant Care', 
      description: 'Nurturing care for babies 6 months to 12 months'
    },
    { 
      icon: Brain, 
      title: 'Early Learning',
      description: 'Developmentally appropriate activities for toddlers'
    },
    { 
      icon: Puzzle, 
      title: 'Preschool Program',
      description: 'Comprehensive curriculum for ages 3-5'
    },
    { 
      icon: BookOpen, 
      title: 'Pre-K Program',
      description: 'Kindergarten readiness preparation'
    },
    { 
      icon: Globe2, 
      title: 'Language Learning',
      description: 'Introduction to multiple languages'
    },
    { 
      icon: Music2, 
      title: 'Music & Movement',
      description: 'Creative expression through music and dance'
    },
    { 
      icon: Paintbrush2, 
      title: 'Arts & Crafts',
      description: 'Creative development and fine motor skills'
    },
    { 
      icon: Apple, 
      title: 'Healthy Meals',
      description: 'Nutritious meals and snacks provided'
    }
  ];

  const additionalFeatures: Activity[] = [
    { 
      icon: Users, 
      title: 'Small Class Sizes',
      description: 'Individual attention and care'
    },
    { 
      icon: Trophy, 
      title: 'Milestone Tracking',
      description: 'Regular progress updates'
    },
    { 
      icon: Heart, 
      title: 'Loving Environment',
      description: 'Warm and nurturing atmosphere'
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-white to-[#F8F9FA]">
      <div className="container mx-auto px-4 md:px-8">
        {/* Title Section */}
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h2 className="text-[42px] font-bold mb-6 text-[#001A6E] leading-tight">
            Our Preschool Program
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            At Tulip PreSchool, we provide a comprehensive early education program designed to nurture your child's cognitive, social, and emotional development in a safe and stimulating environment.
          </p>
        </div>

        {/* Core Activities Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {coreActivities.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div 
                key={index}
                className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="w-14 h-14 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-4">
                  <Icon className="w-7 h-7 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold text-[#001A6E] mb-2">{activity.title}</h3>
                <p className="text-gray-600">{activity.description}</p>
              </div>
            );
          })}
        </div>

        {/* Additional Features */}
        <div className="bg-white rounded-3xl p-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {additionalFeatures.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                      <Icon className="w-6 h-6 text-[#009990]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#001A6E] mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Image Section */}
        <div className="mt-24 relative rounded-3xl overflow-hidden">
          <Image
            src="/images/preschool-program.jpg"
            alt="Children engaged in learning activities"
            width={1200}
            height={600}
            className="object-cover w-full h-[400px] rounded-3xl"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/80 to-transparent flex items-center">
            <div className="text-white max-w-lg px-12">
              <h3 className="text-3xl font-bold mb-4">Start Their Journey With Us</h3>
              <p className="text-white/90 text-lg mb-6">
                Give your child the best start in life with our comprehensive early education program.
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
              >
                Schedule a Tour
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PreSchoolProgram;
