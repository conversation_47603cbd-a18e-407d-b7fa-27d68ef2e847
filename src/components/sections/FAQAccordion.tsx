'use client';

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import type { FAQCategory } from '@/types/faq';

interface FAQAccordionProps {
  faqs: FAQCategory[];
}

export default function FAQAccordion({ faqs }: FAQAccordionProps) {
  return (
    <section className="py-20 bg-gradient-to-b from-white via-gray-50/30 to-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6">
            <span className="text-[#009990] font-medium">FAQ</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600">
            Find answers to common questions about our programs and services
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {faqs.map((category, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
              <h3 className="text-xl font-semibold text-[#074799] mb-4">
                {category.category}
              </h3>
              <Accordion type="single" collapsible className="space-y-3">
                {category.items.map((item, itemIndex) => (
                  <AccordionItem
                    key={itemIndex}
                    value={`${index}-${itemIndex}`}
                    className="border border-gray-100 rounded-xl px-4 hover:border-[#009990]/30 transition-colors duration-300"
                  >
                    <AccordionTrigger className="text-left hover:no-underline py-4">
                      <span className="text-gray-800 font-medium">{item.question}</span>
                    </AccordionTrigger>
                    <AccordionContent className="text-gray-600">
                      {item.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
