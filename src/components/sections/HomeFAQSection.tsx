'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, MessageCircle, ChevronDown } from 'lucide-react';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import { useState } from 'react';

export default function HomeFAQSection() {
  const [openPreschool, setOpenPreschool] = useState<number | null>(null);
  const [openAfterSchool, setOpenAfterSchool] = useState<number | null>(null);

  // Get 3 featured questions from each program
  const featuredPreschoolFAQs = usaProgramsFAQs.preschool
    .flatMap(category => category.items)
    .slice(0, 3);
  
  const featuredAfterSchoolFAQs = usaProgramsFAQs.afterSchool
    .flatMap(category => category.items)
    .slice(0, 3);

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-600">
              Find answers to common questions about our programs
            </p>
          </div>

          {/* FAQ Sections */}
          <div className="space-y-8">
            {/* Preschool FAQs */}
            <div>
              <h3 className="text-xl font-semibold text-[#074799] mb-4 px-4">
                Preschool Program
              </h3>
              <div className="space-y-2">
                {featuredPreschoolFAQs.map((faq, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg shadow-sm"
                  >
                    <button
                      onClick={() => setOpenPreschool(openPreschool === index ? null : index)}
                      className="w-full text-left px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                    >
                      <span className="text-gray-900 font-medium pr-8">
                        {faq.question}
                      </span>
                      <ChevronDown 
                        className={`w-5 h-5 text-gray-500 transition-transform duration-200 flex-shrink-0 ${
                          openPreschool === index ? 'transform rotate-180' : ''
                        }`}
                      />
                    </button>
                    {openPreschool === index && (
                      <div className="px-6 pb-4 text-gray-600">
                        {faq.answer}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* After School FAQs */}
            <div>
              <h3 className="text-xl font-semibold text-[#009990] mb-4 px-4">
                After School Program
              </h3>
              <div className="space-y-2">
                {featuredAfterSchoolFAQs.map((faq, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg shadow-sm"
                  >
                    <button
                      onClick={() => setOpenAfterSchool(openAfterSchool === index ? null : index)}
                      className="w-full text-left px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                    >
                      <span className="text-gray-900 font-medium pr-8">
                        {faq.question}
                      </span>
                      <ChevronDown 
                        className={`w-5 h-5 text-gray-500 transition-transform duration-200 flex-shrink-0 ${
                          openAfterSchool === index ? 'transform rotate-180' : ''
                        }`}
                      />
                    </button>
                    {openAfterSchool === index && (
                      <div className="px-6 pb-4 text-gray-600">
                        {faq.answer}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Browse All FAQs Link */}
          <div className="text-center mt-12">
            <Link 
              href="/faq" 
              className="inline-flex items-center gap-2 px-6 py-3 rounded-lg bg-[#074799] text-white hover:bg-[#074799]/90 transition-colors duration-200"
            >
              Browse All Questions
              <ArrowRight size={18} />
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
