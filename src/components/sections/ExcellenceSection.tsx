'use client';

import { Award, GraduationCap, Users, Building, Shield, School2 } from 'lucide-react';

export default function ExcellenceSection() {
  return (
    <section className="relative py-24 overflow-hidden bg-gradient-to-b from-white via-gray-50/30 to-white">
      {/* Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-0 w-72 h-72 bg-[#009990]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-0 w-96 h-96 bg-[#E1FFBB]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Header Section */}
        <div className="max-w-3xl mx-auto text-center mb-20">
          <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6 animate-fade-in-up">
            <span className="text-[#009990] font-medium">Why Choose Us</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
            Excellence in Early Education
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience a comprehensive approach to early childhood education where every detail 
            is designed to nurture your child's growth and development.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {[
            {
              icon: Award,
              title: "NAEYC Accredited",
              description: "Nationally recognized for meeting the highest standards in early childhood education and care."
            },
            {
              icon: GraduationCap,
              title: "Specialized Programs",
              description: "Age-appropriate curriculum designed to foster cognitive, social, and emotional growth."
            },
            {
              icon: Users,
              title: "Small Class Sizes",
              description: "Low student-to-teacher ratios ensuring personalized attention and care for every child."
            },
            {
              icon: Building,
              title: "Modern Facilities",
              description: "Purpose-built spaces with advanced learning technology and secure outdoor play areas."
            },
            {
              icon: Shield,
              title: "Safety First",
              description: "Comprehensive security measures and health protocols to ensure your child's wellbeing."
            },
            {
              icon: School2,
              title: "Expert Teachers",
              description: "Highly qualified educators committed to nurturing each child's unique potential."
            }
          ].map((feature, index) => (
            <div 
              key={feature.title}
              className="group relative bg-white hover:bg-gradient-to-br hover:from-[#009990] hover:to-[#008880] rounded-2xl shadow-lg hover:shadow-xl p-8 transition-all duration-500 animate-fade-in-up overflow-hidden"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Card Decoration */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#009990]/50 to-[#E1FFBB]/50"></div>
              
              {/* Icon */}
              <div className="mb-6 relative">
                <div className="w-16 h-16 bg-[#009990]/10 group-hover:bg-white/10 rounded-2xl flex items-center justify-center transition-all duration-300 transform group-hover:rotate-6">
                  <feature.icon className="w-8 h-8 text-[#009990] group-hover:text-white transition-colors duration-300" />
                </div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-white transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-600 group-hover:text-white/90 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-[#009990]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          ))}
        </div>

        {/* Bottom Decoration */}
        <div className="absolute bottom-0 left-0 w-full overflow-hidden">
          <svg className="relative w-full h-12 text-white" preserveAspectRatio="none" viewBox="0 0 1440 48">
            <path 
              fill="currentColor" 
              d="M0 48h1440V36C1320 36 1200 24 960 24 720 24 600 36 480 36 360 36 240 24 120 24 60 24 0 36 0 36v12z"
            />
          </svg>
        </div>
      </div>
    </section>
  );
}
