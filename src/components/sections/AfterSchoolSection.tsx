'use client';

import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  Paintbrush2, 
  Music2, 
  Globe2, 
  Atom,
  Trophy,
  Brain,
  GraduationCap,
  Apple,
  Swords,
  Dribbble,
  Mic2,
  Code2,
  Heart,
  LucideIcon,
  Clock,
  Bus
} from 'lucide-react';
import Image from 'next/image';

interface AfterSchoolSectionProps {
  className?: string;
}

interface Activity {
  icon: LucideIcon;
  title: string;
  description?: string;
}

const AfterSchoolSection = ({ className = '' }: AfterSchoolSectionProps) => {
  const coreActivities: Activity[] = [
    { 
      icon: BookOpen, 
      title: 'Homework Help',
      description: 'Dedicated support for daily assignments'
    },
    { 
      icon: Calculator, 
      title: 'Math and Language',
      description: 'Enrichment in core academic subjects'
    },
    { 
      icon: Dumbbell, 
      title: 'Yoga',
      description: 'Physical and mental wellness activities'
    },
    { 
      icon: Paintbrush2, 
      title: 'Arts and Crafts',
      description: 'Creative expression and skill development'
    },
    { 
      icon: Music2, 
      title: 'Cultural Dance',
      description: 'Bollywood, Folk, and Fusion styles'
    },
    { 
      icon: Globe2, 
      title: 'Spanish',
      description: 'Language learning through immersion'
    },
    { 
      icon: At<PERSON>, 
      title: 'STEAM',
      description: 'Hands-on science and technology projects'
    },
    { 
      icon: Trophy, 
      title: 'Spelling Bee',
      description: 'Vocabulary and language competitions'
    }
  ];

  const additionalActivities: Activity[] = [
    { icon: Swords, title: 'Chess', description: 'Strategic thinking and planning' },
    { icon: Dribbble, title: 'Basketball', description: 'Team sports and physical activity' }
  ];

  const clubs: Activity[] = [
    { icon: Mic2, title: 'Public Speaking', description: 'Communication skills development' },
    { icon: Code2, title: 'Coding Club', description: 'Introduction to programming' },
    { icon: Heart, title: 'Community Service', description: 'Giving back to our community' }
  ];

  return (
    <section className={`py-24 bg-gradient-to-b from-white to-[#F8F9FA] ${className}`}>
      <div className="container mx-auto px-4 md:px-8">
        {/* Hero Section */}
        <div className="relative mb-24">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-[42px] font-bold mb-6 text-[#001A6E] leading-tight">
              After School Program
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Tulip After School (TAS) offers a comprehensive enrichment program designed to nurture academic excellence and creative growth in a safe, stimulating environment.
            </p>
          </div>

          {/* Quick Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-[#009990]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#001A6E]">Program Hours</h3>
                  <p className="text-gray-600">After School - 6:30 PM</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <Bus className="w-6 h-6 text-[#009990]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#001A6E]">Transportation</h3>
                  <p className="text-gray-600">Available from local schools</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow lg:col-span-1 md:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-[#E6FFF9] rounded-full flex items-center justify-center">
                  <GraduationCap className="w-6 h-6 text-[#009990]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#001A6E]">Age Group</h3>
                  <p className="text-gray-600">5 - 12 years</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Core Activities */}
        <div className="mb-24">
          <h3 className="text-3xl font-bold text-[#001A6E] mb-12 text-center">Core Activities</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {coreActivities.map((activity, index) => (
              <div
                key={index}
                className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    {<activity.icon className="w-8 h-8 text-[#009990]" />}
                  </div>
                  <h4 className="text-xl font-semibold text-[#001A6E] mb-2">{activity.title}</h4>
                  <p className="text-gray-600 text-sm">{activity.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Programs */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Additional Activities */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Additional Activities</h3>
            <div className="space-y-4">
              {additionalActivities.map((activity, index) => (
                <div 
                  key={index} 
                  className="group flex items-center space-x-4 p-4 bg-[#F8F9FA] rounded-xl hover:bg-[#E6FFF9] transition-colors duration-300"
                >
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    {<activity.icon className="w-6 h-6 text-[#009990]" />}
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#001A6E]">{activity.title}</h4>
                    <p className="text-gray-600 text-sm">{activity.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Clubs */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Clubs</h3>
            <div className="space-y-4">
              {clubs.map((club, index) => (
                <div 
                  key={index} 
                  className="group flex items-center space-x-4 p-4 bg-[#F8F9FA] rounded-xl hover:bg-[#E6FFF9] transition-colors duration-300"
                >
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    {<club.icon className="w-6 h-6 text-[#009990]" />}
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#001A6E]">{club.title}</h4>
                    <p className="text-gray-600 text-sm">{club.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AfterSchoolSection;
