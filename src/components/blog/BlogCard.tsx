'use client'

// This component is now deprecated. Using inline card in BlogPreview and BlogList instead.
// Keeping this file for reference in case we need to revert changes.

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'

interface BlogCardProps {
  post: {
    id: number
    title: string
    excerpt: string
    date: string
    image: string
    category: string
  }
}

const BlogCard = ({ post }: BlogCardProps) => {
  return null // Component deprecated
}

export default BlogCard