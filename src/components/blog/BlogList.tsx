'use client'

import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, Clock, ArrowRight, Tag } from 'lucide-react'
import { getBlogPosts } from '@/services/wordpress'
import { BlogPost } from '@/types/blog'

export default function BlogList() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchPosts() {
      try {
        const fetchedPosts = await getBlogPosts()
        setPosts(fetchedPosts)
      } catch (err) {
        setError('Failed to load blog posts')
        console.error('Error loading blog posts:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {[1, 2, 3].map((n) => (
          <div key={n} className="bg-white rounded-2xl shadow-sm overflow-hidden animate-pulse transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
            <div className="h-56 bg-gray-200" />
            <div className="p-8">
              <div className="flex items-center gap-4 mb-4">
                <div className="h-4 bg-gray-200 rounded w-24" />
                <div className="h-4 bg-gray-200 rounded w-24" />
              </div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4" />
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full" />
                <div className="h-4 bg-gray-200 rounded w-5/6" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-16 bg-white rounded-2xl shadow-sm px-8">
        <p className="text-red-600 mb-4 text-lg">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="inline-flex items-center px-6 py-3 rounded-xl bg-primary-600 text-white hover:bg-primary-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-16 bg-white rounded-2xl shadow-sm px-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-3">No Posts Found</h3>
        <p className="text-gray-600">Check back later for new content!</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {posts.map((post) => (
        <article
          key={post.id}
          className="group bg-white rounded-2xl shadow-sm overflow-hidden transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
        >
          <Link href={`/blog/${post.slug}`} className="block relative">
            <div className="relative h-56 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent z-10" />
              <Image
                src={post.image}
                alt={post.title}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
            </div>
            <span className="absolute top-4 right-4 z-20 inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium bg-white/90 text-primary-600 backdrop-blur-sm">
              <Tag className="w-3.5 h-3.5 mr-1.5" />
              {post.category}
            </span>
          </Link>

          <div className="p-8">
            <div className="flex items-center gap-6 text-sm text-gray-600 mb-4">
              <span className="inline-flex items-center">
                <Calendar className="w-4 h-4 mr-1.5 text-primary-600" />
                {new Date(post.date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </span>
              <span className="inline-flex items-center">
                <Clock className="w-4 h-4 mr-1.5 text-primary-600" />
                {post.readTime}
              </span>
            </div>

            <Link href={`/blog/${post.slug}`} className="block group/title">
              <h2 className="text-2xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover/title:text-primary-600 transition-colors">
                {post.title}
              </h2>
              <p className="text-gray-600 mb-6 line-clamp-3">
                {post.excerpt}
              </p>
              <span className="inline-flex items-center text-primary-600 font-medium group-hover/title:text-primary-700">
                Read Article
                <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover/title:translate-x-1" />
              </span>
            </Link>
          </div>
        </article>
      ))}
    </div>
  )
}
