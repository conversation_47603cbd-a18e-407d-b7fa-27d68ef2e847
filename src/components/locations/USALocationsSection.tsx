'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { MapPin, Phone } from 'lucide-react';

const locations = [
  {
    id: 'willow-ave',
    name: 'Sunnyvale - Willow Ave',
    address: '1159, Willow Ave',
    city: 'Sunnyvale',
    state: 'CA',
    zip: '94086',
    phones: {
      preschool: '(*************',
      afterSchool: '************'
    },
    image: '/images/locations/Willow-ave.jpg',
    type: ['preschool', 'afterSchool']
  },
  {
    id: 'lawrence-station',
    name: 'Sunnyvale - Lawrence Station Road',
    address: '1279 Lawrence Station Rd',
    city: 'Sunnyvale',
    state: 'CA',
    zip: '94089',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/lawrence-station-road.png',
    type: ['preschool']
  }
];

const allLocations = [
  ...locations,
  {
    id: 'el-camino',
    name: 'Santa Clara - El Camino Real',
    address: '2280 East El Camino Real',
    city: 'Santa Clara',
    state: 'CA',
    zip: '95050',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/El Camino Real.jpg',
    type: ['preschool']
  },
  {
    id: 'cottle-road',
    name: 'San Jose - Cottle Road',
    address: '6097 Cottle Rd',
    city: 'San Jose',
    state: 'CA',
    zip: '95123',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Cottle Road.jpg',
    type: ['preschool']
  },
  {
    id: 'grafton-st-after',
    name: 'Dublin - Grafton St',
    address: '4078 Grafton St',
    city: 'Dublin',
    state: 'CA',
    zip: '94568',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Grafton St.jpg',
    type: ['preschool']
  },
  {
    id: 'mountain-house',
    name: 'Mountain House',
    address: '700 S Escuela Dr',
    city: 'Mountain House',
    state: 'CA',
    zip: '95391',
    phones: {
      preschool: '(*************'
    },
    image: '/images/locations/Mountain House.jpg',
    type: ['preschool']
  }
];

const USALocationsSection = () => {
  const [showAllLocations, setShowAllLocations] = useState(false);
  const displayedLocations = showAllLocations ? allLocations : locations;

  return (
    <section className="bg-white py-12">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">Our USA Locations</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayedLocations.map((location) => (
            <div key={location.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="relative h-48">
                <Image
                  src={location.image}
                  alt={location.name}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-4">{location.name}</h3>
                <div className="flex items-start mb-3">
                  <MapPin className="h-5 w-5 text-gray-600 mr-2 mt-1" />
                  <p className="text-gray-600">
                    {location.address}, {location.city}, {location.state} {location.zip}
                  </p>
                </div>
                <div className="flex items-center mb-4">
                  <Phone className="h-5 w-5 text-gray-600 mr-2" />
                  <p className="text-gray-600">{location.phones.preschool}</p>
                </div>
                <Link
                  href={`/locations/usa/california/${location.city.toLowerCase()}/${location.id}`}
                  className="text-teal-600 hover:text-teal-700 font-medium flex items-center"
                >
                  Show More →
                </Link>
              </div>
            </div>
          ))}
        </div>
        {!showAllLocations && (
          <div className="text-center mt-12">
            <button
              onClick={() => setShowAllLocations(true)}
              className="inline-flex items-center px-6 py-3 text-base font-medium text-teal-600 hover:text-teal-700"
            >
              View All Locations →
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default USALocationsSection;
