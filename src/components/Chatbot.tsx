'use client'

import { useEffect, useState } from 'react'
import Script from 'next/script'
import '../styles/chatbot.css'

interface LocationData {
  [key: string]: any
}

export default function Chatbot() {
  const [isOpen, setIsOpen] = useState(false)
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null)
  const [locationData, setLocationData] = useState<string | null>(null)
  const [showLocationButtons, setShowLocationButtons] = useState(false)
  const [showCalendly, setShowCalendly] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null)

  useEffect(() => {
    // Initialize chatbot state
    const chatbotState = localStorage.getItem('chatbotOpen')
    if (chatbotState === 'true') {
      setIsOpen(true)
    }

    // Get user location
    getUserLocationAndLog()

    // Start new conversation
    if (!sessionStorage.getItem('isNewTab')) {
      clearChatbotState()
      sessionStorage.setItem('isNewTab', 'true')
      startNewConversation()
    } else {
      const savedState = localStorage.getItem('chatbotState')
      if (savedState) {
        const state = JSON.parse(savedState)
        setCurrentThreadId(state.currentThreadId)
        const chatbox = document.getElementById('chatbox')
        if (chatbox) {
          chatbox.innerHTML = state.messages
        }
      } else {
        startNewConversation()
      }
    }
  }, [])

  const getUserLocationAndLog = async () => {
    try {
      const response = await fetch('https://api.ipdata.co?api-key=2f97b73b328e4407f0f3253b801a7cc3df90027ad0d7dc5e043074fd')
      const location = await response.json()
      console.log('Location data received:', location)
      setLocationData(JSON.stringify(location))
    } catch (error) {
      console.error('Error fetching location:', error)
    }
  }

  const sendInputToGAS3 = (message: string, isUserMessage: boolean) => {
    const GAS_URL3 = 'https://script.google.com/macros/s/AKfycbxXX_ycbh9DHFZVF8Wnyf_FW2G8uBycgVSNYsz_9ccJoi6exq9BCvknsFkswi0QxG2s/exec'
    const queryParams = `?message=${encodeURIComponent(message)}&isUserMessage=${isUserMessage}`

    fetch(GAS_URL3 + queryParams, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
      .then(() => {
        console.log("Input sent to Google Sheet via GAS_URL3")
      })
      .catch(error => {
        console.error("Error sending input to GAS_URL3:", error)
      })
  }

  const sendToGas4 = (message: string, isUserMessage: boolean) => {
    const GAS_URL4 = 'https://script.google.com/macros/s/AKfycbwqEnscT6ppKZlOzwHslmez22u_7-0796kZcXOfpRRQZIQa9RB3ZpEA6wkBZyxH-leUXA/exec'

    if (message.includes('@') || (message.match(/\d/g) || []).length >= 6) {
      const queryParams = `?message=${encodeURIComponent(message)}&isUserMessage=${isUserMessage}`
      fetch(GAS_URL4 + queryParams, {
        method: 'POST',
        mode: 'no-cors',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
        .then(() => {
          console.log("Message sent to Google Sheet via GAS_URL4")
        })
        .catch(error => {
          console.error("Error sending message to GAS_URL4:", error)
        })
    } else {
      console.log("No lead found in the message; not sent to Google Sheet.")
    }
  }

  const toggleChatbot = () => {
    setShowCalendly(false)
    setIsOpen(!isOpen)
    localStorage.setItem('chatbotOpen', (!isOpen).toString())
  }

  const startConversation = async () => {
    try {
      const response = await fetch('https://chatbot-for-tulip-kids-samuelsicking.replit.app/start', {
        method: 'GET'
      })
      const data = await response.json()
      setCurrentThreadId(data.thread_id)
      console.log("New conversation started with thread ID:", data.thread_id)
    } catch (error) {
      console.error('Error starting conversation:', error)
    }
  }

  const createLoadingElement = () => {
    const loadingElement = document.createElement('div')
    loadingElement.className = 'loading-dots'
    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('div')
      loadingElement.appendChild(dot)
    }
    return loadingElement
  }

  const displayChatbotMessage = (message: string) => {
    const chatbox = document.getElementById('chatbox')
    if (!chatbox) return

    const messageElement = document.createElement('p')
    messageElement.className = 'chat-assistant'
    messageElement.textContent = message
    chatbox.appendChild(messageElement)
    messageElement.innerHTML = convertTextToHtml(message)
    
    const popSound = document.getElementById('pop-sound') as HTMLAudioElement
    if (popSound) popSound.play()
    
    saveChatbotState()
    if (message !== "Hi, how can I help you?") {
      console.log("Chatbot:", message)
    }
  }

  const displayUserMessage = (message: string) => {
    const chatbox = document.getElementById('chatbox')
    if (!chatbox) return

    const userElement = document.createElement('p')
    userElement.className = 'chat-user'
    userElement.textContent = message
    chatbox.appendChild(userElement)
    saveChatbotState()
    console.log("User:", message)
  }

  const convertTextToHtml = (text: string) => {
    let replacedText = text.replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig, (url) => {
      return '<a href="' + url + '" target="_blank">' + url + '</a>'
    })

    replacedText = replacedText.replace(/(\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b)/ig, (email) => {
      return '<a href="mailto:' + email + '">' + email + '</a>'
    })

    return replacedText
  }

  const removeSourceToken = (text: string) => {
    return text.replace(/【.*?】/g, '')
  }

  const saveChatbotState = () => {
    const chatbox = document.getElementById('chatbox')
    if (!chatbox) return

    const state = {
      currentThreadId: currentThreadId,
      messages: chatbox.innerHTML
    }
    localStorage.setItem('chatbotState', JSON.stringify(state))
  }

  const clearChatbotState = () => {
    localStorage.removeItem('chatbotState')
    const chatbox = document.getElementById('chatbox')
    if (chatbox) {
      chatbox.innerHTML = ''
    }
  }

  const startNewConversation = () => {
    startConversation()
    const chatbox = document.getElementById('chatbox')
    if (!chatbox) return

    const loadingElement = createLoadingElement()
    chatbox.appendChild(loadingElement)

    setTimeout(() => {
      chatbox.removeChild(loadingElement)
      displayChatbotMessage("Hi, how may I assist you today?")
    }, 2000)
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    const chatInput = document.getElementById('chat-input') as HTMLInputElement
    const userMessage = chatInput.value

    if (!userMessage.trim()) {
      alert("Please enter a message.")
      return
    }

    const messageBtn = document.getElementById('messagebtn') as HTMLButtonElement
    messageBtn.disabled = true
    displayUserMessage(userMessage)
    chatInput.value = ''
    
    const loadingElement = createLoadingElement()
    const chatbox = document.getElementById('chatbox')
    if (chatbox) {
      chatbox.scrollTop = chatbox.scrollHeight
      chatbox.appendChild(loadingElement)
    }

    try {
      const response = await fetch('https://chatbot-for-tulip-kids-samuelsicking.replit.app/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          thread_id: currentThreadId,
          message: userMessage
        }),
      })

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      const data = await response.json()
      messageBtn.disabled = false
      
      if (chatbox && chatbox.contains(loadingElement)) {
        chatbox.removeChild(loadingElement)
      }
      
      displayChatbotMessage(removeSourceToken(data.response))
      
      const popSound = document.getElementById('pop-sound') as HTMLAudioElement
      if (popSound) popSound.play()
      
      chatInput.value = ''
      chatbox!.scrollTop = chatbox!.scrollHeight
    } catch (error) {
      console.error('Error:', error)
      messageBtn.disabled = false
    }
  }

  const handleBookTour = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowLocationButtons(true)
    setIsOpen(false)
    setShowCalendly(false)
  }

  const handleLocationSelect = (location: string) => {
    setSelectedLocation(location)
    setShowLocationButtons(false)
    setShowCalendly(true)
  }

  const handleBackToChatbot = () => {
    setShowCalendly(false)
    setIsOpen(true)
    setShowLocationButtons(false)
  }

  return (
    <>
      <audio id="pop-sound" src="https://cdn.glitch.global/01a66850-f583-4369-9887-fcb7f2aecbf6/323743__reitanna__mouth-pop5.wav?v=1716411364280" preload="auto" />
      
      {showCalendly && (
        <div id="calendly-widget-container" style={{ display: 'block' }}>
          <button id="back-to-chatbot" onClick={handleBackToChatbot}>Back to Chatbot</button>
          {selectedLocation === 'santa-clara' && (
            <>
              <iframe 
                src="https://api.leadconnectorhq.com/widget/booking/ZyqzAHqnkDjien19fOQ6" 
                style={{ width: '100%', border: 'none', overflow: 'hidden', height: '100%' }} 
                scrolling="no" 
                id="ZyqzAHqnkDjien19fOQ6_1750978281610"
              />
              <Script src="https://link.msgsndr.com/js/form_embed.js" />
            </>
          )}
          {selectedLocation === 'sunnyvale' && (
            <>
              <iframe 
                src="https://api.leadconnectorhq.com/widget/booking/tkHjBxZkvg2H96XYBUsx" 
                style={{ width: '100%', border: 'none', overflow: 'hidden', height: '100%' }} 
                scrolling="no" 
                id="tkHjBxZkvg2H96XYBUsx_1750978262371"
              />
              <Script src="https://link.msgsndr.com/js/form_embed.js" />
            </>
          )}
        </div>
      )}

      <button id="chatbot-toggle" onClick={toggleChatbot}>
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z"/>
          <path d="M7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z"/>
        </svg>
      </button>

      {isOpen && (
        <div id="chat-widget" style={{ display: 'flex' }}>
          <div className="tour-booking">
            <a href="#" id="book-tour-link" onClick={handleBookTour}>Click here to book a tour</a>
          </div>
          <div className="chatbot-header">
            <div className="chatbot-header-line">
              <div>
                <div className="chat-box-title-line">
                  <h4 className="chat-box-title">AI Assistant</h4>
                </div>
                <p id="intro">
                  Hi, I am a personal AI assistant, ask me anything you want to know about Tulip Kids!
                </p>
              </div>
              <a href="#" className="close-btn" id="chatbot-close" onClick={(e) => { e.preventDefault(); setIsOpen(false) }}>
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13 1L1 13M1 1L13 13" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            </div>
          </div>
          <div id="chatbox"></div>
          <form id="chat-form" onSubmit={handleSubmit}>
            <input 
              type="text" 
              id="chat-input" 
              defaultValue="Ask a question" 
              onFocus={(e) => { if (e.target.value === 'Ask a question') e.target.value = '' }}
              onBlur={(e) => { if (e.target.value === '') e.target.value = 'Ask a question' }}
            />
            <button type="submit" id="messagebtn">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </form>
        </div>
      )}

      {showLocationButtons && (
        <div id="location-buttons-container" style={{ display: 'block' }}>
          <p id="loc">Select a location to tour:</p>
          <button 
            className="location-button" 
            id="btn1" 
            onClick={() => handleLocationSelect('santa-clara')}
          >
            Santa Clara location
          </button>
          <button 
            className="location-button" 
            id="btn2" 
            onClick={() => handleLocationSelect('sunnyvale')}
          >
            Sunnyvale location
          </button>
        </div>
      )}
    </>
  )
}