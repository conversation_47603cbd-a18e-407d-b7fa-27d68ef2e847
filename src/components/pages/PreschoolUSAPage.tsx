'use client'

import React from 'react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

import Image from 'next/image';
import { MapPin, Clock, Phone, Mail, Calendar, Book, ChevronDown, Camera, Baby, School, Users, GraduationCap } from 'lucide-react';
import Button from '@/components/shared/Button';
import SectionTitle from '@/components/shared/SectionTitle';
import ContactForm from '@/components/shared/ContactForm';
import DailySchedule from '@/components/shared/DailySchedule';
import ParentTestimonials from '@/components/shared/ParentTestimonials';
import { usaLocations as locations, Location } from '@/data/locations';
import { usaPreschoolFAQs } from '@/data/faq/usa-preschool';
import FAQAccordion from '@/components/sections/FAQAccordion';

const programs = [
  {
    title: 'Infant Care',
    age: '12-24 months',
    icon: Baby,
    description: 'Nurtures infants development through responsive caregiving and a safe, stimulating environment.',
  },
  {
    title: 'Jr. Preschool',
    age: '2-3 years',
    icon: School,
    description: 'Encourages social interaction and foundational learning for children aged 2-3 through play-based activities.',
  },
  {
    title: 'Preschool',
    age: '3-5 years',
    icon: Users,
    description: 'Provides a comprehensive curriculum for ages 3-5, focusing on holistic development and school readiness.',
  },
  {
    title: 'Pre-K',
    age: '4-5 years',
    icon: GraduationCap,
    description: 'Prepares children for kindergarten with exceptional targeted skills in literacy, math, and social-emotional growth.',
  },
];

const ProgramCard = ({ program }: { program: any }) => (
  <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
    <div className="w-20 h-20 bg-gradient-to-br from-[#E1FFBB] to-[#d0ffad] rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-sm">
      <program.icon size={36} className="text-[#009990] transform hover:scale-110 transition-transform duration-200" />
    </div>
    <h3 className="text-2xl font-bold text-[#074799] mb-3 text-center">{program.title}</h3>
    <p className="text-sm font-semibold mb-4 text-center px-2 py-1 bg-[#E1FFBB] rounded-full inline-block mx-auto w-auto">{program.age}</p>
    <p className="text-gray-600 text-center mb-8 leading-relaxed">{program.description}</p>
    <Button 
      variant="primary"
      className="w-full bg-[#074799] hover:bg-[#009990] text-white transition-colors duration-300"
    >
      Learn More
    </Button>
  </div>
);

const Programs = () => {
  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl font-bold text-[#074799] mb-6">Our Programs</h2>
          <p className="text-gray-600 text-lg">
            Discover our comprehensive educational programs designed to nurture your child's growth and development.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {programs.map((program, index) => (
            <ProgramCard key={index} program={program} />
          ))}
        </div>
      </div>
    </section>
  );
};

const PreschoolUSAPage = () => {
  const [activeTab, setActiveTab] = React.useState('preschool');
  
  // Convert locations object to array and filter based on type
  const locationsList = Object.values(locations);
  const filteredLocations = locationsList.filter(location => 
    activeTab === 'preschool' 
      ? location.type?.includes('preschool')
      : location.type?.includes('afterSchool')
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#E1FFBB] to-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-[#E1FFBB] via-white to-white py-32">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-6xl font-bold text-[#074799] mb-6">Tulip Kids USA</h1>
            <p className="text-xl text-gray-600 mb-8">
              Nurturing Young Minds Across California
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="primary" className="px-8 py-3 bg-[#074799] hover:bg-[#009990] text-white transition-colors duration-300">
                Schedule a Visit
              </Button>
              <Button variant="primary" className="px-8 py-3 bg-[#074799] hover:bg-[#009990] text-white transition-colors duration-300">
                Explore Programs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <Programs />

      {/* About Our Centers */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl font-bold text-[#074799] mb-4">About Our Centers</h2>
            <p className="text-xl text-gray-600">
              Excellence in Early Childhood Education Across California
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <MapPin className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Multiple Locations</h3>
              <p className="text-gray-600">6 Centers across California Bay Area</p>
            </div>

            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <Clock className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Flexible Hours</h3>
              <p className="text-gray-600">8:00 AM to 6:00 PM, Monday to Friday</p>
            </div>

            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <Phone className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Get in Touch</h3>
              <p className="text-gray-600">Contact your nearest center</p>
            </div>
          </div>
        </div>
      </section>

      {/* Location Widget Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-[#074799] mb-4">Find a Center Near You</h2>
            <p className="text-xl text-gray-600">Discover Tulip Kids centers in your region</p>
          </div>

          <div className="mb-8 flex justify-center gap-4">
            <button
              onClick={() => setActiveTab('preschool')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-colors ${
                activeTab === 'preschool'
                  ? 'bg-[#074799] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Preschool Centers
            </button>
            <button
              onClick={() => setActiveTab('afterSchool')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-colors ${
                activeTab === 'afterSchool'
                  ? 'bg-[#074799] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              After School Centers
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredLocations.map((location: Location) => (
              <div
                key={location.slug}
                className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                {/* Image */}
                <div className="relative h-48">
                  <Image
                    src={location.heroImage}
                    alt={location.name}
                    fill
                    className="object-cover"
                  />
                  {/* Program Type Badge */}
                  <div className="absolute top-4 left-4 flex gap-2">
                    {location.type?.includes('preschool') && (
                      <span className="bg-[#009990] text-white px-3 py-1 rounded-full text-sm">
                        Preschool
                      </span>
                    )}
                    {location.type?.includes('afterSchool') && (
                      <span className="bg-[#074799] text-white px-3 py-1 rounded-full text-sm">
                        After School
                      </span>
                    )}
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-[#074799] mb-2">{location.name}</h3>
                  <div className="flex items-start gap-2 text-gray-600 mb-2">
                    <MapPin className="w-5 h-5 mt-1 flex-shrink-0" />
                    <p>{location.address.street}, {location.address.city}, {location.address.state} {location.address.zip}</p>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600 mb-4">
                    <Phone className="w-5 h-5" />
                    <p>{location.address.phone}</p>
                  </div>
                  
                  <Link 
                    href={`/locations/usa/${location.slug}`}
                    className="inline-flex items-center text-[#074799] hover:text-[#009990] font-medium transition-colors"
                  >
                    View Details
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Daily Schedule Section */}
      <DailySchedule />

      {/* Parent Testimonials */}
      <ParentTestimonials />

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white via-gray-50/30 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6">
              <span className="text-[#009990] font-medium">FAQ</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
              Common Questions About Preschool
            </h2>
            <p className="text-lg text-gray-600">
              Find answers to frequently asked questions about our preschool program
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {usaPreschoolFAQs.slice(0, 2).map((category, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-[#074799] mb-4">
                  {category.category}
                </h3>
                <Accordion type="single" collapsible className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <AccordionItem
                      key={itemIndex}
                      value={`${index}-${itemIndex}`}
                      className="border border-gray-100 rounded-xl px-4 hover:border-[#009990]/30 transition-colors duration-300"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        <span className="text-gray-800 font-medium">{item.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600">
                        {item.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link 
              href="/faq" 
              className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-[#074799] to-[#009990] text-white hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl"
            >
              Browse All FAQs
              <ArrowRight size={20} />
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <ContactForm />
    </div>
  );
};

export default PreschoolUSAPage;
