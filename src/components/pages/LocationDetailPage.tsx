"use client";

import { Clock, GraduationCap, MapPin, Phone, School2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface LocationDetailPageProps {
  location: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    phones: {
      preschool?: string;
      afterSchool?: string;
    };
    image: string;
    type: string[];
    programs?: string[];
  };
}

export default function LocationDetailPage({
  location,
}: LocationDetailPageProps) {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[400px] md:h-[500px]">
        <Image
          src={location?.image}
          alt={location?.name}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 to-[#074799]/80"></div>
        <div className="container mx-auto px-4 h-full flex items-center relative z-10">
          <div className="max-w-3xl text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {location?.name}
            </h1>
            <div className="flex items-start gap-2 text-gray-100 mb-4">
              <MapPin className="w-6 h-6 mt-1" />
              <p className="text-xl">{`${location?.address}, ${location?.city}, ${location?.state} ${location?.zip}`}</p>
            </div>
            {location?.phones?.preschool && (
              <div className="flex items-center gap-2 text-gray-100">
                <Phone className="w-6 h-6" />
                <p className="text-xl">{location.phones.preschool}</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our Programs</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {location?.type?.includes("preschool") && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center">
                    <School2 className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h3 className="text-xl font-semibold">Preschool Program</h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Our preschool program focuses on early childhood development
                  through play-based learning and structured activities.
                </p>
                <Link
                  href="/programs/preschool"
                  className="text-[#009990] hover:text-[#074799] font-medium inline-flex items-center"
                >
                  Learn More <span className="ml-2">→</span>
                </Link>
              </div>
            )}

            {location?.type.includes("afterSchool") && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-full flex items-center justify-center">
                    <GraduationCap className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h3 className="text-xl font-semibold">
                    After School Program
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Our after-school program provides a balanced mix of homework
                  support, enrichment activities, and supervised play.
                </p>
                <Link
                  href="/programs/after-school"
                  className="text-[#009990] hover:text-[#074799] font-medium inline-flex items-center"
                >
                  Learn More <span className="ml-2">→</span>
                </Link>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Schedule Tour Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Schedule a Tour</h2>
          <p className="text-xl text-gray-600 mb-8">
            Visit our facility and see firsthand how we nurture young minds in a
            safe and engaging environment.
          </p>
          <Link
            href="/contact"
            className="bg-[#009990] hover:bg-[#074799] text-white px-8 py-3 rounded-full font-medium transition-all inline-flex items-center"
          >
            Book a Tour <span className="ml-2">→</span>
          </Link>
        </div>
      </section>
    </main>
  );
}
