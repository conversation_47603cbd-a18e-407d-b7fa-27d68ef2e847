'use client'

import { createContext, useContext, ReactNode } from 'react'
import { useSearchParams as useNextSearchParams } from 'next/navigation'
import { Suspense } from 'react'

// Create a context to hold the search params
const SearchParamsContext = createContext<{
  getParam: (key: string) => string
}>({
  getParam: () => '',
})

// Provider component that uses useSearchParams and provides a safe interface
function SearchParamsProviderInner({ children }: { children: ReactNode }) {
  const searchParams = useNextSearchParams()
  
  // Create a safe wrapper around searchParams
  const contextValue = {
    getParam: (key: string) => searchParams?.get(key) || '',
  }
  
  return (
    <SearchParamsContext.Provider value={contextValue}>
      {children}
    </SearchParamsContext.Provider>
  )
}

// Exported provider that wraps the inner provider with Suspense
export function SearchParamsProvider({ children }: { children: ReactNode }) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SearchParamsProviderInner>
        {children}
      </SearchParamsProviderInner>
    </Suspense>
  )
}

// Hook to use search params safely
export function useSafeSearchParams() {
  return useContext(SearchParamsContext)
}
