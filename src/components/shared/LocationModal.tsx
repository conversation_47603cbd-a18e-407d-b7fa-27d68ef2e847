'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { MapPin, X } from 'lucide-react';

export default function LocationModal() {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if user has already made a location choice
    const hasChosenLocation = localStorage.getItem('locationChoice');
    if (!hasChosenLocation) {
      setIsOpen(true);
    }
  }, []);

  const handleLocationChoice = (location: 'usa' | 'india') => {
    localStorage.setItem('locationChoice', location);
    setIsOpen(false);
    router.push(`/locations/${location}`);
  };

  const handleClose = () => {
    localStorage.setItem('locationChoice', 'home');
    setIsOpen(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[200] flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-lg mx-4 bg-white rounded-xl shadow-xl">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <X className="w-6 h-6" />
        </button>

        {/* Modal Content */}
        <div className="p-6">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <Image
              src="/images/tulip-logo.png"
              alt="Tulip Kids Logo"
              width={120}
              height={120}
              className="w-24 h-24"
            />
          </div>

          {/* Title */}
          <h2 className="text-2xl font-semibold text-center text-gray-800 mb-4">
            Welcome to Tulip Kids
          </h2>
          <p className="text-center text-gray-600 mb-8">
            Please select your location to continue
          </p>

          {/* Location Buttons */}
          <div className="flex flex-col gap-4">
            <button
              onClick={() => handleLocationChoice('usa')}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors"
            >
              <MapPin className="w-5 h-5" />
              United States
            </button>
            <button
              onClick={() => handleLocationChoice('india')}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors"
            >
              <MapPin className="w-5 h-5" />
              India
            </button>
          </div>

          {/* Skip Option */}
          <button
            onClick={handleClose}
            className="w-full text-center text-gray-500 hover:text-gray-700 mt-4 py-2"
          >
            Continue to Home Page
          </button>
        </div>
      </div>
    </div>
  );
}
