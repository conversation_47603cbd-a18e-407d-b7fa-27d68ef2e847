'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { MapPin, Phone, Mail } from 'lucide-react';
import Button from '../shared/Button';
import SectionTitle from '../shared/SectionTitle';
// import FrequentlyAskedQuestions from '@../../components/shared/sections/FrequentlyAskedQuestions';
// import DailySchedule from '../sections/DailySchedule';
// import ParentTestimonials from '../sections/ParentTestimonials';
import ContactForm from '../shared/ContactForm';

const locations = [
  {
    id: 'tulipkids',
    name: 'Tulip Kids - Indore',
    address: '11, Bima Nagar, Anand Bazaar',
    city: 'Indore',
    state: 'MP',
    zip: '452001',
    phone: '+91 95755 45952',
    email: '<EMAIL>',
    image: '/images/locations/indore.jpg',
    programs: ['Preschool'],
    type: 'PreSchool'
  },
  {
    id: 'nipania',
    name: 'Indore - Nipania',
    address: '27, Samar Park Colony, Nipania, Indore, MP 452010',
    city: 'Indore',
    state: 'MP',
    zip: '452010',
    phone: '+91 9575545200',
    email: '<EMAIL>',
    image: '/images/locations/nipania.jpg',
    programs: ['Preschool'],
    type: 'PreSchool'
  }
];

const programs = [
  {
    title: 'Early Learning Program',
    age: '2 - 3 years',
    description: 'Foundation program focusing on basic skills and social development.'
  },
  {
    title: 'Preschool Program',
    age: '3 - 4 years',
    description: 'Comprehensive program with emphasis on cognitive and social skills.'
  },
  {
    title: 'Kindergarten Readiness',
    age: '4 - 5 years',
    description: 'Advanced program preparing children for successful school transition.'
  }
];

const IndiaLocations = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#E1FFBB] to-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-[#E1FFBB] via-white to-white py-32">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-6xl font-bold text-[#074799] mb-6">Tulip Kids India</h1>
            <p className="text-xl text-gray-600 mb-8">
              Bringing World-Class Early Education to India
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="primary" className="px-8 py-3">
                Schedule a Visit
              </Button>
              <Button variant="secondary" className="px-8 py-3">
                Explore Programs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Our Centers */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl font-bold text-[#074799] mb-4">About Our Centers</h2>
            <p className="text-xl text-gray-600">
              Excellence in Early Childhood Education in India
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <MapPin className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Prime Location</h3>
              <p className="text-gray-600">Conveniently located in Vijay Nagar, Indore</p>
            </div>

            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <Phone className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Get in Touch</h3>
              <p className="text-gray-600">Contact us for enrollment information</p>
            </div>

            <div className="bg-gradient-to-br from-[#E1FFBB]/50 to-white p-8 rounded-2xl shadow-sm">
              <div className="w-14 h-14 bg-white rounded-full flex items-center justify-center mb-6 shadow-sm">
                <Mail className="w-7 h-7 text-[#074799]" />
              </div>
              <h3 className="text-xl font-bold text-[#074799] mb-2">Email Us</h3>
              <p className="text-gray-600">Send us your inquiries anytime</p>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20 bg-gradient-to-b from-[#E1FFBB]/30 to-white" id="programs">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl font-bold text-[#074799] mb-4">Our Programs</h2>
            <p className="text-xl text-gray-600">
              Comprehensive early education programs for every age group
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {programs.map((program) => (
              <div key={program.title} className="bg-white rounded-2xl shadow-lg p-8">
                <h3 className="text-xl font-bold text-[#074799] mb-2">{program.title}</h3>
                <p className="text-sm text-[#009990] mb-4">{program.age}</p>
                <p className="text-gray-600">{program.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Locations Section */}
      <section className="py-20 bg-white" id="locations">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl font-bold text-[#074799] mb-4">Our Centers in India</h2>
            <p className="text-xl text-gray-600">
              Find a Tulip Kids center near you
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {locations.map((location) => (
              <div key={location.id} className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src={location.image}
                    alt={location.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-[#074799] mb-2">{location.name}</h3>
                  <div className="space-y-2 text-gray-600">
                    <p className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      {location.address}, {location.city}, {location.state} {location.zip}
                    </p>
                    <p className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      {location.phone}
                    </p>
                    <p className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      {location.email}
                    </p>
                  </div>
                  <div className="mt-4">
                    <Link href="/contact" className="text-[#074799] font-semibold hover:underline">
                      Contact Center →
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Schedule Section */}
      {/* <DailySchedule /> */}

      {/* Testimonials Section */}
      {/* <ParentTestimonials /> */}

      {/* FAQ Section */}
      {/* <FrequentlyAskedQuestions /> */}

      {/* Contact Form */}
      <ContactForm />

      {/* Expansion Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <SectionTitle
            title="Our Growing Presence"
            description="Serving families across Indore"
          />
          <div className="mt-12 max-w-2xl mx-auto">
            <p className="text-gray-600 mb-8">
              We're proud to offer our world-class early childhood education at our Bima Nagar and Nipania locations in Indore.
              Visit either of our centers to experience the Tulip Kids difference firsthand.
            </p>
            <Button href="/contact" variant="primary">
              Schedule a Tour
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IndiaLocations;
