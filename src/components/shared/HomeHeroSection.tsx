'use client'

import React from 'react';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export default function HomeHeroSection() {
  return (
    <section className="relative w-full h-screen min-h-[600px] overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <div className="relative w-full h-full">
          <Image
            src="/images/hero-home.jpg"
            alt="Tulip Kids Hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
            quality={100}
          />
          <div className="absolute inset-0 bg-[#001A6E]/10"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/70 to-transparent"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/90"></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative h-full">
        <div className="container mx-auto px-4 h-full flex items-center">
          <div className="max-w-3xl -mt-20">
            <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6 animate-fade-in-up">
              <span className="text-[#009990] font-medium">Welcome to Tulip Kids</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-3 animate-fade-in-up" style={{ animationDelay: '200ms' }}>
              Nurturing Young Minds
            </h1>
            <h1 className="text-4xl md:text-6xl font-bold text-white/90 mb-6 animate-fade-in-up" style={{ animationDelay: '400ms' }}>
              for a Brighter Tomorrow
            </h1>
            <p className="text-lg md:text-xl text-white/80 mb-8 max-w-2xl animate-fade-in-up" style={{ animationDelay: '600ms' }}>
              Join Tulip Kids Inc's award-winning early education programs. Where learning meets play, and every child's potential blooms.
            </p>
            <div className="flex flex-wrap gap-4 animate-fade-in-up" style={{ animationDelay: '800ms' }}>
              <Link href="/about">
                <button className="group px-8 py-3 bg-[#009990] text-white rounded-full font-medium transition-all duration-300 hover:bg-[#00b3a8] hover:translate-y-[-2px] hover:shadow-lg hover:shadow-[#009990]/30 active:translate-y-[0px]">
                  <span className="flex items-center gap-2">
                    Explore More
                    <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-2" />
                  </span>
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Wave Decoration */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg className="w-full h-auto" viewBox="0 0 1440 100" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
            <path d="M0 50L48 45.7C96 41.3 192 32.7 288 29.2C384 25.7 480 27.3 576 35.8C672 44.3 768 59.7 864 64.2C960 68.7 1056 62.3 1152 54.2C1248 46 1344 36.3 1392 31.5L1440 26.7V100H1392C1344 100 1248 100 1152 100C1056 100 960 100 864 100C768 100 672 100 576 100C480 100 384 100 288 100C192 100 96 100 48 100H0V50Z" fill="white"/>
          </svg>
        </div>
      </div>

      {/* Floating Shapes */}
      <div className="absolute top-1/4 right-[10%] w-24 h-24 rounded-full bg-[#009990]/10 backdrop-blur-sm animate-float"></div>
      <div className="absolute bottom-1/3 right-[15%] w-16 h-16 rounded-full bg-[#001A6E]/10 backdrop-blur-sm animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/3 right-[20%] w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm animate-float" style={{ animationDelay: '2s' }}></div>
    </section>
  );
}