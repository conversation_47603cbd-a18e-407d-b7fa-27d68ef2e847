'use client'

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight } from 'lucide-react';

export default function LocationDirectory() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Find a Center Near You</h2>
          <p className="text-xl text-gray-600">Discover Tulip Kids centers in your region</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* USA Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="relative h-64">
              <Image
                src="/images/locations/san-francisco.jpg"
                alt="USA Centers"
                fill
                className="object-cover"
                priority
              />
            </div>
            <div className="p-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">US Locations</h3>
              <p className="text-gray-600 mb-2">Explore our centers across California</p>
              <p className="text-gray-500 mb-4">6 centers available</p>
              <Link 
                href="/locations/usa"
                className="text-[#009990] hover:text-[#074799] font-medium flex items-center"
              >
                Find Centers
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>

          {/* India Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="relative h-64">
              <Image
                src="/images/taj-mehal.jpg"
                alt="India Centers"
                fill
                className="object-cover"
                priority
              />
            </div>
            <div className="p-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">India Locations</h3>
              <p className="text-gray-600 mb-2">Discover our presence in Indian metropolitan cities</p>
              <p className="text-gray-500 mb-4">2 centers available</p>
              <Link 
                href="/locations/india"
                className="text-[#009990] hover:text-[#074799] font-medium flex items-center"
              >
                Find Centers
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}