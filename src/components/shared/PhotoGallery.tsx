'use client';

import React, { useEffect, useState, useCallback } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import { EmblaOptionsType } from 'embla-carousel';
import mediumZoom from 'medium-zoom';

interface PhotoGalleryProps {
  folderPath: string;
  imageCount: number;
  location: string;
}

const mainCarouselOptions: EmblaOptionsType = {
  skipSnaps: false,
  loop: true
};

const thumbCarouselOptions: EmblaOptionsType = {
  dragFree: true,
  containScroll: "trimSnaps",
  loop: false,
  align: 'start',
  slidesToScroll: 1,
  direction: 'ltr'
};

export default function PhotoGallery({ folderPath, imageCount, location }: PhotoGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mainViewportRef, embla] = useEmblaCarousel(mainCarouselOptions);
  const [thumbViewportRef, emblaThumbs] = useEmblaCarousel({
    dragFree: true,
    align: 'start',
    containScroll: 'trimSnaps',
    loop: false,
    slidesToScroll: 1,
    direction: 'ltr'
  });

  const images = Array.from({ length: imageCount }, (_, i) => {
    // Special case for willow-ave-pre images which have a different naming pattern
    const imageName = location === 'willow-ave-pre' ? 'willo-ave-pre' : location;
    return {
      src: `${folderPath}/${imageName} ${i + 1}.webp`,
      alt: `${location.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Image ${i + 1}`
    };
  });

  const onThumbClick = useCallback(
    (index: number) => {
      if (!embla || !emblaThumbs) return;
      embla.scrollTo(index);
    },
    [embla, emblaThumbs]
  );

  const onSelect = useCallback(() => {
    if (!embla || !emblaThumbs) return;
    setSelectedIndex(embla.selectedScrollSnap());
    emblaThumbs.scrollTo(embla.selectedScrollSnap());
  }, [embla, emblaThumbs]);

  useEffect(() => {
    if (!embla) return;
    onSelect();
    embla.on('select', onSelect);
    return () => {
      embla.off('select', onSelect);
      return void 0;
    };
  }, [embla, onSelect]);

  // Initialize medium-zoom
  useEffect(() => {
    const zoom = mediumZoom('.gallery-image', {
      margin: 20,
      background: 'rgba(0, 0, 0, 0.9)',
      scrollOffset: 0,
    });

    return () => {
      zoom.detach();
    };
  }, []);

  return (
    <div className="max-w-7xl mx-auto">
      {/* Main Carousel */}
      <div className="relative mb-8 rounded-2xl overflow-hidden bg-gray-100">
        <div className="overflow-hidden" ref={mainViewportRef}>
          <div className="flex">
            {images.map((image, index) => (
              <div key={index} className="relative flex-[0_0_100%] min-w-0">
                <div className="relative aspect-[16/9]">
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    className="gallery-image object-cover cursor-zoom-in transition-transform duration-300 hover:scale-105"
                    sizes="(max-width: 1280px) 100vw, 1280px"
                    priority={index === 0}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows */}
        <button
          onClick={() => embla?.scrollPrev()}
          className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-lg hover:bg-white transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
          </svg>
        </button>
        <button
          onClick={() => embla?.scrollNext()}
          className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-lg hover:bg-white transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
          </svg>
        </button>
      </div>

      {/* Thumbnails */}
      <div className="relative px-4">
        <div className="overflow-hidden" ref={thumbViewportRef}>
          <div className="flex gap-2 md:gap-4">
            {images.map((image, index) => (
              <div
                key={index}
                className={`relative flex-[0_0_calc(25%-6px)] md:flex-[0_0_calc(25%-12px)] min-w-0 transition-opacity duration-200 ${
                  index === selectedIndex ? 'opacity-100' : 'opacity-50'
                }`}
                onClick={() => onThumbClick(index)}
              >
                <div className="relative aspect-[16/9] rounded-lg overflow-hidden cursor-pointer">
                  <Image
                    src={image.src}
                    alt={`Thumbnail ${index + 1}`}
                    fill
                    className="object-cover transition-transform duration-300 hover:scale-110"
                    sizes="(max-width: 768px) 25vw, 20vw"
                  />
                  <div className={`absolute inset-0 border-2 rounded-lg transition-colors ${
                    index === selectedIndex ? 'border-[#009990]' : 'border-transparent'
                  }`} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
