import { GraduationCap, School, Building2, Shield, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const ProgramsSection = () => {
  const pathname = usePathname();
  const isIndiaLocation = pathname?.includes('/india/');
  const locationName = pathname?.split('/').pop() || '';

  const programs = [
    {
      title: 'Junior Pre-School',
      age: '2-3 years',
      icon: GraduationCap,
      features: [
        'Nurturing, responsive caregiving',
        'Age-appropriate activities',
        'Safe learning environment'
      ]
    },
    {
      title: 'Preschool Program',
      age: '3-4 years',
      icon: Building2,
      features: [
        'Social interaction skills',
        'Play-based learning',
        'Motor skill development'
      ]
    },
    {
      title: 'Pre-K Program',
      age: '4-5 years',
      icon: Shield,
      features: [
        'Structured learning activities',
        'Creative expression',
        'Early literacy skills'
      ]
    },
    {
      title: 'Kindergarten',
      age: '5-6 years',
      icon: School,
      features: [
        'School readiness skills',
        'Advanced concepts',
        'Independent learning'
      ]
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Our Programs</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Age-appropriate programs designed to nurture every aspect of your child&apos;s development
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {programs.map((program) => (
              <div key={program.title} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col">
                <div className="p-6 flex-grow">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-[#E8F5E9] rounded-full flex items-center justify-center">
                      <program.icon className="w-6 h-6 text-[#009990]" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold text-[#001A6E]">{program.title}</h3>
                      <p className="text-[#009990] text-sm font-medium">{program.age}</p>
                    </div>
                  </div>
                  <ul className="space-y-2 mb-4">
                    {program.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm">
                        <span className="w-4 h-4 bg-[#E8F5E9] rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="w-2 h-2 bg-[#009990] rounded-full" />
                        </span>
                        <span className="ml-2 text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="px-6 pb-6">
                  <Link
                    href={`/contact?country=${isIndiaLocation ? 'india' : 'usa'}&location=${locationName}&program=${program.title.toLowerCase().replace(/\s+/g, '-')}`}
                    className="inline-flex items-center justify-center w-full bg-[#001A6E] text-white px-4 py-2.5 rounded-lg font-medium hover:bg-[#000D35] transition-colors duration-200 group"
                  >
                    Enquire Now
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProgramsSection;
