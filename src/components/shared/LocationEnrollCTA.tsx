'use client';

import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { getEnrollmentLink } from '@/data/enrollment-links';

interface LocationEnrollCTAProps {
  locationSlug: string;
  programType: 'preschool' | 'afterSchool' | 'summerCamp';
  className?: string;
  name?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
}

export default function LocationEnrollCTA({ locationSlug, programType, className = '', name, address }: LocationEnrollCTAProps) {
  const enrollmentUrl = getEnrollmentLink(locationSlug, programType);

  if (!enrollmentUrl) {
    return null;
  }

  const programTypeDisplay = {
    preschool: 'Preschool',
    afterSchool: 'After School',
    summerCamp: 'Summer Camp'
  }[programType];

  const isContactPage = enrollmentUrl === '/contact';

  // Create URL parameters for contact page
  const contactPageParams = new URLSearchParams({
    location: name || locationSlug,
    program: programTypeDisplay,
    ...(address ? {
      street: address.street,
      city: address.city,
      state: address.state,
      zip: address.zip,
      phone: address.phone,
      email: address.email
    } : {})
  }).toString();

  const finalUrl = isContactPage ? `${enrollmentUrl}?${contactPageParams}` : enrollmentUrl;

  return (
    <div className={`relative ${className}`}>
      <div className="bg-[#001A6E] rounded-3xl overflow-hidden">
        <div className="relative z-10 px-8 py-12 md:px-12">
          <div className="max-w-3xl mx-auto text-center">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Enroll in Our {programTypeDisplay} Program
            </h3>
            <p className="text-white/90 text-lg mb-8">
              Give your child the best start in life. Limited spots available for the upcoming session.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isContactPage ? (
                <Link
                  href={finalUrl}
                  className="inline-flex items-center justify-center px-8 py-3 bg-[#009990] text-white rounded-full font-medium transition-all duration-300 hover:bg-[#00b3a8] hover:translate-y-[-2px] hover:shadow-lg hover:shadow-[#009990]/30 active:translate-y-[0px]"
                >
                  <span className="flex items-center gap-2">
                    Contact Us
                    <ArrowRight className="w-5 h-5" />
                  </span>
                </Link>
              ) : (
                <a
                  href={finalUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center px-8 py-3 bg-[#009990] text-white rounded-full font-medium transition-all duration-300 hover:bg-[#00b3a8] hover:translate-y-[-2px] hover:shadow-lg hover:shadow-[#009990]/30 active:translate-y-[0px]"
                >
                  <span className="flex items-center gap-2">
                    Enroll Now
                    <ArrowRight className="w-5 h-5" />
                  </span>
                </a>
              )}
              <Link
                href="/programs"
                className="inline-flex items-center justify-center px-8 py-3 bg-white/10 backdrop-blur-sm text-white rounded-full font-medium transition-all duration-300 hover:bg-white/20"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
