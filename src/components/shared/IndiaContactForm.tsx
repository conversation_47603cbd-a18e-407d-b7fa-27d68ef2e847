'use client'

import { useState, useEffect } from 'react'
import { Phone, Mail, Send } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { trackEvent, trackContactFormConversion } from '@/utils/analytics'
import { useSafeSearchParams } from '@/components/utils/SearchParamsProvider'
import GoogleAdsConversionTracker from '@/components/analytics/GoogleAdsConversionTracker'

interface FormState {
  name: string
  email: string
  phone: string
  childAge: string
  program: string
  preferredTime: string
  message: string
  locationId: string
  source: string
}

interface IndiaContactFormProps {
  className?: string
  locationId: string
  locationName: string
}

function IndiaContactFormContent({
  searchParams,
  locationId,
  locationName,
  onSuccessfulSubmit
}: {
  searchParams: { getParam: (key: string) => string }
  locationId: string
  locationName: string
  onSuccessfulSubmit?: () => void
}) {
  const router = useRouter()

  // Get URL parameters for tracking
  const sourceParam = searchParams.getParam('source') || 'direct'
  const mediumParam = searchParams.getParam('medium') || 'website'
  const campaignParam = searchParams.getParam('campaign') || 'india_locations'

  const [status, setStatus] = useState<{
    type: 'success' | 'error' | 'info' | null
    message: string
  }>({ type: null, message: '' })

  const [formState, setFormState] = useState<FormState>({
    name: searchParams.getParam('name'),
    email: searchParams.getParam('email'),
    phone: searchParams.getParam('phone'),
    childAge: searchParams.getParam('childAge'),
    program: searchParams.getParam('program') || 'Preschool Program',
    preferredTime: searchParams.getParam('preferredTime'),
    message: searchParams.getParam('message'),
    locationId: locationId,
    source: `${locationName} Page - ${sourceParam}`
  })

  // Available programs for India locations
  const availablePrograms = [
    'Junior Pre-School (2-3 years)',
    'Preschool Program (3-4 years)',
    'Pre-K Program (4-5 years)',
    'Kindergarten (5-6 years)'
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormState(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setStatus({ type: 'info', message: 'Sending...' })

    try {
      // Track form submission attempt
      trackEvent('india_contact_form_submit_attempt', {
        location: locationId,
        source: sourceParam,
        medium: mediumParam,
        campaign: campaignParam
      })

      const response = await fetch('/api/send-india-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      })

      if (!response.ok) {
        trackEvent('india_contact_form_submit_error', {
          location: locationId,
          error: 'API response not OK'
        })
        throw new Error('Failed to send message')
      }

      // Track successful form submission
      trackEvent('india_contact_form_submit_success', {
        location: locationId,
        source: sourceParam,
        medium: mediumParam,
        campaign: campaignParam
      })

      // Track Google Ads conversion
      trackContactFormConversion(1.0)
      console.log('India contact form conversion tracked on submit')

      // Call the onSuccessfulSubmit callback if provided
      if (onSuccessfulSubmit) {
        onSuccessfulSubmit()
      }

      // Redirect to thank you page on success with country and location parameters
      router.push(`/thank-you?country=india&location=${locationId}`)
    } catch (error) {
      trackEvent('india_contact_form_submit_error', {
        location: locationId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      setStatus({
        type: 'error',
        message: 'Failed to send message. Please try again.'
      })
    }
  }

  const isSubmitting = status.type === 'info'

  return (
    <div className="w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-shadow duration-300 border border-gray-100">
      <div className="p-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#001A6E] mb-2">Enquire Now</h2>
          <p className="text-gray-600">Fill out the form below to learn more about {locationName}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-6">
            {/* Name & Email - First Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Your Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formState.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                  required
                />
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formState.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                  required
                />
              </div>
            </div>

            {/* Phone & Program - Second Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Phone */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 font-medium">
                    +91
                  </span>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formState.phone}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                    required
                  />
                </div>
              </div>

              {/* Program Selection */}
              <div>
                <label htmlFor="program" className="block text-sm font-medium text-gray-700 mb-2">
                  Program
                </label>
                <select
                  id="program"
                  name="program"
                  value={formState.program}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors bg-white"
                  required
                >
                  <option value="">Select a program</option>
                  {availablePrograms.map(program => (
                    <option key={program} value={program}>
                      {program}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Child's Age & Preferred Time - Third Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Child's Age */}
              <div>
                <label htmlFor="childAge" className="block text-sm font-medium text-gray-700 mb-2">
                  Child's Age (Optional)
                </label>
                <input
                  type="text"
                  id="childAge"
                  name="childAge"
                  value={formState.childAge}
                  onChange={handleInputChange}
                  placeholder="e.g., 3 years"
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                />
              </div>

              {/* Preferred Time */}
              <div>
                <label htmlFor="preferredTime" className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Contact Time (Optional)
                </label>
                <input
                  type="text"
                  id="preferredTime"
                  name="preferredTime"
                  value={formState.preferredTime}
                  onChange={handleInputChange}
                  placeholder="e.g., Morning, Afternoon"
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                />
              </div>
            </div>

            {/* Message - Fourth Row */}
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formState.message}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors resize-none"
                required
              />
            </div>
          </div>

          {/* Removed redundant contact info section */}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[#001A6E] text-white py-4 px-6 rounded-xl font-medium
                     hover:bg-[#002288] transition-all duration-300
                     disabled:opacity-70 disabled:cursor-not-allowed
                     focus:outline-none focus:ring-2 focus:ring-[#001A6E]/50
                     shadow-lg shadow-[#001A6E]/20 transform hover:scale-[1.02]
                     text-base"
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center gap-2">
                <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Sending...
              </span>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <Send className="w-5 h-5" />
                Send Enquiry
              </span>
            )}
          </button>
        </form>
      </div>
    </div>
  )
}

export default function IndiaContactForm({ className = '', locationId, locationName }: IndiaContactFormProps) {
  const safeSearchParams = useSafeSearchParams()
  const [showConversionTracker, setShowConversionTracker] = useState(false)

  // This will be used to conditionally render the conversion tracker
  // after a successful form submission
  useEffect(() => {
    if (showConversionTracker) {
      console.log('Rendering Google Ads conversion tracker for India contact form');
    }
  }, [showConversionTracker]);

  return (
    <div className={className}>
      {/* Google Ads Conversion Tracker - only shown after successful submission */}
      {showConversionTracker && <GoogleAdsConversionTracker />}

      <IndiaContactFormContent
        searchParams={safeSearchParams}
        locationId={locationId}
        locationName={locationName}
        onSuccessfulSubmit={() => setShowConversionTracker(true)}
      />
    </div>
  )
}
