'use client';

import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { getEnrollmentLink } from '@/data/enrollment-links';

interface EnrollmentCTAProps {
  locationSlug: string;
  programType: 'preschool' | 'afterSchool' | 'summerCamp';
}

export default function EnrollmentCTA({ locationSlug, programType }: EnrollmentCTAProps) {
  const enrollmentUrl = getEnrollmentLink(locationSlug, programType);

  if (!enrollmentUrl) {
    return null;
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-[#001A6E] rounded-3xl overflow-hidden relative">
          <div className="relative z-10 px-8 py-16">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Join Our {programType === 'preschool' ? 'Preschool' : 'After School'} Program
              </h2>
              <p className="text-white/90 text-lg mb-8 max-w-2xl mx-auto">
                Give your child the best start in life with our comprehensive early education program. 
                Limited spots available for the upcoming session.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  href={enrollmentUrl}
                  className="inline-flex items-center justify-center px-8 py-4 bg-white text-[#001A6E] rounded-xl font-semibold hover:bg-[#F8FAFC] transition-colors duration-300"
                >
                  Enroll Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link 
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour
                </Link>
              </div>
            </div>
          </div>
          
          {/* Decorative Elements */}
          <div className="absolute top-0 left-0 w-32 h-32 md:w-48 md:h-48 opacity-10">
            <div className="absolute inset-0 bg-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
          </div>
          <div className="absolute bottom-0 right-0 w-40 h-40 md:w-64 md:h-64 opacity-10">
            <div className="absolute inset-0 bg-white rounded-full transform translate-x-1/2 translate-y-1/2"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
