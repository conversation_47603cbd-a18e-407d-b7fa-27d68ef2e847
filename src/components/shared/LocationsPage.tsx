'use client'

import { Search, Building2, Clock, Shield, MapPin, Phone, School2, GraduationCap, Globe2, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

const getDirectionsUrl = (location: any) => {
  const address = encodeURIComponent(`${location.address.street}, ${location.address.city}, ${location.address.state}`);
  return `https://maps.app.goo.gl/JZybzUDUZcidQW1R7`;
};

const locations = {
  USA: [
    {
      id: 'willow-ave',
      name: 'Sunnyvale - Willow Ave',
      address: {
        street: '1159 Willow Ave',
        city: 'Sunnyvale',
        state: 'CA',
        zip: '94086',
        country: 'USA'
      },
      contact: {
        phone: '(*************'
      },
      image: '/images/locations/Willow-ave.jpg',
      type: ['preschool', 'afterSchool']
    },
    {
      id: 'lawrence-station',
      name: 'Sunnyvale - Lawrence Station Road',
      address: {
        street: '1279 Lawrence Station Rd',
        city: 'Sunnyvale',
        state: 'CA',
        zip: '94089',
        country: 'USA'
      },
      contact: {
        phone: '************'
      },
      image: '/images/locations/lawrence-station-road.png',
      type: ['preschool']
    },
    {
      id: 'el-camino',
      name: 'Santa Clara - El Camino Real',
      address: {
        street: '2280 El Camino Real',
        city: 'Santa Clara',
        state: 'CA',
        zip: '95050',
        country: 'USA'
      },
      contact: {
        phone: '************'
      },
      image: '/images/locations/El Camino Real.jpg',
      type: ['preschool']
    },
    {
      id: 'cottle-road',
      name: 'San Jose - Cottle Road',
      address: {
        street: '6097 Cottle Road',
        city: 'San Jose',
        state: 'CA',
        zip: '95123',
        country: 'USA'
      },
      contact: {
        phone: '(*************'
      },
      image: '/images/locations/Cottle Road.jpg',
      type: ['preschool']
    },
    {
      id: 'grafton-st-after',
      name: 'Dublin - Grafton St',
      address: {
        street: '4500 Grafton St',
        city: 'Dublin',
        state: 'CA',
        zip: '94568',
        country: 'USA'
      },
      contact: {
        phone: '(*************'
      },
      image: '/images/locations/Dublin.jpg',
      type: ['afterSchool']
    },
    {
      id: 'mountain-house',
      name: 'Mountain House',
      address: {
        street: '768 N Montebello St',
        city: 'Mountain House',
        state: 'CA',
        zip: '95391',
        country: 'USA'
      },
      contact: {
        phone: '(*************'
      },
      image: '/images/locations/Mountain House.jpg',
      type: ['preschool'],
      isUpcoming: true
    }
  ],
  India: [
    {
      id: 'bima-nagar',
      name: 'Indore - Bima Nagar',
      address: {
        street: '11, Bima Nagar, Anand Bazaar',
        city: 'Indore',
        state: 'MP',
        zip: '452001',
        country: 'India'
      },
      contact: {
        phone: '+91 95755 45952'
      },
      image: '/images/locations/India.jpg',
      type: ['preschool']
    },
    {
      id: 'nipania',
      name: 'Indore - Nipania',
      address: {
        street: '27, Samar Park Colony, Nipania',
        city: 'Indore',
        state: 'MP',
        zip: '452010',
        country: 'India'
      },
      contact: {
        phone: '+91 9575545200'
      },
      image: '/images/locations/nipania.jpg',
      type: ['preschool']
    }
  ]
};

export default function LocationsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'preschool' | 'afterSchool'>('all');
  const [selectedCountry, setSelectedCountry] = useState<'all' | 'usa' | 'india'>('all');

  const getLocationsByCountry = () => {
    switch (selectedCountry) {
      case 'usa':
        return locations.USA;
      case 'india':
        return locations.India;
      default:
        return [...locations.USA, ...locations.India];
    }
  };

  const filteredLocations = getLocationsByCountry().filter((location) => {
    if (!location) return false;

    const searchTerms = searchQuery.toLowerCase().trim();

    // If no search query, just filter by type
    if (!searchTerms) {
      return activeTab === 'all' || (location.type && location.type.includes(activeTab));
    }

    // Search across all relevant fields
    const searchableFields = [
      location.name,
      location.address?.street,
      location.address?.city,
      location.address?.state,
      location.address?.zip,
      location.contact?.phone
    ].filter(Boolean); // Remove any undefined fields

    const matchesSearch = searchableFields.some(field =>
      field.toLowerCase().includes(searchTerms)
    );

    const matchesType = activeTab === 'all' || (location.type && location.type.includes(activeTab));

    return matchesSearch && matchesType;
  });

  const renderLocations = () => {
    if (selectedCountry === 'all') {
      return (
        <>
          {/* USA Locations */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-[#074799] mb-8">United States</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredLocations
                .filter(loc => loc.address.state === 'CA')
                .map((location) => (
                  <LocationCard key={location.id} location={location} />
                ))}
            </div>
          </div>

          {/* India Locations */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-[#074799] mb-8">India</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredLocations
                .filter(loc => loc.address.country === 'India')
                .map((location) => (
                  <LocationCard key={location.id} location={location} />
                ))}
            </div>
          </div>
        </>
      );
    }

    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredLocations.map((location) => (
          <LocationCard key={location.id} location={location} />
        ))}
      </div>
    );
  };

  const LocationCard = ({ location }: { location: any }) => {
    const hasPreSchool = location.type.includes('preschool');
    const hasAfterSchool = location.type.includes('afterSchool');
    const country = location.address.country === 'India' ? 'india' : 'usa';
    const preSchoolUrl = `/locations/${country}/${location.id}`;
    const afterSchoolUrl = location.id === 'grafton-st-after'
      ? `/locations/${country}/grafton-st-after`
      : `/locations/${country}/${location.id}-after`;

    return (
      <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden h-full flex flex-col">
        <div className="relative h-[240px]">
          <Image
            src={location.image}
            alt={location.name}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />
          <div className="absolute top-4 right-4 flex flex-col gap-2">
            {location.isUpcoming && (
              <span className="px-4 py-2 text-sm bg-[#e8f5e9]/90 backdrop-blur-sm text-[#009990] rounded-full shadow-md font-medium">
                Opening Soon
              </span>
            )}
          </div>
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-2xl font-bold text-white mb-2 drop-shadow-sm">{location.name}</h3>
            <div className="flex flex-wrap gap-2">
              {hasPreSchool && (
                <span className="px-3 py-1 text-sm bg-[#001A6E]/90 backdrop-blur-sm text-white rounded-full shadow-md font-medium">
                  PreSchool
                </span>
              )}
              {hasAfterSchool && (
                <span className="px-3 py-1 text-sm bg-[#009990]/90 backdrop-blur-sm text-white rounded-full shadow-md font-medium">
                  After School
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="p-6 flex-grow flex flex-col">
          <div className="space-y-3 mb-6 flex-grow">
            <div className="flex items-start text-gray-600">
              <MapPin className="w-5 h-5 mr-2 mt-1 flex-shrink-0 text-gray-400" />
              <div>
                <p>{location.address.street}</p>
                <p>{location.address.city}, {location.address.state} {location.address.zip}</p>
              </div>
            </div>
            <div className="flex items-center text-[#009990]">
              <Phone className="w-5 h-5 mr-2" />
              <a href={`tel:${location.contact.phone}`} className="hover:text-[#074799]">
                {location.contact.phone}
              </a>
            </div>
          </div>
          <div className="flex flex-col gap-3 mt-auto">
            {hasPreSchool && (
              <Link
                href={preSchoolUrl}
                className="inline-flex items-center justify-center bg-[#001A6E] text-white px-6 py-2.5 rounded-lg font-medium hover:bg-[#001A6E]/90 transition-all duration-200 shadow-md hover:shadow-lg group"
              >
                View PreSchool Program
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            )}
            {hasAfterSchool && (
              <Link
                href={afterSchoolUrl}
                className="inline-flex items-center justify-center bg-[#009990] text-white px-6 py-2.5 rounded-lg font-medium hover:bg-[#009990]/90 transition-all duration-200 shadow-md hover:shadow-lg group"
              >
                View After School Program
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-[#001A6E]/90 rounded-[48px] mx-8 mt-8 mb-16 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/locations/san-francisco.jpg"
            alt="Tulip Kids Locations"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E] via-[#001A6E]/95 to-transparent"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-8 pt-32 pb-20">
          <div className="max-w-4xl">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-3 text-sm mb-8">
              <Link href="/" className="text-white/60 hover:text-white transition-colors">
                Home
              </Link>
              <ArrowRight className="w-4 h-4 text-white/60" />
              <span className="text-white">Locations</span>
            </nav>

            <div className="text-white space-y-8">
              {/* Badge */}
              <div className="inline-block">
                <span className="bg-[#009990] text-white px-6 py-2 rounded-full text-sm font-medium">
                  Our Locations
                </span>
              </div>

              {/* Main Content */}
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  Find a Tulip Kids Center Near You
                </h1>
                <p className="text-xl text-white/80 leading-relaxed max-w-3xl">
                  With multiple locations across the San Francisco Bay Area and India, we're bringing premium early education to more families every day.
                </p>
              </div>

              {/* Search Bar */}
              <div className="max-w-2xl">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search by city, zip code or location name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 rounded-full bg-white/10 backdrop-blur-sm text-white placeholder-white/60 border border-white/20 focus:outline-none focus:border-white/40 transition-colors"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="container mx-auto px-4 mb-8">
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex gap-4">
            <button
              onClick={() => setActiveTab('all')}
              className={`px-4 py-2 rounded-full ${activeTab === 'all'
                  ? 'bg-[#001A6E] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              All Centers
            </button>
            <button
              onClick={() => setActiveTab('preschool')}
              className={`px-4 py-2 rounded-full ${activeTab === 'preschool'
                  ? 'bg-[#009990] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              Preschools
            </button>
            <button
              onClick={() => setActiveTab('afterSchool')}
              className={`px-4 py-2 rounded-full ${activeTab === 'afterSchool'
                  ? 'bg-[#001A6E] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              After School
            </button>
          </div>

          <div className="flex gap-4">
            <button
              onClick={() => setSelectedCountry('all')}
              className={`px-4 py-2 rounded-full ${selectedCountry === 'all'
                  ? 'bg-[#001A6E] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              All Countries
            </button>
            <button
              onClick={() => setSelectedCountry('usa')}
              className={`px-4 py-2 rounded-full ${selectedCountry === 'usa'
                  ? 'bg-[#009990] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              United States
            </button>
            <button
              onClick={() => setSelectedCountry('india')}
              className={`px-4 py-2 rounded-full ${selectedCountry === 'india'
                  ? 'bg-[#009990] text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              India
            </button>
          </div>
        </div>
      </section>

      {/* Locations Grid */}
      <section className="container mx-auto px-4 pb-20">
        {renderLocations()}
      </section>
    </main>
  );
}
