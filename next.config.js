/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['images.unsplash.com', 'unsplash.com', 'blogs.aicipherleads.xyz'],
    unoptimized: true,
  },
  // Disable static generation completely
  staticPageGenerationTimeout: 1,
  // Server Actions are available by default now
  experimental: {},
  // Disable ESLint during build to avoid issues
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig
