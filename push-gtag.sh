#!/bin/bash

echo "GitHub Push Helper for Google Ads Integration"
echo "==========================================="
echo ""

# Check if the repository exists
if [ ! -d ".git" ]; then
  echo "Error: This doesn't appear to be a Git repository."
  echo "Please run this script from the root of your Git repository."
  exit 1
fi

# Get the current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo "Current branch: $CURRENT_BRANCH"
echo ""

echo "Please enter your GitHub credentials:"
read -p "Username: " USERNAME
read -sp "Personal Access Token: " TOKEN
echo ""

# Set the remote URL with credentials
REMOTE_URL="https://$USERNAME:$<EMAIL>/hyperlinqai/tulip-kids.git"
git remote set-url origin "$REMOTE_URL"

echo ""
echo "Attempting to push to GitHub..."
git push origin $CURRENT_<PERSON>ANCH

# Reset the remote URL to remove the token (for security)
git remote set-url origin "https://github.com/hyperlinqai/tulip-kids.git"

echo ""
echo "Push attempt completed."
echo "If successful, your Google Ads integration is now on GitHub."
echo "If unsuccessful, please check the error message above."
