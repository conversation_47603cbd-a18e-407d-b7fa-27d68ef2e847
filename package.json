{"name": "tulip-new-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "node build.js", "build:next": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.2", "@types/nodemailer": "^6.4.17", "axios": "^1.7.9", "clsx": "^2.1.1", "embla-carousel": "^8.5.2", "embla-carousel-react": "^8.5.2", "express": "^4.21.2", "framer-motion": "^11.17.0", "js": "^0.1.0", "lucide-react": "0.294.0", "medium-zoom": "^1.1.0", "next": "^14.0.4", "nodemailer": "^6.9.16", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "20.10.5", "@types/react": "18.2.43", "@types/react-dom": "18.2.17", "autoprefixer": "10.4.16", "eslint": "8.55.0", "eslint-config-next": "14.0.4", "mini-css-extract-plugin": "^2.9.2", "postcss": "8.4.32", "tailwindcss": "3.3.6", "typescript": "5.3.3"}, "engines": {"node": ">=18.0.0"}}