# Google Tag Manager Setup for Tulip Kids Website

This document explains how Google Tag Manager (GTM) is implemented in the Tulip Kids website and how to use it to track events.

## Implementation

The website uses Google Tag Manager to track user interactions and page views. The implementation consists of:

1. **GTM Container**: The GTM container is loaded in the `<head>` section of the website using Next.js's `Script` component.
2. **DataLayer**: A dataLayer is initialized before the GTM container to ensure all events are captured.
3. **Event Tracking**: Custom events are pushed to the dataLayer to track user interactions.
4. **Page View Tracking**: A dedicated client component tracks page views as users navigate the site.

## Components

- **GoogleAnalyticsScript.tsx**: Loads the GTM and GA4 scripts in the document head (server component)
- **PageViewTracker.tsx**: Client component that tracks page views as users navigate the site (wrapped in Suspense)
- **utils/analytics.ts**: Contains utility functions for tracking events and page views

## Important Implementation Details

1. **Suspense Boundaries**: All components using client-side hooks like `useSearchParams()` are wrapped in Suspense boundaries to avoid static generation errors
2. **Dynamic Rendering**: The app is configured to use dynamic rendering with `export const dynamic = 'force-dynamic'` to avoid issues with static generation
3. **Client/Server Component Separation**: We've carefully separated client and server components to ensure proper hydration

## Configuration

### Environment Variables

The following environment variables are used for GTM and GA4:

- `NEXT_PUBLIC_GTM_ID`: The Google Tag Manager container ID (default: 'GTM-M4BD655F')
- `NEXT_PUBLIC_GA4_MEASUREMENT_ID`: The Google Analytics 4 measurement ID (default: 'G-MC8BXC5CXB')

These can be set in your `.env.local` file or in your hosting environment.

## Tracked Events

The website tracks the following events:

### Page Views

- **Event Name**: `pageview`
- **Data**:
  - `page`: The current page URL

### Form Interactions

- **Event Name**: `contact_form_country_change`
  - **Data**: `country`: The selected country (usa/india)

- **Event Name**: `contact_form_location_select`
  - **Data**:
    - `country`: The selected country
    - `location`: The selected location

- **Event Name**: `contact_form_program_select`
  - **Data**:
    - `country`: The selected country
    - `location`: The selected location
    - `program`: The selected program

### Form Submissions

- **Event Name**: `contact_form_submit_attempt`
  - **Data**:
    - `country`: The selected country
    - `location`: The selected location
    - `program`: The selected program
    - `childAge`: The child's age

- **Event Name**: `contact_form_submit_success`
  - **Data**:
    - `country`: The selected country
    - `location`: The selected location
    - `program`: The selected program

- **Event Name**: `contact_form_submit_error`
  - **Data**:
    - `country`: The selected country
    - `error`: The error message

## Adding New Events

To add new event tracking to a component:

1. Import the tracking function:
   ```tsx
   import { trackEvent } from '@/utils/analytics';
   ```

2. Call the function with an event name and optional data:
   ```tsx
   trackEvent('event_name', {
     property1: 'value1',
     property2: 'value2'
   });
   ```

## Verifying Events in GTM

To verify that events are being sent to GTM:

1. Open your website with the GTM Debug mode enabled
2. Use the GTM Preview mode to see events as they happen
3. Check the Network tab in your browser's developer tools for requests to `https://www.google-analytics.com/g/collect` and `https://www.googletagmanager.com`

## Troubleshooting

If events are not being tracked:

1. Check that the GTM container ID is correct in your environment variables
2. Verify that the GTM container is loaded by checking the Network tab
3. Make sure the dataLayer is initialized before any events are pushed
4. Check for JavaScript errors in the console that might prevent event tracking
