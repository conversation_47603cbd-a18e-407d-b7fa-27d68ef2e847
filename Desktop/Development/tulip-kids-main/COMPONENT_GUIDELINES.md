# Component-Specific Guidelines

## LocationDirectory.tsx
- Maintain the current card layout with image, details, and "Get Directions" button
- Keep the search functionality with clear button (X icon)
- Preserve the filter system for countries and programs
- Maintain the responsive grid layout
- Keep the "Opening Soon" badge for upcoming locations
- Preserve all map links and their positioning

## WhyChooseTulip.tsx
- Keep the 6-benefit layout in a 3-column grid
- Maintain the current card design with icons and hover effects
- Preserve the color scheme for each benefit type
- Keep the trust badge at the bottom
- Maintain the centered section title and description
- Preserve the gradient background

## Navigation
- Keep the current header layout and styling
- Maintain the mobile menu functionality
- Preserve the dropdown behaviors
- Keep the current scroll behaviors

## Hero Section
- Maintain the current gradient background
- Keep the centered content layout
- Preserve the current typography scale
- Maintain the current spacing and padding

## Contact Form
- Keep the current form layout and validation
- Maintain the input field styling
- Preserve the submit button design
- Keep the current error handling display

## Footer
- Maintain the current column layout
- Keep the social media links styling
- Preserve the newsletter signup form
- Maintain the copyright notice layout

## Daily Schedule
- Keep the current timeline display
- Maintain the activity cards design
- Preserve the time slot formatting
- Keep the current responsive behavior

## Parent Testimonials
- Maintain the current slider functionality
- Keep the testimonial card design
- Preserve the navigation controls
- Maintain the background styling

## FAQ Section
- Keep the current accordion design
- Maintain the expand/collapse animations
- Preserve the question/answer layout
- Keep the current spacing and typography

## Notes:
1. All components should maintain their current responsive breakpoints
2. Color schemes should remain consistent across all components
3. Animation timings and effects should be preserved
4. Typography hierarchy should be maintained
5. Spacing and padding should follow the current system
6. Interactive elements should keep their current hover/focus states
7. Image aspect ratios and sizes should be preserved
8. Shadow and elevation hierarchy should be maintained

DO NOT modify these designs unless explicitly requested. This serves as a reference for maintaining design consistency across the website.
