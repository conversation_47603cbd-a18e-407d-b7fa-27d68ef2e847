import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        'xs': '480px',
      },
      colors: {
        'primary': {
          '50': '#f5f8ff',
          '100': '#ebf1ff',
          '200': '#d7e3ff',
          '300': '#b3c9ff',
          '400': '#809fff',
          '500': '#4d75ff',
          '600': '#1a4bff',
          '700': '#0033e6',
          '800': '#0029b3',
          '900': '#001f80',
        },
        'secondary': {
          '50': '#fff7f5',
          '100': '#ffefeb',
          '200': '#ffd7cc',
          '300': '#ffb3a3',
          '400': '#ff8066',
          '500': '#ff4d29',
          '600': '#e63600',
          '700': '#b32b00',
          '800': '#802000',
          '900': '#4d1300',
        },
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-in-up': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
      },
      animation: {
        'fade-in': 'fade-in 0.6s ease-out',
        'fade-in-up': 'fade-in-up 0.6s ease-out',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}

export default config
