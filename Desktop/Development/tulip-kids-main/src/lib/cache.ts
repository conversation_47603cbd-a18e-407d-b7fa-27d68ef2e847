import { unstable_cache } from 'next/cache';

// Cache key prefixes
const CACHE_KEYS = {
  LOCATIONS: 'locations',
  PROGRAMS: 'programs',
  TESTIMONIALS: 'testimonials',
  BLOG_POSTS: 'blog-posts',
} as const;

// Cache tags for revalidation
export const CACHE_TAGS = {
  LOCATIONS: 'locations',
  PROGRAMS: 'programs',
  TESTIMONIALS: 'testimonials',
  BLOG: 'blog',
} as const;

// Default cache duration (24 hours in seconds)
const DEFAULT_CACHE_DURATION = 60 * 60 * 24;

// Cache configuration for different data types
const cacheConfig = {
  locations: {
    revalidate: DEFAULT_CACHE_DURATION,
    tags: [CACHE_TAGS.LOCATIONS],
  },
  programs: {
    revalidate: DEFAULT_CACHE_DURATION * 7, // 7 days for programs
    tags: [CACHE_TAGS.PROGRAMS],
  },
  testimonials: {
    revalidate: DEFAULT_CACHE_DURATION * 3, // 3 days for testimonials
    tags: [CACHE_TAGS.TESTIMONIALS],
  },
  blogPosts: {
    revalidate: 3600, // 1 hour for blog posts
    tags: [CACHE_TAGS.BLOG],
  },
} as const;

// Type for cache options
type CacheOptions = {
  revalidate?: number;
  tags?: string[];
};

// Generic cache wrapper function
export async function cachedFetch<T>(
  key: string,
  fetchFn: () => Promise<T>,
  options: CacheOptions = {}
): Promise<T> {
  const { revalidate, tags } = options;

  return unstable_cache(
    fetchFn,
    [key],
    {
      revalidate,
      tags,
    }
  )();
}

// Specific cache functions for different data types
export const cacheHelpers = {
  locations: {
    async get(fetchFn: () => Promise<any>) {
      return unstable_cache(
        fetchFn,
        [CACHE_KEYS.LOCATIONS],
        {
          revalidate: DEFAULT_CACHE_DURATION,
          tags: [CACHE_TAGS.LOCATIONS],
        }
      )();
    },
    async invalidate() {
      // Implement cache invalidation logic here
      // This will be used when location data is updated
    },
  },
  programs: {
    async get(fetchFn: () => Promise<any>) {
      return unstable_cache(
        fetchFn,
        [CACHE_KEYS.PROGRAMS],
        {
          revalidate: DEFAULT_CACHE_DURATION * 7, // 7 days for programs
          tags: [CACHE_TAGS.PROGRAMS],
        }
      )();
    },
    async invalidate() {
      // Implement cache invalidation logic here
      // This will be used when program data is updated
    },
  },
  testimonials: {
    async get(fetchFn: () => Promise<any>) {
      return unstable_cache(
        fetchFn,
        [CACHE_KEYS.TESTIMONIALS],
        {
          revalidate: DEFAULT_CACHE_DURATION * 3, // 3 days for testimonials
          tags: [CACHE_TAGS.TESTIMONIALS],
        }
      )();
    },
    async invalidate() {
      // Implement cache invalidation logic here
      // This will be used when testimonial data is updated
    },
  },
  blogPosts: {
    async get(fetchFn: () => Promise<any>) {
      return unstable_cache(
        fetchFn,
        [CACHE_KEYS.BLOG_POSTS],
        {
          revalidate: 3600, // 1 hour for blog posts
          tags: [CACHE_TAGS.BLOG],
        }
      )();
    },
    async invalidate() {
      // Implement cache invalidation logic here
      // This will be used when blog post data is updated
    },
  },
};
