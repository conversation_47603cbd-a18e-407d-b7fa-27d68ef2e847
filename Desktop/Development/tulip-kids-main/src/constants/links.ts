export const socialLinks = [
  { id: 'facebook', Icon: 'Facebook', href: 'https://www.facebook.com/profile.php?id=100057178641270', label: 'Follow us on Facebook' },
  { id: 'instagram', Icon: 'Instagram', href: 'https://www.instagram.com/tulipkidsintl/', label: 'Follow us on Instagram' }
];

export const programLinks = [
  { id: 'infant-care', label: 'Infant Care', href: '/programs/infant-care' },
  { id: 'jr-preschool', label: 'Jr. Preschool', href: '/programs/jr-preschool' },
  { id: 'preschool', label: 'Preschool', href: '/programs/preschool' },
  { id: 'pre-k', label: 'Pre-K', href: '/programs/pre-k' }
];

export const resourceLinks = [
  { id: 'about', label: 'About Us', href: '/about' },
  { id: 'locations', label: 'Our Locations', href: '/locations' },
  { id: 'blog', label: 'Blog', href: '/blog' },
  // { id: 'parent-portal', label: 'Parent Portal', href: '/parent-portal' },
  { id: 'careers', label: 'Careers', href: '/careers' },
  { id: 'contact', label: 'Contact Us', href: '/contact' },
  { id: 'faq', label: 'FAQ', href: '/faq' },
  { id: 'refund-policy', label: 'Refund Policy', href: '/refund-policy' },
  { id: 'franchise', label: 'Partner with us', href: '/franchise' }
];

export const legalLinks = [
  { id: 'privacy', label: 'Privacy Policy', href: '/privacy-policy' },
  { id: 'terms', label: 'Terms & Conditions', href: '/terms' },
  { id: 'cookies', label: 'Cookie Policy', href: '/cookie-policy' },
  { id: 'sitemap', label: 'Sitemap', href: '/sitemap' }
];