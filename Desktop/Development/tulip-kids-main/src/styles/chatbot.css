body {
  font-family: Arial, sans-serif;
}

#chatbot-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 65px;
  height: 65px;
  background-color: #3B82F6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  overflow: visible;
  z-index: 2147483646;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

#chatbot-toggle:hover {
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  background-color: #2563EB;
}

#chatbot-toggle svg {
  width: 32px;
  height: 32px;
  fill: white;
  flex-shrink: 0;
  pointer-events: none;
}

#chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 425px;
  height: 450px;
  background-color: #3B82F6;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 2147483647;
}

#chatbox {
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  background-color: white;
}

.chat-user {
  text-align: right;
  color: #3B82F6;
  font-size: 16px;
  line-height: 24px;
  margin: 12px 0;
}

.chat-assistant {
  position: relative;
  max-width: 315px;
  text-align: left;
  color: #0D082C;
  font-size: 16px;
  line-height: 22px;
  padding-left: 30px;
  margin: 12px 0;
}

#chat-form {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #E0E0E0;
  background-color: #F5F5F5;
}

#chat-input {
  background-color: transparent;
  color: #0D082C99;
  font-size: 16px;
  font-weight: 600;
  line-height: 24.34px;
  flex-grow: 1;
  border: 0;
  outline: 0;
  border-radius: 0;
  padding: 0;
  margin-right: 10px;
}

#chat-input::placeholder {
  color: #0D082C99;
  font-size: 16px;
  font-weight: 600;
  line-height: 24.34px;
}

#chat-input:focus {
  color: #000000;
}

.chatbot-header {
  color: white;
  background-color: #3B82F6;
  padding: 16px;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px 16px 0 0;
}

.chatbot-header-line {
  display: flex;
  gap: 12px;
}

.chatbot-header-line .close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.chatbot-header-line .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.chatbot-header-line .close-btn svg {
  width: 14px;
  height: 14px;
}

.chat-box-title-line {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0 0 2px 0;
}

.chat-box-title {
  flex: 1;
  font-family: "Titillium Web", -apple-system, BlinkMacSystemFont, Arial, sans-serif;
  font-size: 18px;
  line-height: 27.38px;
  font-weight: 700;
  margin: 0;
  color: white !important;
}

.chatbot-header p {
  font-size: 14px;
  line-height: 18.2px;
  font-weight: 400;
  margin: 0;
  color: white;
}

.chat-assistant::before {
  position: absolute;
  top: 0;
  left: 0;
  content: "🤖";
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 16px;
  background-color: #EFF6FF;
  border-radius: 50%;
  vertical-align: middle;
}

#intro {
  bottom: 300px;
  color: white;
  top: auto;
}

#messagebtn {
  width: 40px !important;
  height: 40px !important;
  background-color: #3B82F6 !important;
  border-radius: 50% !important;
  border: none !important;
  cursor: pointer !important;
  overflow: visible !important;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 !important;
  transition: background-color 0.2s ease !important;
}

#messagebtn:hover {
  background-color: #2563EB !important;
}

#messagebtn svg {
  width: 22px;
  height: 22px;
  fill: white;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
}

.loading-dots > div {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #3B82F6;
  border-radius: 50%;
  animation: loading-animation 1.4s infinite ease-in-out both;
}

.loading-dots > div:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots > div:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-animation {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.loading-dots span {
  margin-left: 5px;
  color: #3B82F6;
  font-size: 14px;
}

.tour-booking {
  background-color: #10B981;
  padding: 12px;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: block;
  transition: background-color 0.2s ease;
}

.tour-booking:hover {
  background-color: #059669;
}

.tour-booking a {
  color: white;
  text-decoration: none;
  display: block;
  cursor: pointer;
}

.tour-booking a:hover {
  text-decoration: underline;
}

#calendly-widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 425px;
  height: 450px;
  z-index: 100;
  display: none;
  overflow: auto;
}

#back-to-chatbot {
  position: absolute;
  top: 30px;
  right: 10px;
  z-index: 105;
  background-color: #3B82F6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

#back-to-chatbot:hover {
  background-color: #2563EB;
}

#loc {
  position: fixed;
  bottom: 420px;
  right: 20px;
  color: white;
  background-color: #3B82F6 !important;
  padding: 16px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 10px !important;
  border-top-right-radius: 10px !important;
  width: 425px;
  text-align: center;
  z-index: 2147483647;
  font-size: 18px;
}

.location-button {
  font-size: 24px;
  height: 210px;
  width: 425px;
  background-color: #3B82F6 !important;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 20px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  z-index: 2147483647;
  transition: background-color 0.2s ease;
}

.location-button:hover {
  background-color: #2563EB !important;
}

#btn1 {
  position: fixed;
  bottom: 230px;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}

#btn2 {
  position: fixed;
  bottom: 20px;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}

@media screen and (max-width: 768px) {
  #chat-widget {
    position: fixed;
    bottom: 50px;
  }
}

@media screen and (max-width: 490px) {
  #chat-widget {
    width: 346px;
    max-width: none;
    bottom: 8px;
    right: 8px;
  }
}

@media screen and (max-width: 386px) {
  #chat-widget {
    width: 290px;
  }
  .chat-box-title {
    font-size: 16px;
    line-height: 20px;
  }
  .chatbot-header p {
    font-size: 12px;
    line-height: 16.2px;
  }
  #chat-input,
  .chat-assistant,
  .chat-user {
    font-size: 14px;
  }
  .chat-assistant::before {
    width: 20px;
    height: 20px;
  }
  #chat-form {
    padding: 6px 16px;
  }
}