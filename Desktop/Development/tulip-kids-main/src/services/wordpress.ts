import { WordPressBlogPost, BlogPost } from '@/types/blog'
import { mockBlogPosts } from '@/data/mockBlogPosts'

const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://blogs.aicipherleads.xyz/wp-json/wp/v2'
const USE_MOCK_DATA = true // Set to false when WordPress API is working

// Estimate read time based on content length
function estimateReadTime(content: string): string {
  const wordsPerMinute = 200
  const words = content.split(/\s+/).length
  const minutes = Math.ceil(words / wordsPerMinute)
  return `${minutes} min read`
}

// Strip HTML tags from content
function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '')
}

// Transform WordPress post to our BlogPost format
function transformPost(wpPost: WordPressBlogPost): BlogPost {
  const featuredMedia = wpPost._embedded?.['wp:featuredmedia']?.[0]
  const imageUrl = featuredMedia?.source_url || '/images/blog/default.jpg'

  return {
    id: wpPost.id,
    title: wpPost.title.rendered,
    excerpt: stripHtml(wpPost.excerpt.rendered),
    content: wpPost.content.rendered,
    image: imageUrl,
    date: wpPost.date,
    readTime: estimateReadTime(stripHtml(wpPost.content.rendered)),
    category: 'Education', // You might want to fetch this from WordPress categories
    slug: wpPost.slug
  }
}

export async function getBlogPosts(): Promise<BlogPost[]> {
  // If USE_MOCK_DATA is true, return mock data instead of fetching from WordPress
  if (USE_MOCK_DATA) {
    console.log('Using mock blog data')
    return mockBlogPosts
  }

  try {
    const response = await fetch(
      `${WORDPRESS_API_URL}/posts?_embed&per_page=9`,
      { next: { revalidate: 3600 } } // Revalidate every hour
    )

    if (!response.ok) {
      console.error('WordPress API returned an error:', response.status)
      console.log('Falling back to mock data')
      return mockBlogPosts
    }

    const posts: WordPressBlogPost[] = await response.json()
    return posts.map(transformPost)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    console.log('Falling back to mock data due to error')
    return mockBlogPosts // Return mock data in case of error
  }
}
