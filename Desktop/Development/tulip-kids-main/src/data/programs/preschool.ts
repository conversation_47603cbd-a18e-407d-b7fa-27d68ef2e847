export interface Program {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  ageRange?: string;
  features: {
    title: string;
    description: string;
  }[];
  curriculum: {
    title: string;
    items: string[];
  }[];
  schedule?: {
    days: string;
    hours: string;
  };
  learningOutcomes: string[];
  icons?: {
    icon: string;
    label: string;
  }[];
  gallery?: {
    src: string;
    alt: string;
  }[];
}

export const preschoolPrograms: Program[] = [
  {
    id: 'pre-kindergarten',
    title: 'Pre-Kindergarten Program',
    subtitle: 'Preparing Your Child for Kindergarten Success',
    description: 'Tulip Kids Academy offers a "High Scope" academic Pre-K curriculum for children ages 4 to 5 years old. Our Pre-K Program is structured with the whole child approach in mind. Through the implementation of Saxon math, science, spelling, phonics and teacher provided social studies, thematic lesson plans and proper guidance; we are able to assure your child is fully equipped and ready to spread their wings into the big world of KINDERGARTEN.',
    ageRange: '4-5 years',
    features: [
      {
        title: 'Comprehensive Curriculum',
        description: 'Saxon math, science, spelling, phonics, and social studies'
      },
      {
        title: 'Whole Child Approach',
        description: 'Focus on academic, social, and emotional development'
      },
      {
        title: 'Kindergarten Readiness',
        description: 'Structured preparation for successful transition to kindergarten'
      },
      {
        title: 'Literacy Development',
        description: 'Strong focus on reading, writing, and language skills'
      }
    ],
    curriculum: [
      {
        title: 'Core Subjects',
        items: [
          'Saxon Mathematics',
          'Science Exploration',
          'Phonics & Reading',
          'Spelling Fundamentals',
          'Social Studies'
        ]
      },
      {
        title: 'Development Areas',
        items: [
          'Personal Skills',
          'Social Interaction',
          'Independence Building',
          'Creative Expression',
          'Life Skills'
        ]
      }
    ],
    learningOutcomes: [
      'Strong foundation in basic mathematics',
      'Developed reading and writing skills',
      'Enhanced social interaction abilities',
      'Improved independence and self-confidence',
      'Creative thinking and problem-solving skills',
      'Prepared for kindergarten curriculum'
    ],
    gallery: [
      {
        src: '/images/programs/pre-k/classroom.jpg',
        alt: 'Pre-K Classroom Environment'
      },
      {
        src: '/images/programs/pre-k/learning.jpg',
        alt: 'Children Engaged in Learning'
      }
    ]
  },
  {
    id: 'preschool',
    title: 'Preschool Programs',
    subtitle: 'Building Strong Foundations for Learning',
    description: 'Our Preschool Program focuses on developing fundamental skills through play-based learning and structured activities. We create an environment where children can explore, discover, and grow while building essential academic and social skills.',
    ageRange: '3-4 years',
    features: [
      {
        title: 'Play-Based Learning',
        description: 'Interactive activities that make learning fun and engaging'
      },
      {
        title: 'Social Development',
        description: 'Opportunities to build friendships and social skills'
      },
      {
        title: 'Basic Academics',
        description: 'Introduction to numbers, letters, and concepts'
      },
      {
        title: 'Creative Expression',
        description: 'Art, music, and movement activities'
      }
    ],
    curriculum: [
      {
        title: 'Core Learning Areas',
        items: [
          'Early Mathematics',
          'Language Development',
          'Science Discovery',
          'Arts and Crafts',
          'Physical Development'
        ]
      }
    ],
    learningOutcomes: [
      'Basic number and letter recognition',
      'Improved communication skills',
      'Enhanced motor skills',
      'Social interaction abilities',
      'Creative expression'
    ],
    gallery: [
      {
        src: '/images/programs/preschool/play.jpg',
        alt: 'Preschool Play Area'
      }
    ]
  },
  {
    id: 'jr-preschool',
    title: 'Jr. Preschool Program',
    subtitle: 'Early Learning Adventures',
    description: 'The Junior Preschool Program introduces young children to structured learning through play and exploration. We focus on developing basic skills and social interaction in a nurturing environment.',
    ageRange: '2-3 years',
    features: [
      {
        title: 'Gentle Introduction',
        description: 'Smooth transition into structured learning'
      },
      {
        title: 'Basic Skills',
        description: 'Development of fundamental abilities'
      },
      {
        title: 'Social Interaction',
        description: 'Learning to play and share with others'
      }
    ],
    curriculum: [
      {
        title: 'Development Areas',
        items: [
          'Basic Communication',
          'Motor Skills',
          'Social Skills',
          'Creative Play',
          'Sensory Activities'
        ]
      }
    ],
    learningOutcomes: [
      'Improved communication',
      'Better motor skills',
      'Enhanced social interaction',
      'Basic concept understanding',
      'Increased independence'
    ],
    gallery: [
      {
        src: '/images/programs/jr-preschool/play.jpg',
        alt: 'Junior Preschool Activities'
      }
    ]
  },
  {
    id: 'infant-toddler',
    title: 'Infant/Toddlers Program',
    subtitle: 'Nurturing Early Development',
    description: 'Our Infant/Toddler Program provides a safe, nurturing environment for our youngest learners. We focus on supporting developmental milestones while providing loving care and attention.',
    ageRange: '6 weeks - 2 years',
    features: [
      {
        title: 'Individual Care',
        description: 'Personalized attention for each child'
      },
      {
        title: 'Development Support',
        description: 'Activities to support growth milestones'
      },
      {
        title: 'Safe Environment',
        description: 'Secure and nurturing atmosphere'
      }
    ],
    curriculum: [
      {
        title: 'Development Focus',
        items: [
          'Sensory Development',
          'Motor Skills',
          'Language Development',
          'Social Interaction',
          'Emotional Growth'
        ]
      }
    ],
    learningOutcomes: [
      'Achievement of developmental milestones',
      'Basic communication skills',
      'Motor skill development',
      'Social awareness',
      'Emotional security'
    ],
    gallery: [
      {
        src: '/images/programs/infant-toddler/care.jpg',
        alt: 'Infant Care Environment'
      }
    ]
  }
];
