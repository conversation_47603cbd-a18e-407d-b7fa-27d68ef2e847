interface Director {
  name: string;
  role: string;
  image: string;
  bio: string[];
}

export const directors: { [key: string]: Director } = {
  'willow-ave': {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Center Director',
    image: '/images/team/<PERSON><PERSON>ine <PERSON>.jpg',
    bio: [
      '<PERSON><PERSON><PERSON> is a passionate teacher and administrator. She has a background in teaching secondary school, and her experiences working with young children are evident in her current management position at a school in Silicon Valley.',
      'Ms<PERSON> holds a Bachelor of Arts Degree, a Bachelor of Science in Education (BSE), and a Master of Arts in Education (MA). She has furthered her education through various institutions in the United States, including De Anza / Foothill College, San Jose State University, and UC Davis, specializing in Child Development.',
      'Her strong belief system, nurtured by her grandmother\'s influence, inspired her compassionate and organized approach to teaching and leadership.',
      'Ms<PERSON> enjoys attending conferences and seminars across the USA constantly seeking to improve her skills and stay current in the field. She has been at Tulip Kids Academy for more than a decade.'
    ]
  }
};
