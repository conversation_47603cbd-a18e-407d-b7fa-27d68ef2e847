interface LocationDetail {
  shortName: string;
  fullAddress: string;
  label: string;
}

export const usaLocationMappings: Record<string, LocationDetail> = {
  'willow-ave': {
    shortName: 'Willow Ave',
    fullAddress: '1159 Willow Ave, Sunnyvale CA 94086, United States',
    label: 'Sunnyvale - Willow Ave'
  },
  'lawrence-station': {
    shortName: 'Lawrence Station',
    fullAddress: '1279 Lawrence Station Rd, Sunnyvale CA 94089, United States',
    label: 'Sunnyvale - Lawrence Station'
  },
  'el-camino': {
    shortName: 'El Camino Real',
    fullAddress: '2280 El Camino Real, Santa Clara, CA 95050, United States',
    label: 'Santa Clara - El Camino Real'
  },
  'cottle-road': {
    shortName: 'Cottle Road',
    fullAddress: '6097 Cottle Rd, San Jose, CA 95123, United States',
    label: 'San Jose - Cottle Road'
  },
  'mountain-house': {
    shortName: 'Mountain House',
    fullAddress: '768 N Montebello St, Mountain House, CA 95391, United States',
    label: 'Mountain House'
  },
  'grafton-st-after': {
    shortName: 'Grafton St',
    fullAddress: '4078 Grafton St, Dublin, CA 94568, United States',
    label: 'Dublin - Grafton St (After School)'
  },
  'dublin-after': {
    shortName: 'Dublin After School',
    fullAddress: '4078 Grafton St, Dublin, CA 94568, United States',
    label: 'Dublin After School'
  }
};

export const indiaLocationMappings: Record<string, LocationDetail> = {
  'bima-nagar': {
    shortName: 'Bima Nagar',
    fullAddress: '11, Bima Nagar, Anand Bazaar, Indore, Madhya Pradesh 452001, India',
    label: 'Indore - Bima Nagar'
  },
  'nipania': {
    shortName: 'Nipania',
    fullAddress: '27, Samar Park Colony, Nipania, Indore, Madhya Pradesh 452010, India',
    label: 'Indore - Nipania'
  }
};

export const getLocationDetails = (key: string, country: 'usa' | 'india'): LocationDetail | undefined => {
  return country === 'usa' ? usaLocationMappings[key] : indiaLocationMappings[key];
};

export const getAllLocations = (country: 'usa' | 'india'): LocationDetail[] => {
  const mappings = country === 'usa' ? usaLocationMappings : indiaLocationMappings;
  return Object.values(mappings);
};
