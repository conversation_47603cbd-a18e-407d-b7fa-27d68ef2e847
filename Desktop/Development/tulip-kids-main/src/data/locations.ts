// Define the Location interface once
export interface Location {
  name: string;
  badge: string;
  heading: string;
  description: string;
  heroImage: string;
  heroImageAlt: string;
  slug: string;
  location: string;
  type?: string[];
  features: string[];
  facilities: Array<{
    name: string;
    description: string;
    image?: string;
    iconName: string;
  }>;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  accessibility: {
    features: string[];
    additionalInfo: string;
  };
  safety: Array<{
    title: string;
    description: string;
  }>;
  community: Array<{
    title: string;
    description: string;
    image?: string;
  }>;
  faq: Array<{
    question: string;
    answer: string;
  }>;
  director?: {
    name: string;
    role: string;
    image: string;
    bio: string[];
  };
  country: "usa" | "india";
}

// Export usaLocations once with all locations
export const usaLocations: { [key: string]: Location } = {
  "willow-ave": {
    name: "Willow Ave Preschool",
    badge: "Preschool Program",
    heading: "Welcome to Willow Ave",
    description:
      "Our Willow Ave location provides a nurturing environment where children can learn, grow, and thrive. With experienced educators and state-of-the-art facilities, we offer comprehensive early childhood education programs.",
    heroImage: "/images/locations/willow-ave.jpg",
    heroImageAlt: "Willow Ave Preschool Location",
    slug: "willow-ave",
    location: "Sunnyvale, CA",
    type: ["preschool"],
    features: [
      "Child-Centered Learning",
      "Safe Environment",
      "Experienced Staff",
    ],
    address: {
      street: "1159 Willow Ave",
      city: "Sunnyvale",
      state: "CA",
      zip: "94086",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },
  "willow-ave-after": {
    name: "Willow Ave After School 2",
    badge: "After School Program",
    heading: "Welcome to Willow Ave After School",
    description:
      "Our Willow Ave After School program provides a dynamic learning environment where students can thrive academically and socially. We offer comprehensive after-school care with a focus on homework support, enrichment activities, and creative development.",
    heroImage: "/images/locations/Willow-ave.jpg",
    heroImageAlt: "Willow Ave After School Location",
    slug: "willow-ave-after",
    location: "Sunnyvale, CA",
    type: ["afterSchool"],
    features: ["Homework Support", "STEAM Activities", "Physical Education"],
    address: {
      street: "1159 Willow Ave",
      city: "Sunnyvale",
      state: "CA",
      zip: "94086",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },

  "el-camino": {
    name: "El Camino Real Preschool",
    badge: "Preschool Program",
    heading: "Welcome to El Camino Real",
    description:
      "Located in the heart of Santa Clara, our El Camino Real center offers a vibrant preschool program focused on early childhood development and kindergarten readiness.",
    heroImage: "/images/locations/El Camino Real.webp",
    heroImageAlt: "El Camino Real Preschool Location",
    slug: "el-camino",
    location: "Santa Clara, CA",
    type: ["preschool"],
    features: ["Early Learning", "Creative Arts", "Outdoor Play"],
    address: {
      street: "2280 El Camino Real",
      city: "Santa Clara",
      state: "CA",
      zip: "95050",
      phone: "************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },

  "lawrence-station": {
    name: "Lawrence Station Preschool",
    badge: "Preschool Program",
    heading: "Welcome to Lawrence Station",
    description:
      "Our Lawrence Station location provides a modern learning environment with state-of-the-art facilities designed to nurture young minds and foster growth.",
    heroImage: "/images/locations/lawrence-station.jpg",
    heroImageAlt: "Lawrence Station Preschool Location",
    slug: "lawrence-station",
    location: "Sunnyvale, CA",
    type: ["preschool"],
    features: ["Early Learning", "Creative Arts", "Outdoor Play"],
    address: {
      street: "1279 Lawrence Station Rd",
      city: "Sunnyvale",
      state: "CA",
      zip: "94089",
      phone: "************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },
  "mountain-house": {
    name: "Mountain House Preschool",
    badge: "Preschool Program",
    heading: "Welcome to Mountain House",
    description:
      "Our Mountain House location offers a modern learning environment in a growing community, focusing on early childhood development and educational excellence.",
    heroImage: "/images/locations/mountain-house.jpg",
    heroImageAlt: "Mountain House Preschool Location",
    slug: "mountain-house",
    location: "Mountain House, CA",
    type: ["preschool"],
    features: ["Early Learning", "Creative Arts", "Outdoor Play"],
    address: {
      street: "768 N Montebello St",
      city: "Mountain House",
      state: "CA",
      zip: "95391",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },
  "cottle-road": {
    name: "Cottle Road Preschool",
    badge: "Preschool Program",
    heading: "Welcome to Cottle Road",
    description:
      "Our Cottle Road location in San Jose provides a warm and enriching environment where children can discover, learn, and grow together.",
    heroImage: "/images/locations/cottle-road.jpg",
    heroImageAlt: "Cottle Road Preschool Location",
    slug: "cottle-road",
    location: "San Jose, CA",
    type: ["preschool"],
    features: ["Early Learning", "Creative Arts", "Outdoor Play"],
    address: {
      street: "6097 Cottle Rd",
      city: "San Jose",
      state: "CA",
      zip: "95123",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },
  "grafton-st-after": {
    name: "Grafton St After School",
    badge: "After School Program",
    heading: "Welcome to Grafton St After School",
    description:
      "Our Grafton St After School program provides a safe, engaging, and enriching environment for school-age children. We offer homework support, enrichment activities, and supervised play time.",
    heroImage: "/images/locations/Dublin.jpg",
    heroImageAlt:
      "Grafton St After School Location - Modern educational facility in Dublin, CA",
    location: "Dublin, CA",
    slug: "grafton-st-after",
    type: ["afterSchool"],
    features: [
      "Homework Support",
      "Enrichment Activities",
      "Physical Activities",
    ],
    address: {
      street: "4078 Grafton St",
      city: "Dublin",
      state: "CA",
      zip: "94568",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [
      {
        name: "Study Areas",
        description:
          "Quiet spaces equipped with resources for homework and study time",
        iconName: "BookOpen",
      },
      {
        name: "Activity Zones",
        description:
          "Designated areas for different activities including arts, games, and group projects",
        iconName: "LayoutGrid",
      },
      {
        name: "Outdoor Play Space",
        description:
          "Safe and supervised outdoor area for sports and recreational activities",
        iconName: "Trees",
      },
      {
        name: "Tech Lab",
        description:
          "Modern computer stations for educational software and digital learning",
        iconName: "Monitor",
      },
      {
        name: "Game Room",
        description: "Space for indoor games and recreational activities",
        iconName: "Gamepad2",
      },
      {
        name: "Dining Area",
        description: "Clean and comfortable space for snacks and meals",
        iconName: "Utensils",
      },
    ],
    accessibility: {
      features: [
        "ADA Compliant Facilities",
        "Ground Floor Access",
        "Wide Doorways and Hallways",
      ],
      additionalInfo:
        "Our facility is designed to be accessible to all children and families.",
    },
    safety: [
      {
        title: "Health & Safety Protocols",
        description:
          "Comprehensive safety measures including regular sanitization and health screenings",
      },
      {
        title: "Secure Access",
        description:
          "Controlled entry system and security cameras for enhanced safety",
      },
    ],
    community: [
      {
        title: "Parent Communication",
        description:
          "Regular updates on activities and student progress through our parent portal",
      },
      {
        title: "Special Events",
        description: "Regular community events and student showcases",
      },
    ],
    faq: [
      {
        question: "What are your operating hours?",
        answer:
          "Our after-school program operates Monday through Friday from school dismissal until 6:00 PM. We also offer full-day programs during school holidays and breaks.",
      },
      {
        question: "Do you provide transportation?",
        answer:
          "Yes, we provide transportation from partner schools in the Dublin area. Please contact us for specific school routes and availability.",
      },
      {
        question: "What age groups do you serve?",
        answer:
          "Our after-school program serves children from kindergarten through 5th grade (ages 5-11).",
      },
      {
        question: "What activities do you offer?",
        answer:
          "We offer a balanced program including homework support, STEAM activities, arts and crafts, physical activities, and supervised free play. We also have special enrichment programs that rotate throughout the year.",
      },
      {
        question: "Do you provide snacks?",
        answer:
          "Yes, we provide a healthy afternoon snack. On full days, children should bring their lunch, and we provide both morning and afternoon snacks.",
      },
      {
        question: "How do you handle homework?",
        answer:
          "We have dedicated homework time with qualified staff available to provide assistance. We work with parents to understand each child's homework requirements and provide appropriate support.",
      },
      {
        question: "What is your staff-to-child ratio?",
        answer:
          "We maintain a 1:14 staff-to-child ratio to ensure proper supervision and individual attention.",
      },
      {
        question: "Do you offer drop-in care?",
        answer:
          "Drop-in care may be available based on daily capacity. Please contact us in advance to check availability and rates.",
      },
    ],
    director: {
      name: "Michael Chen",
      role: "Program Director",
      image: "/images/staff/michael-chen.jpg",
      bio: [
        "Mr. Michael brings over 8 years of experience in after-school program management and youth development.",
        "With a Bachelor's degree in Education and certification in youth development, he is passionate about creating engaging after-school experiences that support academic success and personal growth.",
        "His approach focuses on building strong relationships with schools and families while implementing programs that balance academic support with enriching activities.",
        "Under his leadership, our Grafton St after-school program has become known for its innovative STEAM programs and strong academic support.",
      ],
    },
    country: "usa",
  },
  "golden-valley": {
    name: "Golden Valley Preschool",
    badge: "Preschool Program",
    heading: "Welcome to Golden Valley",
    description:
      "Our Golden Valley location provides a nurturing environment where children can learn, grow, and thrive. With experienced educators and state-of-the-art facilities, we offer comprehensive early childhood education programs.",
    heroImage: "/images/locations/golden-valley.jpg",
    heroImageAlt: "Golden Valley Preschool Location",
    slug: "golden-valley",
    location: "Lathrop, CA",
    type: ["preschool"],
    features: [
      "Child-Centered Learning",
      "Safe Environment",
      "Experienced Staff",
    ],
    address: {
      street: "17380 Golden Valley Pkwy",
      city: "Lathrop",
      state: "CA",
      zip: "95330",
      phone: "(*************",
      email: "<EMAIL>",
    },
    facilities: [],
    accessibility: {
      features: [],
      additionalInfo: "",
    },
    safety: [],
    community: [],
    faq: [],
    country: "usa",
  },
};

// Export indiaLocations
export const indiaLocations: { [key: string]: Location } = {
  nipania: {
    name: "Indore - Nipania",
    badge: "PreSchool",
    heading: "Welcome to Tulip Kids Academy - Nipania",
    description:
      "Discover excellence in early childhood education with our comprehensive approach to learning and development.",
    heroImage: "/images/locations/nipania.jpg",
    heroImageAlt: "Tulip Kids Academy Nipania Location",
    slug: "nipania",
    location: "Indore, Madhya Pradesh",
    type: ["preschool"],
    features: [
      "Holistic Development",
      "Age-Appropriate Learning",
      "Safe Environment",
    ],
    facilities: [
      {
        name: "Learning Spaces",
        description:
          "Well-designed classrooms with modern educational resources",
        iconName: "School",
      },
      {
        name: "Play Area",
        description:
          "Safe and engaging outdoor play space for physical development",
        iconName: "Trees",
      },
      {
        name: "Activity Room",
        description: "Multi-purpose room for various educational activities",
        iconName: "Palette",
      },
    ],
    address: {
      street: "27 Samar Park, Nipania",
      city: "Indore",
      state: "MP",
      zip: "452010",
      phone: "**********",
      email: "<EMAIL>",
    },
    accessibility: {
      features: [
        "Ground floor access",
        "Inclusive facilities",
        "Child-friendly furniture",
      ],
      additionalInfo:
        "Our center is designed to be accessible and comfortable for all children.",
    },
    safety: [
      {
        title: "Security Measures",
        description: "24/7 CCTV surveillance and secure entry system",
      },
      {
        title: "Health Protocol",
        description: "Regular sanitization and health monitoring",
      },
    ],
    community: [
      {
        title: "Family Engagement",
        description: "Regular parent-teacher meetings and family events",
      },
      {
        title: "Cultural Celebration",
        description: "Festivals and cultural activities throughout the year",
      },
    ],
    faq: [
      {
        question: "When will the center open?",
        answer:
          "We are currently in the development phase. Please check back for updates on our opening date.",
      },
      {
        question: "What programs will you offer?",
        answer:
          "We will offer our signature early childhood education programs. Detailed program information will be available closer to opening.",
      },
      {
        question: "How can I get updates about the opening?",
        answer:
          "You can sign up for our newsletter or follow us on social media for the latest updates about our Nipania location.",
      },
    ],
    director: {
      name: "To Be Announced",
      role: "Center Director",
      image: "/images/placeholder/director.jpg",
      bio: [
        "Our upcoming center director will be an experienced early childhood education professional",
        "They will be carefully selected to uphold our high standards of education and care",
        "Stay tuned for more information about our leadership team",
      ],
    },
    country: "india",
  },
  "bima-nagar": {
    name: "Bima Nagar Center",
    badge: "Early Learning Center",
    heading: "Welcome to Bima Nagar",
    description:
      "Our Bima Nagar location provides a nurturing environment where children can learn, grow, and thrive. With experienced educators and modern facilities, we offer comprehensive early childhood education programs.",
    heroImage: "/images/locations/bima-nagar.jpg",
    heroImageAlt: "Bima Nagar Center Location",
    slug: "bima-nagar",
    location: "Indore, Madhya Pradesh",
    type: ["preschool"],
    features: [
      "Child-Centered Learning",
      "Safe Environment",
      "Experienced Staff",
    ],
    facilities: [
      {
        name: "Modern Classrooms",
        description:
          "Spacious, well-equipped learning spaces designed for young children.",
        iconName: "School",
      },
      {
        name: "Outdoor Play Area",
        description:
          "Safe and engaging playground for physical activity and outdoor learning.",
        iconName: "Trees",
      },
      {
        name: "Activity Center",
        description:
          "Dedicated space for arts, crafts, and interactive learning activities.",
        iconName: "Palette",
      },
    ],
    address: {
      street: "11, Bima Nagar, Anand Bazaar",
      city: "Indore",
      state: "MP",
      zip: "452001",
      phone: "**********",
      email: "<EMAIL>",
    },
    accessibility: {
      features: [
        "Wheelchair accessible",
        "Ground floor access",
        "Inclusive learning materials",
      ],
      additionalInfo:
        "We are committed to providing an inclusive environment for all children.",
    },
    safety: [
      {
        title: "Health Protocols",
        description: "Regular sanitization and health screening procedures.",
      },
      {
        title: "Security System",
        description: "CCTV surveillance and controlled access entry.",
      },
    ],
    community: [
      {
        title: "Parent Involvement",
        description: "Regular parent-teacher meetings and family events.",
        image: "/images/locations/parent-meeting.jpg",
      },
      {
        title: "Cultural Activities",
        description: "Celebration of festivals and cultural programs.",
        image: "/images/locations/cultural-event.jpg",
      },
    ],
    faq: [
      {
        question: "What are your operating hours?",
        answer:
          "We are open Monday through Saturday from 09:00 AM to 03:30 PM.",
      },
      {
        question: "What age groups do you accept?",
        answer:
          "We accept children from 2 to 6 years of age in different programs: Junior Pre-School (2-3 years), Preschool Program (3-4 years), Pre-K Program (4-5 years), and Kindergarten (5-6 years).",
      },
    ],
    director: {
      name: "Priya Sharma",
      role: "Center Director",
      image: "/images/team/priya-sharma.jpg",
      bio: [
        "Ms. Priya has over 15 years of experience in early childhood education.",
        "She holds a Master's degree in Early Childhood Development.",
        "Under her leadership, our center has become a model for progressive early education.",
      ],
    },
    country: "india",
  },
};
