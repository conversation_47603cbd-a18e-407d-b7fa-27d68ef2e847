// Location data types
export interface Location {
  name: string;
  badge: string;
  type: string[];
  heading: string;
  description: string;
  heroImage: string;
  heroImageAlt: string;
  features: Array<{
    title: string;
    description: string;
    iconName?: string;
  }>;
  facilities: Array<{
    name: string;
    description: string;
    image?: string;
    iconName: string;
  }>;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  accessibility: {
    features: string[];
    additionalInfo: string;
  };
  safety: Array<{
    title: string;
    description: string;
  }>;
  community: Array<{
    title: string;
    description: string;
    image?: string;
  }>;
  faq: Array<{
    question: string;
    answer: string;
  }>;
  country: 'usa' | 'india';
  ExcellenceSection?: React.ComponentType;
  id?: string;
  image?: string;
  slug?: string;
  director?: {
    name: string;
    role: string;
    image: string;
    bio: string[];
    achievements?: string[];
  };
  enrollmentLink?: string;
}

import { directors } from '../directors';

export interface LocationCard {
  id: number;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  email: string;
  image: string;
  type: 'PreSchool' | 'After School';
  enrollmentLink: string;
}

export const usaLocations: { [key: string]: Location } = {
  'willow-ave': {
    id: 'willow-ave',
    name: 'Sunnyvale - Willow Ave',
    badge: 'PreSchool & After School',
    type: ['preschool', 'afterSchool'],
    heading: 'Welcome to Tulip Kids Academy - Willow Ave',
    description: 'Experience excellence in early childhood education with our comprehensive programs from infant care to pre-K.',
    heroImage: '/images/locations/Willow-ave.jpg',
    heroImageAlt: 'Tulip Kids Academy Willow Ave Location',
    image: '/images/locations/Willow-ave.jpg',
    address: {
      street: '1159 Willow Ave',
      city: 'Sunnyvale',
      state: 'CA',
      zip: '94086',
      phone: '******-340-7993',
      email: '<EMAIL>'
    },
    director: directors['willow-ave'],
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/register/tulip-kids-willow-ave-sunnyvale-94086',
    features: [
      {
        title: 'Infant Care (12-24 months)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Toddler Program (2-3 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Preschool (3-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Pre-K (4-5 years)',
        description: 'Advanced learning preparation for kindergarten success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Dining Area',
        description: 'Clean and comfortable space for meals and snacks',
        iconName: 'Utensils'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for indoor activities and creative play',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      },
      {
        name: 'Transportation',
        description: 'Safe and reliable transportation services',
        iconName: 'Bus'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating diversity through cultural events and activities',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious meals and snacks that meet dietary guidelines and accommodate allergies.'
      }
    ]
  },
  'willow-ave-after': {
    name: 'Willow Ave After School',
    badge: 'After School Program',
    type: ['afterSchool'],
    heading: 'Welcome to Tulip Kids Academy - Willow Ave After School',
    description: 'Our after-school program provides a safe, enriching environment where children can learn, play, and grow.',
    heroImage: '/images/locations/Willow-ave.jpg',
    heroImageAlt: 'Willow Ave After School Location',
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c',
    // ExcellenceSection: null,
    features: [
      {
        title: 'Academic Support',
        description: 'Dedicated homework time with qualified tutors',
        iconName: 'BookOpen'
      },
      {
        title: 'Enrichment Activities',
        description: 'Arts, music, and STEM programs to foster creativity and learning',
        iconName: 'Gamepad2'
      },
      {
        title: 'Physical Activities',
        description: 'Structured outdoor play and sports activities',
        iconName: 'Trees'
      },
      {
        title: 'Healthy Snacks',
        description: 'Nutritious afternoon snacks provided daily',
        iconName: 'Utensils'
      },
      {
        title: 'Transportation',
        description: 'Safe transportation from local schools',
        iconName: 'Bus'
      }
    ],
    facilities: [
      {
        name: 'Homework Area',
        description: 'Quiet, dedicated space for homework and study',
        iconName: 'BookOpen'
      },
      {
        name: 'Activity Room',
        description: 'Space for indoor activities and games',
        iconName: 'Gamepad2'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Safe outdoor space for physical activities',
        iconName: 'Trees'
      }
    ],
    address: {
      street: '1159 Willow Ave',
      city: 'Sunnyvale',
      state: 'CA',
      zip: '94086',
      phone: '******-930-1862',
      email: '<EMAIL>'
    },
    accessibility: {
      features: [
        'Wheelchair accessible',
        'Ground floor access',
        'Wide doorways and hallways'
      ],
      additionalInfo: 'Please contact us for specific accessibility accommodations.'
    },
    safety: [
      {
        title: 'Health & Safety',
        description: 'Regular sanitization and health protocols in place'
      },
      {
        title: 'Security',
        description: 'Secure entry system and monitored premises'
      }
    ],
    community: [
      {
        title: 'Parent Community',
        description: 'Regular parent meetings and events'
      },
      {
        title: 'Local Schools',
        description: 'Strong partnerships with nearby schools'
      }
    ],
    faq: [
      {
        question: 'What are your hours?',
        answer: 'We operate from school dismissal until 6:30 PM on regular school days.'
      },
      {
        question: 'Do you provide transportation?',
        answer: 'Yes, we provide transportation from select local schools. Please contact us for specific school information.'
      },
      {
        question: 'What activities do you offer?',
        answer: 'We offer homework help, enrichment activities, outdoor play, and supervised free time.'
      }
    ]
  },
  'lawrence-station': {
    id: 'lawrence-station',
    name: 'Sunnyvale - Lawrence Station Road',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - Lawrence Station',
    description: 'Discover a nurturing environment where your child can thrive and grow.',
    heroImage: '/images/locations/lawrence-station-road.png',
    heroImageAlt: 'Tulip Kids Academy Lawrence Station Location',
    image: '/images/locations/lawrence-station-road.png',
    address: {
      street: '1279 Lawrence Station Rd',
      city: 'Sunnyvale',
      state: 'CA',
      zip: '94089',
      phone: '******-255-0540',
      email: '<EMAIL>'
    },
    director: directors['lawrence-station'],
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/register/tulip-kids-ls-sunnyvale-94086',
    features: [
      {
        title: 'Infant Care (12-24 months)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Toddler Program (2-3 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Preschool (3-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Pre-K (4-5 years)',
        description: 'Advanced learning preparation for kindergarten success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Dining Area',
        description: 'Clean and comfortable space for meals and snacks',
        iconName: 'Utensils'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for indoor activities and creative play',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      },
      {
        name: 'Transportation',
        description: 'Safe and reliable transportation services',
        iconName: 'Bus'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating diversity through cultural events and activities',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious meals and snacks that meet dietary guidelines and accommodate allergies.'
      }
    ]
  },
  'el-camino': {
    id: 'el-camino',
    name: 'Santa Clara - El Camino Real',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - El Camino Real',
    description: 'Discover a nurturing environment where your child can thrive and grow.',
    heroImage: '/images/locations/El Camino Real.jpg',
    heroImageAlt: 'Tulip Kids Academy El Camino Real Location',
    image: '/images/locations/El Camino Real.jpg',
    address: {
      street: '2280 El Camino Real',
      city: 'Santa Clara',
      state: 'CA',
      zip: '95050',
      phone: '******-377-5774',
      email: '<EMAIL>'
    },
    director: directors['el-camino'],
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/register/2596080c-cc3c-43bc-8d2b-3835b893d805',
    features: [
      {
        title: 'Infant Care (12-24 months)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Toddler Program (2-3 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Preschool (3-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Pre-K (4-5 years)',
        description: 'Advanced learning preparation for kindergarten success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Dining Area',
        description: 'Clean and comfortable space for meals and snacks',
        iconName: 'Utensils'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for indoor activities and creative play',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      },
      {
        name: 'Transportation',
        description: 'Safe and reliable transportation services',
        iconName: 'Bus'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating diversity through cultural events and activities',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious meals and snacks that meet dietary guidelines and accommodate allergies.'
      }
    ]
  },
  'cottle-road': {
    id: 'cottle-road',
    name: 'San Jose - Cottle Road',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - Cottle Road',
    description: 'Discover a nurturing environment where your child can thrive and grow in our state-of-the-art facility.',
    heroImage: '/images/locations/Cottle Road.jpg',
    heroImageAlt: 'Tulip Kids Academy Cottle Road Location',
    image: '/images/locations/Cottle Road.jpg',
    address: {
      street: '6097 Cottle Rd',
      city: 'San Jose',
      state: 'CA',
      zip: '95123',
      phone: '******-859-6442',
      email: '<EMAIL>'
    },
    director: directors['cottle-road'],
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/register/673683fa-25cf-4328-a931-4d6c5d4ebfad',
    features: [
      {
        title: 'Infant Care (12-24 months)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Toddler Program (2-3 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Preschool (3-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Pre-K (4-5 years)',
        description: 'Advanced learning preparation for kindergarten success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Spacious Classrooms',
        description: 'Modern, well-equipped classrooms designed for interactive learning and play',
        iconName: 'School'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Large, secure playground with age-appropriate equipment and natural elements',
        iconName: 'Trees'
      },
      {
        name: 'STEAM Lab',
        description: 'Dedicated space for hands-on science, technology, engineering, arts, and mathematics activities',
        iconName: 'Brain'
      },
      {
        name: 'Library & Reading Nook',
        description: 'Cozy space filled with age-appropriate books to foster early literacy',
        iconName: 'BookOpen'
      },
      {
        name: 'Art Studio',
        description: 'Creative space for artistic expression and development',
        iconName: 'Puzzle'
      },
      {
        name: 'Dining Area',
        description: 'Clean, comfortable space for nutritious meals and snacks',
        iconName: 'Utensils'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances and pathways',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Secure Access System',
        description: 'State-of-the-art security system with controlled access and monitoring'
      },
      {
        title: 'Health & Safety Protocols',
        description: 'Comprehensive health and safety measures including regular sanitization'
      },
      {
        title: 'Certified Staff',
        description: 'All staff members are certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Partnerships',
        description: 'Regular parent-teacher meetings and family engagement events',
        image: '/images/community/parent-partnerships.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Diverse cultural events celebrating our community',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'Our teacher-to-student ratios exceed state requirements: 1:3 for infants, 1:4 for toddlers, 1:8 for preschool, and 1:10 for pre-K.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious breakfast, lunch, and snacks that meet USDA guidelines. We accommodate food allergies and dietary restrictions.'
      },
      {
        question: 'What is your curriculum approach?',
        answer: 'We follow a play-based, STEAM-focused curriculum that promotes hands-on learning and development across all domains.'
      }
    ]
  },
  'willow-after': {
    name: 'Sunnyvale - Willow Ave',
    badge: 'After School',
    type: ['afterSchool'],
    heading: 'Welcome to Tulip Kids Academy - Willow Ave After School',
    description: 'Quality after-school programs for your child\'s continued growth.',
    heroImage: '/images/locations/willow-after-hero.jpg',
    heroImageAlt: 'Tulip Kids Academy Willow Ave After School Location',
    address: {
      street: '1159 Willow Ave',
      city: 'Sunnyvale',
      state: 'CA',
      zip: '94086',
      phone: '******-930-1862',
      email: '<EMAIL>'
    },
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c',
    features: [
      {
        title: 'After School Care',
        description: 'Enriching activities and homework support for school-age children.',
        iconName: 'GraduationCap'
      },
      {
        title: 'Summer Programs',
        description: 'Engaging summer activities combining learning and fun.',
        iconName: 'School'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Dining Area',
        description: 'Clean and comfortable space for meals and snacks',
        iconName: 'Utensils'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for indoor activities and creative play',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      },
      {
        name: 'Transportation',
        description: 'Safe and reliable transportation services',
        iconName: 'Bus'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating diversity through cultural events and activities',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'Our after-school program runs Monday through Friday from after school until 6:30 PM.'
      },
      {
        question: 'Do you provide transportation from schools?',
        answer: 'Yes, we provide safe and reliable transportation from partner schools in the Dublin area.'
      },
      {
        question: 'What activities are included in the program?',
        answer: 'Our program includes homework help, enrichment activities in Math and Language Arts, Yoga, Arts & Crafts, Cultural Dance, Spanish lessons, STEAM Workshops, and various clubs including Coding and Public Speaking.'
      },
      {
        question: 'Do you provide snacks?',
        answer: 'Yes, we provide healthy and nutritious snacks daily, accommodating various dietary requirements and restrictions.'
      },
      {
        question: 'How do you ensure academic progress?',
        answer: 'We maintain regular communication with parents and schools, provide dedicated homework time, and offer additional enrichment through our Math and Language Arts programs.'
      }
    ]
  },
  'grafton-st-after': {
    name: 'Grafton St After School',
    badge: 'After School Program',
    type: ['afterSchool'],
    heading: 'Welcome to Grafton St After School',
    description: 'Our after-school program provides a dynamic learning environment where students can thrive academically and socially. We offer comprehensive after-school care with a focus on homework support, enrichment activities, and creative development.',
    heroImage: '/images/locations/Dublin.jpg',
    heroImageAlt: 'Grafton St After School Location',
    country: 'usa',
    enrollmentLink: 'https://tulipkidsinc.com/contact-us/',
    features: [
      {
        title: 'Homework Support',
        description: 'Dedicated homework time with qualified tutors and academic assistance',
        iconName: 'BookOpen'
      },
      {
        title: 'STEAM Activities',
        description: 'Hands-on science, technology, engineering, arts, and mathematics projects',
        iconName: 'Brain'
      },
      {
        title: 'Physical Education',
        description: 'Structured outdoor play, sports activities, and yoga for physical wellness',
        iconName: 'HeartPulse'
      },
      {
        title: 'Arts and Crafts',
        description: 'Creative expression through various art mediums and craft projects',
        iconName: 'Gamepad2'
      },
      {
        title: 'Cultural Dance',
        description: 'Bollywood, Folk, and Fusion dance styles for cultural enrichment',
        iconName: 'Trees'
      },
      {
        title: 'Language Learning',
        description: 'Spanish language immersion and vocabulary building activities',
        iconName: 'GraduationCap'
      },
      {
        title: 'Life Skills',
        description: 'Chess, public speaking, coding club, and community service projects',
        iconName: 'Award'
      },
      {
        title: 'Transportation',
        description: 'Safe and reliable transportation service from local schools',
        iconName: 'Bus'
      }
    ],
    address: {
      street: '4078 Grafton St',
      city: 'Dublin',
      state: 'CA',
      zip: '94568',
      phone: '******-679-5431',
      email: '<EMAIL>'
    },
    facilities: [
      {
        name: 'Homework Area',
        description: 'Quiet, dedicated space for homework and study',
        iconName: 'BookOpen'
      },
      {
        name: 'Activity Room',
        description: 'Space for indoor activities and games',
        iconName: 'Gamepad2'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Safe outdoor space for physical activities',
        iconName: 'Trees'
      },
      {
        name: 'STEAM Lab',
        description: 'Dedicated space for science, technology, engineering, arts, and mathematics activities',
        iconName: 'Gamepad2'
      },
      {
        name: 'Snack Area',
        description: 'Clean and comfortable space for afternoon snacks',
        iconName: 'Utensils'
      }
    ],
    accessibility: {
      features: [
        'Wheelchair accessible entrances and facilities',
        'ADA compliant restrooms',
        'Wide doorways and hallways',
        'Accessible parking spaces',
        'Visual and auditory learning aids'
      ],
      additionalInfo: 'Our facility is designed to accommodate children of all abilities. Please contact us to discuss specific accommodation needs.'
    },
    safety: [
      {
        title: 'Security System',
        description: 'State-of-the-art security system with controlled access and monitoring'
      },
      {
        title: 'Health Protocols',
        description: 'Strict health and sanitization protocols to ensure a safe environment'
      },
      {
        title: 'Emergency Response',
        description: 'Well-trained staff in emergency procedures and first aid'
      },
      {
        title: 'Safe Play Areas',
        description: 'Regular safety inspections of all play equipment and facilities'
      }
    ],
    community: [
      {
        title: 'Parent Workshops',
        description: 'Regular workshops on child development and parenting skills'
      },
      {
        title: 'Family Events',
        description: 'Seasonal celebrations and family engagement activities'
      },
      {
        title: 'Cultural Programs',
        description: 'Diverse cultural activities and celebrations throughout the year'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious breakfast, lunch, and snacks prepared fresh daily.'
      },
      {
        question: 'What is your staff-to-child ratio?',
        answer: 'We maintain low staff-to-child ratios that meet or exceed state requirements for each age group.'
      },
      {
        question: 'How do you handle allergies?',
        answer: 'We have strict protocols for managing allergies and maintain a nut-free environment. Our staff is trained in allergy awareness and emergency response.'
      }
    ]
  },
  
  'mountain-house': {
    name: 'Mountain House',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - Mountain House',
    description: 'Opening Soon! Join us in our newest location providing quality early childhood education.',
    heroImage: '/images/locations/Mountain House.jpg',
    heroImageAlt: 'Tulip Kids Academy Mountain House Location',
    address: {
      street: '17380 Golden Valley Pkwy',
      city: 'Lathrop',
      state: 'CA',
      zip: '95330',
      phone: '******-687-5823',
      email: '<EMAIL>'
    },
    country: 'usa',
    enrollmentLink: 'https://schools.procareconnect.com/register/3ea394f5-2b40-4150-85d1-2ce734c856ae',
    features: [
      {
        title: 'Infant Care (12-24 months)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Toddler Program (2-3 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Preschool (3-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Pre-K (4-5 years)',
        description: 'Advanced learning preparation for kindergarten success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Dining Area',
        description: 'Clean and comfortable space for meals and snacks',
        iconName: 'Utensils'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for indoor activities and creative play',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      },
      {
        name: 'Transportation',
        description: 'Safe and reliable transportation services',
        iconName: 'Bus'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with wide doorways',
        'Wheelchair accessible entrances',
        'ADA compliant restrooms',
        'Inclusive playground equipment',
        'Sensory-friendly spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating diversity through cultural events and activities',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 8:00 AM to 6:00 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious meals and snacks that meet dietary guidelines and accommodate allergies.'
      }
    ]
  },
  'indore': {
    name: 'Indore - Bima Nagar',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - Bima Nagar',
    description: 'Discover excellence in early childhood education with our comprehensive approach to learning and development.',
    heroImage: '/images/locations/India.jpg',
    heroImageAlt: 'Tulip Kids Academy Bima Nagar Location',
    director: {
      name: 'Meenal Shah',
      role: 'Center Director',
      image: '/images/team/female-avatar.jpg',
      bio: [
        'With extensive experience in early childhood education, Meenal Shah leads our Bima Nagar center with dedication and expertise.',
        'She is committed to providing a nurturing environment where every child can thrive and reach their full potential.'
      ]
    },
    address: {
      street: '11, Bima Nagar, Anand Bazaar',
      city: 'Indore',
      state: 'MP',
      zip: '452001',
      phone: '9575545952',
      email: '<EMAIL>'
    },
    country: 'india',
    features: [
      {
        title: 'Junior Pre-School (2-3 years)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Preschool Program (3-4 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Pre-K Program (4-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Kindergarten (5-6 years)',
        description: 'Advanced learning preparation for school success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration',
        iconName: 'Trees'
      },
      {
        name: 'Activity Room',
        description: 'Dedicated space for creative and educational activities',
        iconName: 'Gamepad2'
      },
      {
        name: 'Library',
        description: 'Well-stocked library with age-appropriate books and reading materials',
        iconName: 'BookOpen'
      },
      {
        name: 'Medical Room',
        description: 'Equipped medical room for basic health care',
        iconName: 'HeartPulse'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with easy access',
        'Wheelchair accessible entrances',
        'Child-friendly restrooms',
        'Inclusive playground equipment',
        'Well-lit spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating Indian festivals and cultural events',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 09:30 AM to 03:30 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'No, we do not provide meals.'
      }
    ]
  },
  'nipania': {
    name: 'Indore - Nipania',
    badge: 'PreSchool',
    type: ['preschool'],
    heading: 'Welcome to Tulip Kids Academy - Nipania',
    description: 'Discover excellence in early childhood education with our comprehensive approach to learning and development.',
    heroImage: '/images/locations/nipania.jpg',
    heroImageAlt: 'Tulip Kids Academy Nipania Location',
    address: {
      street: '27 Samar Park, Nipania',
      city: 'Indore',
      state: 'MP',
      zip: '452010',
      phone: '9575545200',
      email: '<EMAIL>'
    },
    country: 'india',
    features: [
      {
        title: 'Junior Pre-School (2-3 years)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Preschool Program (3-4 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Pre-K Program (4-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Kindergarten (5-6 years)',
        description: 'Advanced learning preparation for school success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Modern Classrooms',
        description: 'Spacious, well-lit classrooms equipped with age-appropriate learning materials.',
        iconName: 'Building2'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration.',
        iconName: 'Trees'
      },
      {
        name: 'Activity Center',
        description: 'Dedicated space for arts, crafts, and interactive learning activities.',
        iconName: 'Puzzle'
      }
    ],
    accessibility: {
      features: [
        'Ground floor facility with easy access',
        'Wheelchair accessible entrances',
        'Child-friendly restrooms',
        'Inclusive playground equipment',
        'Well-lit spaces'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children. Please contact us for specific accommodation needs.'
    },
    safety: [
      {
        title: 'Health & Safety Protocols',
        description: 'Regular sanitization, health screenings, and strict hygiene practices'
      },
      {
        title: 'Secure Access',
        description: 'Controlled entry system and security cameras for enhanced safety'
      },
      {
        title: 'Trained Staff',
        description: 'Staff certified in first aid, CPR, and emergency response'
      }
    ],
    community: [
      {
        title: 'Parent Involvement',
        description: 'Regular parent-teacher meetings and family events',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Celebrations',
        description: 'Celebrating Indian festivals and cultural events',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Saturday from 9:00 AM to 3:30 PM.'
      },
      {
        question: 'What age groups do you accept?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-student ratio?',
        answer: 'We maintain low teacher-to-student ratios that exceed state requirements to ensure individual attention.'
      },
      {
        question: 'Do you provide meals?',
        answer: 'Yes, we provide nutritious meals and snacks that meet dietary guidelines and accommodate allergies.'
      }
    ]
  },
  'bima-nagar': {
    name: 'Bima Nagar',
    badge: 'Now Open',
    type: ['Pre School', 'After School'],
    heading: 'Welcome to Tulip Kids Bima Nagar',
    description: 'Our Bima Nagar location offers a nurturing environment for children to learn, grow, and thrive.',
    heroImage: '/images/locations/India.jpg',
    heroImageAlt: 'Tulip Kids Bima Nagar Location',
    features: [
      {
        title: 'Junior Pre-School (2-3 years)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Preschool Program (3-4 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Pre-K Program (4-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Kindergarten (5-6 years)',
        description: 'Advanced learning preparation for school success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Modern Classrooms',
        description: 'Spacious, well-lit classrooms equipped with age-appropriate learning materials.',
        iconName: 'Building2'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration.',
        iconName: 'Trees'
      },
      {
        name: 'Activity Center',
        description: 'Dedicated space for arts, crafts, and interactive learning activities.',
        iconName: 'Puzzle'
      }
    ],
    address: {
      street: '11, Bima Nagar, Anand Bazaar',
      city: 'Indore',
      state: 'Madhya Pradesh',
      zip: '452001',
      phone: '9575545952',
      email: '<EMAIL>'
    },
    country: 'india',
    accessibility: {
      features: [
        'Wheelchair accessible',
        'Ground floor location',
        'Wide doorways',
        'Accessible restrooms'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families.'
    },
    safety: [
      {
        title: 'CCTV Surveillance',
        description: '24/7 monitoring of all areas'
      },
      {
        title: 'Secure Entry',
        description: 'Access control system for enhanced security'
      },
      {
        title: 'Safety Protocols',
        description: 'Regular safety drills and emergency procedures'
      }
    ],
    community: [
      {
        title: 'Parent Partnerships',
        description: 'Regular updates and communication with parents about student progress.',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Events',
        description: 'Celebration of festivals and cultural programs',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 09:30 AM to 03:30 PM.'
      },
      {
        question: 'What age groups do you cater to?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-child ratio?',
        answer: 'We maintain a low teacher-to-child ratio of 1:8 for preschoolers and 1:4 for infants.'
      },
      {
        question: 'What curriculum do you follow?',
        answer: 'We follow a play-based learning curriculum that incorporates both modern educational practices and traditional values.'
      }
    ],
    director: {
      name: 'Priya Sharma',
      role: 'Center Director',
      image: '/images/locations/team/priya-sharma.jpg',
      bio: [
        'Priya Sharma brings over 15 years of experience in early childhood education to Tulip Kids Academy.',
        'She holds a Master\'s degree in Early Childhood Education and has been instrumental in developing our unique curriculum that blends modern teaching methods with traditional Indian values.',
        'Under her leadership, our Bima Nagar center has become known for its excellence in early childhood education and holistic development approach.'
      ]
    }
  }
};

export const indiaLocations: { [key: string]: Location } = {
  'bima-nagar': {
    name: 'Bima Nagar',
    badge: 'Now Open',
    type: ['Pre School', 'After School'],
    heading: 'Welcome to Tulip Kids Bima Nagar',
    description: 'Our Bima Nagar location offers a nurturing environment for children to learn, grow, and thrive.',
    heroImage: '/images/locations/India.jpg',
    heroImageAlt: 'Tulip Kids Bima Nagar Location',
    features: [
      {
        title: 'Junior Pre-School (2-3 years)',
        description: 'Nurturing environment with specialized caregivers and age-appropriate activities.',
        iconName: 'Baby'
      },
      {
        title: 'Preschool Program (3-4 years)',
        description: 'Focus on early learning, social skills, and developmental milestones.',
        iconName: 'School'
      },
      {
        title: 'Pre-K Program (4-5 years)',
        description: 'Comprehensive curriculum including STEAM, language, and creative arts.',
        iconName: 'School'
      },
      {
        title: 'Kindergarten (5-6 years)',
        description: 'Advanced learning preparation for school success.',
        iconName: 'GraduationCap'
      }
    ],
    facilities: [
      {
        name: 'Modern Classrooms',
        description: 'Spacious, well-lit classrooms equipped with age-appropriate learning materials.',
        iconName: 'Building2'
      },
      {
        name: 'Outdoor Play Area',
        description: 'Safe and engaging outdoor space for physical activities and nature exploration.',
        iconName: 'Trees'
      },
      {
        name: 'Activity Center',
        description: 'Dedicated space for arts, crafts, and interactive learning activities.',
        iconName: 'Puzzle'
      }
    ],
    address: {
      street: '11, Bima Nagar, Anand Bazaar',
      city: 'Indore',
      state: 'Madhya Pradesh',
      zip: '452001',
      phone: '+91 731 - 4999788',
      email: '<EMAIL>'
    },
    country: 'india',
    accessibility: {
      features: [
        'Wheelchair accessible',
        'Ground floor location',
        'Wide doorways',
        'Accessible restrooms'
      ],
      additionalInfo: 'Our facility is designed to be accessible to all children and families.'
    },
    safety: [
      {
        title: 'CCTV Surveillance',
        description: '24/7 monitoring of all areas'
      },
      {
        title: 'Secure Entry',
        description: 'Access control system for enhanced security'
      },
      {
        title: 'Safety Protocols',
        description: 'Regular safety drills and emergency procedures'
      }
    ],
    community: [
      {
        title: 'Parent Partnerships',
        description: 'Regular updates and communication with parents about student progress.',
        image: '/images/community/parent-involvement.jpg'
      },
      {
        title: 'Cultural Events',
        description: 'Celebration of festivals and cultural programs',
        image: '/images/community/cultural-celebrations.jpg'
      }
    ],
    faq: [
      {
        question: 'What are your operating hours?',
        answer: 'We are open Monday through Friday from 09:30 AM to 03:30 PM.'
      },
      {
        question: 'What age groups do you cater to?',
        answer: 'We accept children from 12 months to 5 years old.'
      },
      {
        question: 'What is your teacher-to-child ratio?',
        answer: 'We maintain a low teacher-to-child ratio of 1:8 for preschoolers and 1:4 for infants.'
      },
      {
        question: 'What curriculum do you follow?',
        answer: 'We follow a play-based learning curriculum that incorporates both modern educational practices and traditional values.'
      }
    ],
    director: {
      name: 'Priya Sharma',
      role: 'Center Director',
      image: '/images/locations/team/priya-sharma.jpg',
      bio: [
        'Priya Sharma brings over 15 years of experience in early childhood education to Tulip Kids Academy.',
        'She holds a Master\'s degree in Early Childhood Education and has been instrumental in developing our unique curriculum that blends modern teaching methods with traditional Indian values.',
        'Under her leadership, our Bima Nagar center has become known for its excellence in early childhood education and holistic development approach.'
      ]
    }
  }
};

export const locationsArray = [~
  {
    id: 1,
    name: 'Sunnyvale - Willow Ave',
    address: '1159, Willow Ave',
    city: 'Sunnyvale',
    state: 'CA',
    zip: '94086',
    phone: '******-340-7993',
    email: '<EMAIL>',
    image: '/images/locations/Willow-ave.jpg',
    type: 'PreSchool',
    enrollmentLink: 'https://schools.procareconnect.com/register/tulip-kids-willow-ave-sunnyvale-94086'
  },
{
  id: 2,
  name: 'Sunnyvale - Lawrence Station Road',
  address: '1279 Lawrence Station Rd',
  city: 'Sunnyvale',
  state: 'CA',
  zip: '94089',
  phone: '******-255-0540',
  type: 'PreSchool',
  enrollmentLink: 'https://schools.procareconnect.com/register/2596080c-cc3c-43bc-8d2b-3835b893d805'
},
{
  id: 4,
  name: 'San Jose - Cottle Road',
  address: '6097 Cottle Rd',
  city: 'San Jose',
  state: 'CA',
  zip: '95123',
  phone: '******-859-6442',
  email: '<EMAIL>',
  image: '/images/locations/Cottle Road.jpg',
  type: 'PreSchool',
  enrollmentLink: 'https://schools.procareconnect.com/register/3ea394f5-2b40-4150-85d1-2ce734c856ae'
},
{
  id: 6,
  name: 'Sunnyvale - Willow Ave (After School)',
  address: '1159 Willow Ave',
  city: 'Sunnyvale',
  state: 'CA',
  zip: '94086',
  phone: '******-930-1862',
  email: '<EMAIL>',
  image: '/images/locations/Willow-ave.jpg',
  type: 'After School',
  enrollmentLink: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c'
},
{
  id: 7,
  name: 'Dublin - Grafton St',
  address: '4078 Grafton St',
  city: 'Dublin',
  state: 'CA',
  zip: '94568',
  phone: '******-679-5431',
  email: '<EMAIL>',
  image: '/images/locations/Dublin.jpg',
  type: 'After School',
  enrollmentLink: 'https://tulipkidsinc.com/contact-us/'
}
];
