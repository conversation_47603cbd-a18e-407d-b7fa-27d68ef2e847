'use client';

import Image from 'next/image';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Puzzle, 
  <PERSON>lette, 
  Music, 
  Globe, 
  Beaker, 
  Star, 
  Award, 
  Calculator, 
  Apple,
  Dumbbell,
  GraduationCap,
  Mic,
  Code,
  Heart
} from 'lucide-react';

interface ProgramSectionProps {
  showAfterSchool?: boolean;
}

const coreActivities = [
  { icon: BookOpen, title: 'Homework Help', description: 'Dedicated support for daily homework assignments' },
  { icon: Brain, title: 'Math and Language Art Enrichment', description: 'Advanced learning in key academic areas' },
  { icon: Heart, title: 'Yoga', description: 'Mind-body wellness through guided practice' },
  { icon: Palette, title: 'Arts and Crafts', description: 'Creative expression through various mediums' },
  { icon: Music, title: 'Cultural Dance', description: 'Bollywood, Folk, and Fusion dance styles' },
  { icon: Globe, title: 'Spanish Language', description: 'Interactive Spanish language learning' },
  { icon: Beaker, title: 'STEAM Workshops', description: 'Hands-on science and technology projects' },
  { icon: Star, title: 'Spelling Bee', description: 'Vocabulary and spelling competitions' },
  { icon: Puzzle, title: 'Critical Thinking', description: 'Problem-solving and logical reasoning' },
  { icon: Calculator, title: 'Math Workshops', description: 'Advanced mathematical concepts' },
  { icon: Apple, title: 'Healthy Snacks', description: 'Nutritious daily snacks provided' }
];

const additionalActivities = [
  { icon: GraduationCap, title: 'Chess', description: 'Strategic thinking and planning' },
  { icon: Dumbbell, title: 'Basketball', description: 'Physical fitness and team sports' }
];

const clubs = [
  { icon: Mic, title: 'Public Speaking', description: 'Confidence building through presentation skills' },
  { icon: Code, title: 'Coding Club', description: 'Introduction to programming concepts' },
  { icon: Heart, title: 'Community Service', description: 'Giving back to our local community' }
];

export default function LocationProgramSection({ showAfterSchool = true }: ProgramSectionProps) {
  return (
    <section className="py-20 bg-gradient-to-b from-white via-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header Section */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-5xl font-bold mb-6 text-[#001A6E] leading-tight">
            After School Programs
          </h2>
          {showAfterSchool && (
            <p className="text-xl text-gray-600 leading-relaxed">
              Tulip After School (TAS) provides a nurturing environment where students can thrive academically 
              and socially through our comprehensive enrichment programs.
            </p>
          )}
        </div>

        {/* Main Program Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {/* Core Activities */}
          <div className="lg:col-span-2 bg-white rounded-3xl shadow-xl overflow-hidden">
            <div className="relative h-[300px]">
              <Image
                src="/images/locations/dublin.jpg"
                alt="Students engaged in activities"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#001A6E]/90 to-transparent flex items-end">
                <div className="p-8">
                  <h3 className="text-3xl font-bold text-white mb-2">Core Activities</h3>
                  <p className="text-gray-200">Comprehensive academic and enrichment programs</p>
                </div>
              </div>
            </div>
            <div className="p-8">
              <div className="grid sm:grid-cols-2 gap-6">
                {coreActivities.map((activity, index) => (
                  <div 
                    key={index}
                    className="flex items-start gap-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 group"
                  >
                    <div className="p-3 rounded-xl bg-[#001A6E]/5 group-hover:bg-[#001A6E]/10 transition-colors">
                      <activity.icon className="w-6 h-6 text-[#001A6E]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">{activity.title}</h4>
                      <p className="text-sm text-gray-600">{activity.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Side Programs */}
          <div className="space-y-8">
            {/* Additional Activities */}
            <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
              <div className="p-8">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Additional Activities</h3>
                <div className="space-y-6">
                  {additionalActivities.map((activity, index) => (
                    <div 
                      key={index}
                      className="flex items-start gap-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 group"
                    >
                      <div className="p-3 rounded-xl bg-[#001A6E]/5 group-hover:bg-[#001A6E]/10 transition-colors">
                        <activity.icon className="w-6 h-6 text-[#001A6E]" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{activity.title}</h4>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Clubs */}
            <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
              <div className="p-8">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Special Interest Clubs</h3>
                <div className="space-y-6">
                  {clubs.map((club, index) => (
                    <div 
                      key={index}
                      className="flex items-start gap-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 group"
                    >
                      <div className="p-3 rounded-xl bg-[#001A6E]/5 group-hover:bg-[#001A6E]/10 transition-colors">
                        <club.icon className="w-6 h-6 text-[#001A6E]" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{club.title}</h4>
                        <p className="text-sm text-gray-600">{club.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Banner */}
        <div className="relative h-[200px] rounded-3xl overflow-hidden shadow-xl">
          <Image
            src="/images/locations/san-francisco.jpg"
            alt="Children playing basketball"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 to-transparent flex items-center">
            <div className="p-8 max-w-2xl">
              <h3 className="text-2xl font-bold text-white mb-3">Join Our After School Program</h3>
              <p className="text-gray-200">
                Experience a perfect blend of academic support and enriching activities in a safe, 
                nurturing environment.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
