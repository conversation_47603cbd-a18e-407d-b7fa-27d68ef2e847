'use client';

import Image from 'next/image';
import { useState } from 'react';
import { Award, GraduationCap, Users, Star } from 'lucide-react';

interface DirectorBioProps {
  location: string;
  director: {
    name: string;
    role: string;
    image: string;
    bio: string[];
    achievements?: string[];
  };
}

const DirectorBio = ({ location, director }: DirectorBioProps) => {
  const [imageError, setImageError] = useState(false);
  
  if (!director) return null;

  return (
    <div className="max-w-7xl mx-auto">
      {/* Section Header */}
      <div className="text-center mb-16">
        <span className="text-[#009990] font-medium text-sm uppercase tracking-wider">Our Leadership</span>
        <h2 className="mt-4 text-4xl font-bold text-[#001A6E] capitalize">Meet Our Director</h2>
        <div className="mt-4 w-24 h-1 bg-gradient-to-r from-[#074799] to-[#009990] mx-auto rounded-full"></div>
      </div>

      <div className="grid lg:grid-cols-12 gap-8 items-start">
        {/* Left Column - Image */}
        <div className="lg:col-span-5">
          <div className="sticky top-8">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-[#074799] to-[#009990] rounded-3xl transform rotate-6 scale-[0.98] opacity-20 transition-transform group-hover:rotate-4 group-hover:scale-[0.99]"></div>
              <div className="relative aspect-[4/5] rounded-3xl overflow-hidden shadow-xl">
                <Image
                  src={director.image}
                  alt={director.name}
                  fill
                  className="object-cover transform group-hover:scale-105 transition-transform duration-700"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  onError={() => setImageError(true)}
                  priority
                />
              </div>
              {/* Name Card Overlay */}
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 w-[90%] bg-white rounded-2xl shadow-xl p-4 text-center">
                <h3 className="text-2xl font-bold text-[#001A6E]">{director.name}</h3>
                <p className="text-[#009990] font-medium">{director.role}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Content */}
        <div className="lg:col-span-7 space-y-8 pt-8 lg:pt-0">
          {/* Qualifications */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-4 mb-3">
                <div className="w-12 h-12 rounded-xl bg-[#E6FFF9] flex items-center justify-center">
                  <GraduationCap className="w-6 h-6 text-[#009990]" />
                </div>
                <h4 className="text-lg font-semibold text-[#001A6E]">Education</h4>
              </div>
              <ul className="space-y-2 text-gray-600">
                <li>• Bachelor of Arts Degree</li>
                <li>• Bachelor of Science in Education</li>
                <li>• Master of Arts in Education</li>
              </ul>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-4 mb-3">
                <div className="w-12 h-12 rounded-xl bg-[#E6FFF9] flex items-center justify-center">
                  <Star className="w-6 h-6 text-[#009990]" />
                </div>
                <h4 className="text-lg font-semibold text-[#001A6E]">Expertise</h4>
              </div>
              <ul className="space-y-2 text-gray-600">
                <li>• Early Childhood Education</li>
                <li>• Child Development</li>
                <li>• Educational Leadership</li>
              </ul>
            </div>
          </div>

          {/* Bio Sections */}
          <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-xl bg-[#E6FFF9] flex items-center justify-center">
                <Users className="w-6 h-6 text-[#009990]" />
              </div>
              <h4 className="text-xl font-semibold text-[#001A6E]">About {director.name}</h4>
            </div>
            <div className="space-y-6">
              {director.bio.map((paragraph, index) => (
                <p 
                  key={index} 
                  className={`text-gray-600 leading-relaxed ${index === 0 ? 'first-letter:text-4xl first-letter:font-bold first-letter:text-[#001A6E] first-letter:mr-1 first-letter:float-left' : ''}`}
                >
                  {paragraph}
                </p>
              ))}
            </div>
          </div>

          {/* Experience Highlights */}
          <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-xl bg-[#E6FFF9] flex items-center justify-center">
                <Award className="w-6 h-6 text-[#009990]" />
              </div>
              <h4 className="text-xl font-semibold text-[#001A6E]">Experience Highlights</h4>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 bg-gray-50 rounded-xl p-4">
                <span className="text-[#009990] font-semibold">10+</span>
                <span className="text-gray-600">Years at Tulip Kids Academy</span>
              </div>
              <div className="flex items-center gap-3 bg-gray-50 rounded-xl p-4">
                <span className="text-[#009990] font-semibold">15+</span>
                <span className="text-gray-600">Years in Education</span>
              </div>
              <div className="flex items-center gap-3 bg-gray-50 rounded-xl p-4">
                <span className="text-[#009990] font-semibold">50+</span>
                <span className="text-gray-600">Professional Development Hours</span>
              </div>
              <div className="flex items-center gap-3 bg-gray-50 rounded-xl p-4">
                <span className="text-[#009990] font-semibold">100+</span>
                <span className="text-gray-600">Families Impacted</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectorBio;
