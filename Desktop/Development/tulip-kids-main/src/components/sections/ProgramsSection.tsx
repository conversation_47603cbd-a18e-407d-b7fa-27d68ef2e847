'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Baby, 
  Brain, 
  Palette, 
  Heart, 
  Users, 
  GraduationCap,
  BookOpen,
  Puzzle,
  Lightbulb,
  ShieldCheck,
  Sparkles,
  Shapes,
  LucideIcon
} from 'lucide-react';
import Link from 'next/link';

interface Program {
  id: string;
  title: string;
  ageRange: string;
  description: string;
  features: {
    icon: LucideIcon;
    title: string;
    description: string;
  }[];
  color: string;
}

const programs: Program[] = [
  {
    id: 'infant-toddler',
    title: 'Infant/Toddler Program',
    ageRange: '12 - 24 months',
    description: 'A nurturing environment where infants and toddlers develop essential skills through sensory experiences and guided exploration.',
    features: [
      {
        icon: BookOpen,
        title: 'Theme Based Curriculum',
        description: 'Age-appropriate themes that engage and stimulate young minds'
      },
      {
        icon: Puzzle,
        title: 'Learning through Play',
        description: 'Interactive activities that promote natural discovery'
      },
      {
        icon: Palette,
        title: 'Arts & Crafts',
        description: 'Creative activities developing fine motor skills'
      },
      {
        icon: Brain,
        title: 'Motor Development',
        description: 'Activities promoting physical growth and coordination'
      },
      {
        icon: Heart,
        title: 'Social & Emotional Development',
        description: 'Building confidence and social connections'
      },
      {
        icon: ShieldCheck,
        title: 'Safe Environment',
        description: 'Secure, clean spaces designed for young explorers'
      },
      {
        icon: Users,
        title: 'Nurturing Staff',
        description: "Experienced caregivers dedicated to your child's growth"
      }
    ],
    color: 'from-pink-500 to-purple-500'
  },
  {
    id: 'jr-preschool',
    title: 'Jr. Preschool Program',
    ageRange: '2 - 3 years',
    description: 'A structured program that builds independence and foundational learning skills through guided activities and play.',
    features: [
      {
        icon: BookOpen,
        title: 'Theme Based Curriculum',
        description: 'Engaging themes that spark curiosity and learning'
      },
      {
        icon: Puzzle,
        title: 'Learning through Play',
        description: 'Playful activities that teach fundamental concepts'
      },
      {
        icon: Palette,
        title: 'Arts & Crafts',
        description: 'Creative expression and skill development'
      },
      {
        icon: Brain,
        title: 'Motor Development',
        description: 'Activities enhancing physical abilities'
      },
      {
        icon: Heart,
        title: 'Social & Emotional Growth',
        description: "Building relationships and emotional intelligence"
      },
      {
        icon: ShieldCheck,
        title: 'Safe Environment',
        description: 'Protected spaces for active learning'
      },
      {
        icon: Users,
        title: 'Nurturing Staff',
        description: "Caring teachers guiding development"
      }
    ],
    color: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'preschool',
    title: 'Preschool Program',
    ageRange: '3 - 4 years',
    description: 'A comprehensive program focusing on kindergarten readiness through structured learning and creative exploration.',
    features: [
      {
        icon: BookOpen,
        title: 'Mother-Goose Curriculum',
        description: 'Classic themes promoting literacy and learning'
      },
      {
        icon: Brain,
        title: 'Whole Child Approach',
        description: 'Balanced development of mind and body'
      },
      {
        icon: Shapes,
        title: 'Core Subjects',
        description: 'Math, Science, Social Studies, and Language Arts'
      },
      {
        icon: GraduationCap,
        title: 'Literacy Development',
        description: 'Building strong reading and writing foundations'
      },
      {
        icon: Heart,
        title: 'Independence Skills',
        description: "Fostering self-reliance and confidence"
      },
      {
        icon: Sparkles,
        title: 'Creative Environment',
        description: 'Spaces that inspire imagination'
      },
      {
        icon: Lightbulb,
        title: 'Curiosity & Discovery',
        description: 'Encouraging exploration and learning'
      }
    ],
    color: 'from-green-500 to-teal-500'
  },
  {
    id: 'pre-k',
    title: 'Pre-Kindergarten Program',
    ageRange: '4 - 5 years',
    description: 'An advanced program preparing children for kindergarten success through comprehensive academic and social development.',
    features: [
      {
        icon: BookOpen,
        title: 'Advanced Curriculum',
        description: 'Comprehensive preparation for kindergarten'
      },
      {
        icon: Brain,
        title: 'Academic Focus',
        description: 'Strong emphasis on core subjects'
      },
      {
        icon: Shapes,
        title: 'STEAM Activities',
        description: 'Science, Technology, Engineering, Arts & Math'
      },
      {
        icon: GraduationCap,
        title: 'Reading Readiness',
        description: 'Advanced literacy and phonics'
      },
      {
        icon: Heart,
        title: 'Social Skills',
        description: "Leadership and collaboration"
      },
      {
        icon: Sparkles,
        title: 'Creative Expression',
        description: 'Art, music, and dramatic play'
      },
      {
        icon: Lightbulb,
        title: 'Critical Thinking',
        description: 'Problem-solving and reasoning skills'
      }
    ],
    color: 'from-orange-500 to-red-500'
  }
];

export default function ProgramsSection() {
  const [selectedProgram, setSelectedProgram] = useState<string>(programs[0].id);

  const currentProgram = programs.find(p => p.id === selectedProgram)!;

  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-[42px] font-bold text-[#001A6E] mb-6">
            Our Programs
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our age-appropriate programs designed to nurture your child's development at every stage.
          </p>
        </div>

        {/* Program Selector */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {programs.map((program) => (
            <button
              key={program.id}
              onClick={() => setSelectedProgram(program.id)}
              className={`px-6 py-3 rounded-full text-sm md:text-base font-medium transition-all duration-300 ${
                selectedProgram === program.id
                ? `bg-gradient-to-r ${program.color} text-white shadow-lg scale-105`
                : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              {program.title}
            </button>
          ))}
        </div>

        {/* Program Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedProgram}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="max-w-7xl mx-auto"
          >
            <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
              <div className={`bg-gradient-to-r ${currentProgram.color} p-8 text-white`}>
                <h3 className="text-3xl font-bold mb-2">{currentProgram.title}</h3>
                <p className="text-lg opacity-90 mb-4">Age Range: {currentProgram.ageRange}</p>
                <p className="text-lg opacity-90">{currentProgram.description}</p>
              </div>

              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {currentProgram.features.map((feature, index) => {
                    const Icon = feature.icon;
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start space-x-4"
                      >
                        <div className="flex-shrink-0">
                          <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${currentProgram.color} bg-opacity-10 flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 mb-2">
                            {feature.title}
                          </h4>
                          <p className="text-gray-600">
                            {feature.description}
                          </p>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <Link
            href="/contact"
            className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
          >
            Schedule a Tour
          </Link>
        </div>
      </div>
    </section>
  );
}
