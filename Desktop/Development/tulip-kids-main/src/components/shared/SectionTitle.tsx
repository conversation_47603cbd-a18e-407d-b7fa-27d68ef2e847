'use client'

import React from 'react';

interface SectionTitleProps {
  title: string;
  description?: string;
}

const SectionTitle = ({ title, description }: SectionTitleProps) => {
  return (
    <div className="text-center mb-16">
      <h2 className="text-4xl font-bold text-[#001A6E] mb-4">{title}</h2>
      {description && (
        <p className="text-gray-600 max-w-2xl mx-auto">
          {description}
        </p>
      )}
    </div>
  );
};

export default SectionTitle;