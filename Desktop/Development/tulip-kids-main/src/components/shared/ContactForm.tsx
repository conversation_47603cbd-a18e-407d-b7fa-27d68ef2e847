'use client'

import { useState, useEffect } from 'react'
import { Phone, Mail, Clock, MapPin, Send } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { contactInfo } from '@/data/contact'
import {
  getAvailableProgramsForLocation,
  getLocationNamesByCountry
} from '@/data/programs'
import { Suspense } from 'react'
import { trackEvent, trackContactFormConversion } from '@/utils/analytics'
import { useSafeSearchParams } from '@/components/utils/SearchParamsProvider'

interface FormState {
  country: 'usa' | 'india'
  location: string
  name: string
  email: string
  phone: string
  childAge: string
  program: string
  preferredTime: string
  message: string
}

interface ContactFormProps {
  className?: string;
}

// Define a safe interface for search params
interface SafeSearchParams {
  getParam: (key: string) => string;
}

function ContactFormContent({ searchParams }: { searchParams: SafeSearchParams }) {
  const router = useRouter();

  // Get country from URL params, default to 'usa' if not provided
  const countryParam = searchParams.getParam('country');
  const initialCountry = (countryParam === 'india' || countryParam === 'usa') ? countryParam : 'usa';

  const [selectedCountry, setSelectedCountry] = useState<'usa' | 'india'>(initialCountry)
  const [status, setStatus] = useState<{
    type: 'success' | 'error' | 'info' | null;
    message: string;
  }>({ type: null, message: '' });

  const [formState, setFormState] = useState<FormState>({
    country: initialCountry,
    location: searchParams.getParam('location'),
    name: searchParams.getParam('name'),
    email: searchParams.getParam('email'),
    phone: searchParams.getParam('phone'),
    childAge: searchParams.getParam('childAge'),
    program: searchParams.getParam('program'),
    preferredTime: searchParams.getParam('preferredTime'),
    message: searchParams.getParam('message')
  });

  const [availableLocations, setAvailableLocations] = useState<Array<{ id: string; name: string }>>([]);
  const [availablePrograms, setAvailablePrograms] = useState<string[]>([]);

  useEffect(() => {
    const locations = getLocationNamesByCountry(formState.country);
    setAvailableLocations(locations);
    setFormState(prev => ({
      ...prev,
      location: '',
      program: ''
    }));
  }, [formState.country]);

  useEffect(() => {
    if (formState.location) {
      const programs = getAvailableProgramsForLocation(formState.location);
      setAvailablePrograms(programs);
      if (!programs.includes(formState.program)) {
        setFormState(prev => ({
          ...prev,
          program: ''
        }));
      }
    } else {
      setAvailablePrograms([]);
    }
  }, [formState.location]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'country') {
      if (value === 'usa' || value === 'india') {
        // Track country selection change
        trackEvent('contact_form_country_change', {
          country: value
        });

        setFormState(prev => ({
          ...prev,
          country: value as 'usa' | 'india',
          location: '',
          program: ''
        }));
      }
    } else if (name === 'location' && value) {
      // Track location selection
      trackEvent('contact_form_location_select', {
        country: formState.country,
        location: value
      });

      setFormState(prev => ({ ...prev, [name]: value }));
    } else if (name === 'program' && value) {
      // Track program selection
      trackEvent('contact_form_program_select', {
        country: formState.country,
        location: formState.location,
        program: value
      });

      setFormState(prev => ({ ...prev, [name]: value }));
    } else {
      setFormState(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setStatus({ type: 'info', message: 'Sending...' });

    try {
      // Track form submission attempt
      trackEvent('contact_form_submit_attempt', {
        country: formState.country,
        location: formState.location,
        program: formState.program,
        childAge: formState.childAge
      });

      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      });

      if (!response.ok) {
        // Track form submission failure
        trackEvent('contact_form_submit_error', {
          country: formState.country,
          error: 'API response not OK'
        });
        throw new Error('Failed to send message');
      }

      // Track successful form submission
      trackEvent('contact_form_submit_success', {
        country: formState.country,
        location: formState.location,
        program: formState.program
      });

      // Track Google Ads conversion
      trackContactFormConversion(1.0);
      console.log('Contact form conversion tracked on submit');

      // Redirect to thank you page on success with country and location parameters
      router.push(`/thank-you?country=${formState.country.toLowerCase()}&location=${formState.location}`);
    } catch (error) {
      // Track form submission error
      trackEvent('contact_form_submit_error', {
        country: formState.country,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      setStatus({
        type: 'error',
        message: 'Failed to send message. Please try again.'
      });
    }
  }

  const isSubmitting = status.type === 'info';

  return (
    <div className="w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden">
      <div className="p-8">
        <h2 className="text-2xl font-bold text-[#001A6E] mb-8">Get in Touch</h2>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Country Selection */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={() => handleInputChange({ target: { name: 'country', value: 'usa' } } as any)}
              className={`flex-1 px-4 py-3 rounded-xl text-sm font-medium transition-all ${formState.country === 'usa'
                ? 'bg-[#001A6E] text-white shadow-lg shadow-[#001A6E]/20 scale-[1.02]'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              United States
            </button>
            <button
              type="button"
              onClick={() => handleInputChange({ target: { name: 'country', value: 'india' } } as any)}
              className={`flex-1 px-4 py-3 rounded-xl text-sm font-medium transition-all ${formState.country === 'india'
                ? 'bg-[#001A6E] text-white shadow-lg shadow-[#001A6E]/20 scale-[1.02]'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              India
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Location Selection */}
            <div className="md:col-span-2">
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                Location
              </label>
              <select
                id="location"
                name="location"
                value={formState.location}
                onChange={handleInputChange}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors bg-white"
                required
              >
                <option value="">Select a location</option>
                {availableLocations.map(location => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Program Selection */}
            <div className="md:col-span-2">
              <label htmlFor="program" className="block text-sm font-medium text-gray-700 mb-2">
                Program
              </label>
              <select
                id="program"
                name="program"
                value={formState.program}
                onChange={handleInputChange}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors bg-white disabled:bg-gray-50 disabled:text-gray-500"
                required
                disabled={!formState.location}
              >
                <option value="">Select a program</option>
                {availablePrograms.map(program => (
                  <option key={program} value={program}>
                    {program}
                  </option>
                ))}
              </select>
            </div>

            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Your Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formState.name}
                onChange={handleInputChange}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                required
              />
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formState.email}
                onChange={handleInputChange}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                required
              />
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <div className="relative">
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 font-medium">
                  {formState.country === 'india' ? '+91' : '+1'}
                </span>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formState.phone}
                  onChange={handleInputChange}
                  className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
                  required
                />
              </div>
            </div>

            {/* Child's Age */}
            <div>
              <label htmlFor="childAge" className="block text-sm font-medium text-gray-700 mb-2">
                Child's Age (Optional)
              </label>
              <input
                type="text"
                id="childAge"
                name="childAge"
                value={formState.childAge}
                onChange={handleInputChange}
                placeholder="e.g., 3 years"
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
              />
            </div>

            {/* Preferred Time */}
            <div>
              <label htmlFor="preferredTime" className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Contact Time (Optional)
              </label>
              <input
                type="text"
                id="preferredTime"
                name="preferredTime"
                value={formState.preferredTime}
                onChange={handleInputChange}
                placeholder="e.g., Morning, Afternoon"
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors"
              />
            </div>

            {/* Message */}
            <div className="md:col-span-2">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formState.message}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-[#001A6E]/20 focus:border-[#001A6E] transition-colors resize-none"
                required
              />
            </div>
          </div>

          {/* Contact Info */}
          <div className="border-t border-gray-200 pt-8 mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-[#001A6E]" />
                <span>{contactInfo[formState.country].phone}</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-[#001A6E]" />
                <span>{contactInfo[formState.country].email}</span>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[#001A6E] text-white py-3 px-6 rounded-xl font-medium
                     hover:bg-[#002288] transition-colors duration-200
                     disabled:opacity-70 disabled:cursor-not-allowed
                     focus:outline-none focus:ring-2 focus:ring-[#001A6E]/50
                     shadow-lg shadow-[#001A6E]/20"
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center gap-2">
                <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Sending...
              </span>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <Send className="w-5 h-5" />
                Send Message
              </span>
            )}
          </button>
        </form>
      </div>
    </div>
  )
}

function SearchParamsWrapper() {
  const safeSearchParams = useSafeSearchParams()
  return <ContactFormContent searchParams={safeSearchParams} />
}

export default function ContactForm({ className = '' }: ContactFormProps) {
  return (
    <div className={className}>
      <Suspense fallback={<div>Loading...</div>}>
        <SearchParamsWrapper />
      </Suspense>
    </div>
  )
}