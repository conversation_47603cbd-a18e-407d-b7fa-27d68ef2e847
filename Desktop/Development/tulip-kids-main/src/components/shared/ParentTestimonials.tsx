'use client'

import React from 'react';
import { Star, Quote } from 'lucide-react';
import SectionTitle from '../shared/SectionTitle';

interface Testimonial {
  id: number;
  name: string;
  role: string;
  image: string;
  quote: string;
  rating: number;
  location: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON>',
    role: 'Parent of Alex, Age 4',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80&w=150&h=150',
    quote: 'My son had his first daycare experience at Tulip Kids Academy, and it was exceptional. The staff genuinely cares for the children, creating a nurturing environment that made my son feel loved and secure.',
    rating: 5,
    location: 'Santa Clara'
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    role: '<PERSON><PERSON> of NIck, Age 3',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=150&h=150',
    quote: 'My <PERSON> is thriving in her pre-K class at Tulip Kids Academy. The patient teachers helped her adjust and learn English step by step, ensuring she feels safe and cared for, even during early drop-offs.',
    rating: 5,
    location: '<PERSON> Clara'
  },
  {
    id: 3,
    name: '<PERSON>ianna <PERSON>a',
    role: 'Parent of <PERSON>, Age 5',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=150&h=150',
    quote: 'Since enrolling my daughter at Tulip Kids Academy in October 2022, I have seen significant growth in her learning. The tour was fantastic, allowing her to participate in activities that confirmed it was the right fit for her.',
    rating: 5,
    location: 'Sunnyvale'
  }
];

const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => (
  <div className="group h-full bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden">
    {/* Top Decorative Bar */}
    <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#009990] to-[#E1FFBB]"></div>
    
    {/* Main Content */}
    <div className="p-8 h-full flex flex-col">
      {/* Profile Section */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-shrink-0">
          <img 
            src={testimonial.image} 
            alt={testimonial.name}
            className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-md"
          />
          <div className="absolute -right-1 -bottom-1 bg-[#009990] text-white p-1 rounded-full shadow-md">
            <Quote size={12} />
          </div>
        </div>
        <div>
          <h3 className="font-bold text-[#074799] group-hover:text-[#009990] transition-colors duration-300">
            {testimonial.name}
          </h3>
          <p className="text-sm text-gray-600">{testimonial.role}</p>
          <div className="flex items-center gap-2 mt-1">
            <span className="inline-block w-1.5 h-1.5 rounded-full bg-[#009990]"></span>
            <p className="text-sm text-[#009990]">{testimonial.location}</p>
          </div>
        </div>
      </div>

      {/* Rating */}
      <div className="flex gap-0.5 mb-4">
        {[...Array(testimonial.rating)].map((_, i) => (
          <Star 
            key={i} 
            size={16} 
            className="fill-[#E1FFBB] text-[#009990] transition-transform duration-300 group-hover:scale-110" 
            style={{ transitionDelay: `${i * 50}ms` }}
          />
        ))}
      </div>

      {/* Quote */}
      <div className="relative flex-grow">
        <p className="text-gray-600 text-sm leading-relaxed">
          {testimonial.quote}
        </p>
      </div>
    </div>
  </div>
);

const Testimonials = () => {
  return (
    <section className="py-24 bg-gradient-to-b from-white via-gray-50/30 to-white [&_.fade-in]:animate-fadeIn">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <SectionTitle 
            title="What Parents Say"
            description="Join thousands of happy parents who trust Tulip Kids with their children's early education"
          />
        </div>

        {/* Testimonial Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className="transform h-full fade-in"
              style={{ 
                '--delay': `${index * 200}ms`,
                animationDelay: `${index * 200}ms`,
                opacity: 0
              } as React.CSSProperties}
            >
              <TestimonialCard testimonial={testimonial} />
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="flex justify-center">
          <div className="bg-white rounded-2xl shadow-lg p-8 grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            <div className="text-center px-4 md:px-6">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-[#E1FFBB]/20 rounded-full blur-2xl"></div>
                <h4 className="text-3xl font-bold text-[#074799] relative">98%</h4>
              </div>
              <p className="text-sm font-medium text-gray-600 mt-2">Parent Satisfaction</p>
            </div>
            <div className="text-center px-4 md:px-6 relative md:border-x border-gray-100">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-[#E1FFBB]/20 rounded-full blur-2xl"></div>
                <h4 className="text-3xl font-bold text-[#074799] relative">15+</h4>
              </div>
              <p className="text-sm font-medium text-gray-600 mt-2">Years Experience</p>
            </div>
            <div className="text-center px-4 md:px-6">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-[#E1FFBB]/20 rounded-full blur-2xl"></div>
                <h4 className="text-3xl font-bold text-[#074799] relative">8+</h4>
              </div>
              <p className="text-sm font-medium text-gray-600 mt-2">Centers</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;