'use client'

import React, { useState } from 'react';
import { Calendar, Clock } from 'lucide-react';
// import Button from '../shared/Button';
import IconButton from '../shared/IconButton';
import SectionTitle from '../shared/SectionTitle';
import Link from 'next/link';

const ageGroups = ['Infant', 'Toddler', 'Preschool', 'Pre-K'];

const scheduleData = {
  'Infant': [
    { time: '9:00 AM', activity: 'Arrival & Free Play' },
    { time: '10:00 AM', activity: 'Sensory Activities' },
    { time: '11:00 AM', activity: 'Outdoor Time' },
    { time: '12:00 PM', activity: 'Lunch & Rest' }
  ],
  'Toddler': [
    { time: '9:00 AM', activity: 'Circle Time' },
    { time: '10:00 AM', activity: 'Art & Craft' },
    { time: '11:00 AM', activity: 'Music & Movement' },
    { time: '12:00 PM', activity: 'Lunch Time' }
  ],
  'Preschool': [
    { time: '9:00 AM', activity: 'Morning Meeting' },
    { time: '10:00 AM', activity: 'Learning Centers' },
    { time: '11:00 AM', activity: 'STEAM Activities' },
    { time: '12:00 PM', activity: 'Lunch & Stories' }
  ],
  'Pre-K': [
    { time: '9:00 AM', activity: 'Academic Time' },
    { time: '10:00 AM', activity: 'Project Work' },
    { time: '11:00 AM', activity: 'Physical Education' },
    { time: '12:00 PM', activity: 'Lunch & Journal' }
  ]
};

type AgeGroup = 'Infant' | 'Toddler' | 'Preschool' | 'Pre-K';

const ProgramSchedule: React.FC = () => {
  const [selectedAge, setSelectedAge] = useState<AgeGroup>('Infant');

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <SectionTitle 
          title="Daily Schedule"
          description="Experience a day filled with learning, play, and growth"
        />

        <div className="max-w-4xl mx-auto">
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            {ageGroups.map((age) => (
              <button
                key={age}
                onClick={() => setSelectedAge(age as AgeGroup)}
                className={`px-6 py-2 rounded-full ${
                  selectedAge === age
                    ? 'bg-[#009990] text-white'
                    : 'bg-[#009990] text-white hover:bg-[#074799]'
                }`}
              >
                {age}
              </button>
            ))}
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="grid gap-6">
              {scheduleData[selectedAge].map((item, index) => (
                <div key={index} className="flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50">
                  <Clock className="text-[#009990]" />
                  <div>
                    <p className="font-semibold text-[#074799]">{item.time}</p>
                    <p className="text-gray-600">{item.activity}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8 flex justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
              >
                Schedule a Tour
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProgramSchedule;