'use client'

import React from 'react';
import Link from 'next/link';

interface BaseButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  icon?: React.ComponentType<any>;
}

interface ButtonAsButtonProps extends BaseButtonProps, React.ButtonHTMLAttributes<HTMLButtonElement> {
  href?: never;
}

interface ButtonAsLinkProps extends BaseButtonProps {
  href: string;
  children: React.ReactNode;
}

type ButtonProps = ButtonAsButtonProps | ButtonAsLinkProps;

const Button = ({ variant = 'primary', className = '', href, icon: Icon, children, ...props }: ButtonProps) => {
  const baseStyles = "px-6 py-2 rounded-full transition-colors inline-flex items-center justify-center gap-2 whitespace-nowrap";
  
  const variants = {
    primary: "bg-[#074799] hover:bg-[#E1FFBB] hover:text-[#074799] text-white",
    secondary: "bg-[#074799] text-white hover:bg-[#E1FFBB] hover:text-[#074799]",
    outline: "border-2 border-[#074799] text-[#074799] hover:bg-[#074799] hover:text-white"
  };

  const classes = `${baseStyles} ${variants[variant]} ${className}`;

  const content = (
    <>
      {children}
      {Icon && <Icon className="w-5 h-5" />}
    </>
  );

  if (href) {
    return (
      <Link href={href} className={classes}>
        {content}
      </Link>
    );
  }

  return (
    <button className={classes} {...props}>
      {content}
    </button>
  );
};

export default Button;