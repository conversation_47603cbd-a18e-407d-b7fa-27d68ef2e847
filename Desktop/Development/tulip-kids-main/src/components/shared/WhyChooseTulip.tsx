import React from 'react';
import { Award, BookOpen, Heart, Shield, CheckCircle2, <PERSON>, Brain, Sparkles } from 'lucide-react';
import Image from 'next/image';
import SectionTitle from '../shared/SectionTitle';

interface Benefit {
  title: string;
  icon: React.ElementType;
  description: string;
  color: string;
}

interface BenefitCardProps {
  benefit: Benefit;
}

const benefits = [
  {
    title: 'Expert Educators',
    icon: Award,
    description: 'Our certified teachers bring years of experience in early childhood education',
    color: '#E1FFBB'
  },
  {
    title: 'Innovative Curriculum',
    icon: Brain,
    description: 'Research-based program designed to nurture every aspect of development',
    color: '#FFE4D6'
  },
  {
    title: 'Safe Environment',
    icon: Shield,
    description: 'State-of-the-art security and strict safety protocols for peace of mind',
    color: '#D5F5FF'
  },
  {
    title: 'Personalized Care',
    icon: Heart,
    description: "Individual attention and care tailored to each child's unique needs",
    color: '#FFD6E8'
  },
  {
    title: 'Social Development',
    icon: Users,
    description: 'Fostering friendship, teamwork, and essential social skills',
    color: '#E6E6FF'
  },
  {
    title: 'Subsidy Programs',
    icon: Sparkles,
    description: 'We partner with programs like Choices for Children, Go Kids, CalWorks to make quality childcare affordable and accessible for all families.',
    color: '#FFDFD6'
  }
];

const BenefitCard = ({ benefit }: BenefitCardProps) => (
  <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 p-6 group">
    <div className="flex items-start gap-4">
      <div 
        className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-500"
        style={{ backgroundColor: benefit.color }}
      >
        <benefit.icon className="w-6 h-6 text-[#074799] group-hover:text-[#009990] transition-colors duration-300" />
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-bold text-[#074799] group-hover:text-[#009990] transition-colors duration-300 mb-2">
          {benefit.title}
        </h3>
        <p className="text-gray-600 text-sm leading-relaxed">
          {benefit.description}
        </p>
      </div>
    </div>
  </div>
);

const WhyChooseUs = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50" id="why-us">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <h2 className="text-4xl font-bold text-[#074799] mb-4">
            Why Parents Choose Tulip Kids
          </h2>
          <p className="text-gray-600 text-lg">
            We create an environment where learning is fun, creativity flourishes, and every child's potential is nurtured.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {benefits.map((benefit) => (
            <BenefitCard key={benefit.title} benefit={benefit} />
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 bg-[#E1FFBB] text-[#074799] px-6 py-3 rounded-full font-medium">
            <CheckCircle2 className="w-5 h-5" />
            Trusted by 15000+ families across USA and India
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;