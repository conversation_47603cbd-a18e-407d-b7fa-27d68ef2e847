import React from 'react';
import { Baby, School, Users, GraduationCap } from 'lucide-react';
import Button from '../shared/Button';
import SectionTitle from '../shared/SectionTitle';

// Define types for better type safety
type IconType = typeof Baby | typeof School | typeof Users | typeof GraduationCap;
type CountryType = 'usa' | 'india';

export interface Program {
  title: string;
  age: string;
  icon: IconType;
  description: string;
  link?: string;
}

interface ProgramCardProps {
  program: Program;
}

interface ProgramsOverviewProps {
  title?: string;
  description?: string;
  programs?: Program[];
  className?: string;
  country?: CountryType;
}

// India-specific programs
const indiaPrograms: Program[] = [
  {
    title: 'Junior Pre-School',
    age: '2-3 years',
    icon: Baby,
    description: 'Foundation program focusing on early development through nurturing care and age-appropriate activities.',
    link: '/programs/india/junior-pre-school'
  },
  {
    title: 'Preschool Program',
    age: '3-4 years',
    icon: School,
    description: 'Structured learning program incorporating play-based activities and social interaction.',
    link: '/programs/india/preschool'
  },
  {
    title: 'Pre-K Program',
    age: '4-5 years',
    icon: Users,
    description: 'Comprehensive program focusing on early literacy, creative expression, and school readiness.',
    link: '/programs/india/pre-k'
  },
  {
    title: 'Kindergarten',
    age: '5-6 years',
    icon: GraduationCap,
    description: 'Advanced program preparing children for school with focus on independent learning and advanced concepts.',
    link: '/programs/india/kindergarten'
  },
];

// USA-specific programs
const usaPrograms: Program[] = [
  {
    title: 'Infant Care',
    age: '12-24 months',
    icon: Baby,
    description: 'Nurtures infants development through responsive caregiving and a safe, stimulating environment.',
    link: '/programs/usa/infant-care'
  },
  {
    title: 'Jr. Preschool',
    age: '2-3 years',
    icon: School,
    description: 'Encourages social interaction and foundational learning through play-based activities.',
    link: '/programs/usa/jr-preschool'
  },
  {
    title: 'Preschool',
    age: '3-4 years',
    icon: Users,
    description: 'Provides a comprehensive curriculum focusing on holistic development for school readiness.',
    link: '/programs/usa/preschool'
  },
  {
    title: 'Pre-K',
    age: '4-5 years',
    icon: GraduationCap,
    description: 'Prepares children for kindergarten with targeted skills in literacy, math, and social-emotional growth.',
    link: '/programs/usa/pre-k'
  },
];

const ProgramCard = ({ program }: ProgramCardProps) => (
  <div className="bg-white rounded-xl shadow-lg p-6 hover:transform hover:-translate-y-2 transition-all duration-300">
    <div className="w-16 h-16 bg-[#E1FFBB] rounded-full flex items-center justify-center mb-6 mx-auto">
      <program.icon size={32} className="text-[#009990]" />
    </div>
    <h3 className="text-xl font-bold text-[#074799] mb-2 text-center">{program.title}</h3>
    <p className="text-sm text-gray-600 mb-2 text-center">{program.age}</p>
    <p className="text-gray-700 mb-6 text-center">{program.description}</p>
    {program.link && (
      <Button 
        variant="secondary" 
        className="w-full"
        href={program.link}
      >
        Learn More
      </Button>
    )}
  </div>
);

const ProgramsOverview = ({ 
  title = "Our Programs",
  description = "Age-appropriate programs designed to nurture every aspect of your child's development",
  programs,
  className = "",
  country = "usa"
}: ProgramsOverviewProps) => {
  const selectedPrograms = country === 'india' ? indiaPrograms : usaPrograms;
  const finalPrograms = programs || selectedPrograms;

  return (
    <div className={`py-12 ${className}`}>
      <SectionTitle
        title={title}
        description={description}
        // className="text-center mb-12"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {finalPrograms.map((program, index) => (
          <ProgramCard key={index} program={program} />
        ))}
      </div>
    </div>
  );
};

export default ProgramsOverview;