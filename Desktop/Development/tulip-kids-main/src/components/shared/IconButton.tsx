'use client'

import React from 'react';
import Button from './Button';
import { LucideIcon } from 'lucide-react';

interface IconButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  icon: LucideIcon;
  href?: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
}

const IconButton: React.FC<IconButtonProps> = ({
  variant,
  icon: Icon,
  href,
  className,
  children,
  onClick,
}) => {
  return (
    <Button
      variant={variant}
      href={href}
      className={className}
      onClick={onClick}
    >
      <Icon size={20} className="inline-block" />
      {children}
    </Button>
  );
};

export default IconButton;
