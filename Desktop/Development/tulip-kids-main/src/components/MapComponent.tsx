'use client';
// components/MapComponent.tsx
import React, { useState } from 'react';

const MapComponent = () => {
  const [selectedState, setSelectedState] = useState('');
  
  return (
    <div className="w-full">
      <div className="bg-white p-4 rounded-lg mb-4">
        <h3 className="text-lg font-bold text-[#074799] mb-2">
          Select Your Territory in {selectedState || 'India'}
        </h3>
        {/* Simple territory selection */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Uttar Pradesh'].map((state) => (
            <button
              key={state}
              className={`p-3 rounded-lg text-sm transition-all duration-300 ${
                selectedState === state 
                  ? 'bg-[#074799] text-white' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => setSelectedState(state)}
            >
              {state}
            </button>
          ))}
        </div>
      </div>
      
      <div className="mt-4">
        {selectedState && (
          <div className="bg-[#E1FFBB] p-4 rounded-lg">
            <p className="font-bold text-[#074799]">Available Territories in {selectedState}:</p>
            <ul className="mt-2 space-y-2">
              <li className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-green-500"></span>
                Central {selectedState} - 3 slots available
              </li>
              <li className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-yellow-500"></span>
                North {selectedState} - 1 slot available
              </li>
              <li className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-red-500"></span>
                South {selectedState} - Waitlist only
              </li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapComponent;