'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Check } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface FormData {
  // Personal Information
  name: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  country: string;

  // Professional Details
  experience: string;
  qualification: string;
  currentSchool: string;
  message: string;
}

const TeacherTrainingForm = () => {
  const router = useRouter();
  const { register, handleSubmit, watch, formState: { errors } } = useForm<FormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/teachers-training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      // Determine country for thank you page
      const country = data.country.toLowerCase().includes('india') ? 'india' : 'usa';

      // Redirect to thank you page with country parameter
      router.push(`/thank-you?country=${country}`);
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success state removed - now redirecting to thank-you page

  return (
    <div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl md:text-3xl font-bold text-[#001A6E] mb-3">Registration Form</h2>
        <p className="text-gray-600">Fill in your details to join our teacher training program</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Personal Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-[#009990] border-b pb-2">Personal Information</h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
              <input
                type="text"
                {...register('name', { required: 'Full name is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your full name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
              <input
                type="email"
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address'
                  }
                })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <input
                type="tel"
                {...register('phone', { required: 'Phone number is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your phone number"
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">City *</label>
              <input
                type="text"
                {...register('city', { required: 'City is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your city"
              />
              {errors.city && (
                <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">State *</label>
              <input
                type="text"
                {...register('state', { required: 'State is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your state"
              />
              {errors.state && (
                <p className="mt-1 text-sm text-red-600">{errors.state.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Country *</label>
              <input
                type="text"
                {...register('country', { required: 'Country is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Enter your country"
              />
              {errors.country && (
                <p className="mt-1 text-sm text-red-600">{errors.country.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Professional Details */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-[#009990] border-b pb-2">Professional Details</h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Teaching Experience *</label>
              <input
                type="text"
                {...register('experience', { required: 'Teaching experience is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Years of teaching experience"
              />
              {errors.experience && (
                <p className="mt-1 text-sm text-red-600">{errors.experience.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Qualification *</label>
              <input
                type="text"
                {...register('qualification', { required: 'Qualification is required' })}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Your highest qualification"
              />
              {errors.qualification && (
                <p className="mt-1 text-sm text-red-600">{errors.qualification.message}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Current School (Optional)</label>
              <input
                type="text"
                {...register('currentSchool')}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Name of your current school"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Additional Message (Optional)</label>
              <textarea
                {...register('message')}
                rows={4}
                className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#009990] focus:border-transparent"
                placeholder="Any additional information you'd like to share"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`
              px-8 py-3 bg-[#001A6E] text-white rounded-lg
              hover:bg-[#001A6E]/90 transition-colors
              ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Registration'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TeacherTrainingForm;
