import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { MapPin, Phone } from 'lucide-react';

const USLocations = () => {
    const router = useRouter();
    const locations = [
        {
            name: 'Sunnyvale - Willow Ave',
            address: '1159, Willow Ave, Sunnyvale, CA 94086',
            phone: '(*************',
            image: '/images/locations/willow-ave.jpg'
        },
        {
            name: 'Sunnyvale - Lawrence Station Road',
            address: '1279 Lawrence Station Rd, Sunnyvale, CA 94089',
            phone: '(*************',
            image: '/images/locations/lawrence-station.jpg'
        }
    ];

    const handleShowMore = () => {
        router.push('/locations');
    };

    return (
        <div className="bg-white py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 className="text-4xl font-bold text-center text-gray-900 mb-12">Our USA Locations</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    {locations.map((location, index) => (
                        <div 
                            key={index}
                            className="bg-white rounded-lg overflow-hidden shadow-lg"
                        >
                            <div className="relative h-64 w-full">
                                <Image
                                    src={location.image}
                                    alt={location.name}
                                    fill
                                    style={{ objectFit: 'cover' }}
                                />
                            </div>
                            <div className="p-6">
                                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                                    {location.name}
                                </h3>
                                <div className="flex items-center mb-3">
                                    <MapPin className="h-5 w-5 text-gray-600 mr-2" />
                                    <p className="text-gray-600">{location.address}</p>
                                </div>
                                <div className="flex items-center mb-4">
                                    <Phone className="h-5 w-5 text-gray-600 mr-2" />
                                    <p className="text-gray-600">{location.phone}</p>
                                </div>
                                <div className="mt-2">
                                    <button
                                        onClick={() => router.push('/locations')}
                                        className="text-teal-600 hover:text-teal-700 font-medium flex items-center"
                                    >
                                        Show More →
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="text-center mt-8">
                    <button
                        onClick={handleShowMore}
                        className="inline-flex items-center px-6 py-3 text-base font-medium text-teal-600 hover:text-teal-700"
                    >
                        View All Locations →
                    </button>
                </div>
            </div>
        </div>
    );
};

export default USLocations;
