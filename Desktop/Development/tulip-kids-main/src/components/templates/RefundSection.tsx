'use client';

import { useState } from 'react';
import { School2, GraduationCap, Sparkles } from 'lucide-react';

interface PolicySectionProps {
  title: string;
  items: string[];
  icon: React.ReactNode;
  color: string;
}

function PolicySection({ title, items, icon, color }: PolicySectionProps) {
  return (
    <div className={`bg-white rounded-2xl p-8 shadow-sm border-l-4 ${color}`}>
      <div className="flex items-center gap-4 mb-6">
        <div className={`p-3 rounded-xl ${color.replace('border-', 'bg-')}/10`}>
          {icon}
        </div>
        <h3 className="text-xl font-semibold">{title}</h3>
      </div>
      <ul className="space-y-4">
        {items.map((item, index) => (
          <li key={index} className="flex items-start gap-3">
            <span className={`w-1.5 h-1.5 rounded-full mt-2 ${color.replace('border-', 'bg-')}`}></span>
            <span className="text-gray-600 leading-relaxed">{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default function RefundSection() {
  const policies = [
    {
      title: "Preschool Program",
      icon: <School2 className="w-6 h-6 text-blue-600" />,
      color: "border-blue-600",
      items: [
        "The registration fee is non-refundable.",
        "One month notice is required for discontinuing your child at Tulip childcare/preschool program"
      ]
    },
    {
      title: "After School Program",
      icon: <GraduationCap className="w-6 h-6 text-purple-600" />,
      color: "border-purple-600",
      items: [
        "The registration fee is non-refundable.",
        "One months' notice is required for discontinuing your child at Tulip after school program"
      ]
    },
    {
      title: "Summer Camps & Enrichment",
      icon: <Sparkles className="w-6 h-6 text-teal-600" />,
      color: "border-teal-600",
      items: [
        "100% refund if cancelled 3 weeks before the camp /class starts",
        "50% refund if cancelled 2 weeks before the camp/class starts",
        "No refund in other cases like absence / no-show.",
        "Parents will be notified if a camp/class will be cancelled due to lack of minimum enrollment needed and a choice will be given to move the child to other camp of choice (if seats are available in that choice class) or refund the camp fee."
      ]
    }
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            {policies.map((policy, index) => (
              <PolicySection 
                key={index} 
                title={policy.title} 
                items={policy.items} 
                icon={policy.icon}
                color={policy.color}
              />
            ))}
          </div>
          
          {/* Additional Notes */}
          <div className="mt-12 p-6 bg-gray-50 rounded-xl">
            <h4 className="text-lg font-semibold mb-4 text-gray-900">Important Notes</h4>
            <ul className="space-y-3">
              <li className="flex items-start gap-3">
                <span className="w-1.5 h-1.5 rounded-full mt-2 bg-gray-400"></span>
                <span className="text-gray-600">All refund requests must be submitted in writing or via email.</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="w-1.5 h-1.5 rounded-full mt-2 bg-gray-400"></span>
                <span className="text-gray-600">Processing time for refunds may take up to 5-7 business days.</span>
              </li>
              <li className="flex items-start gap-3">
                <span className="w-1.5 h-1.5 rounded-full mt-2 bg-gray-400"></span>
                <span className="text-gray-600">For any questions about refunds or cancellations, please contact our administrative office.</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
