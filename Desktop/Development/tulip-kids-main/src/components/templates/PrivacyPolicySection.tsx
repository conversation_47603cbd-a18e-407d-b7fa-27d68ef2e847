'use client';

import { Shield, Users, Database, Mail, Lock, Eye } from 'lucide-react';

interface PolicySectionProps {
  title: string;
  content: string[];
  icon: React.ReactNode;
}

function PolicySection({ title, content, icon }: PolicySectionProps) {
  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="flex items-center gap-4 mb-6">
        <div className="p-3 rounded-xl bg-[#009990]/10">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      <div className="space-y-4">
        {content.map((paragraph, index) => (
          <p key={index} className="text-gray-600 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
}

export default function PrivacyPolicySection() {
  const policies = [
    {
      title: "Information We Collect",
      icon: <Database className="w-6 h-6 text-[#009990]" />,
      content: [
        "We collect information that you provide directly to us, including names, contact information, and emergency contacts when you enroll in our programs.",
        "We may also collect information about your child's health, allergies, and developmental needs to ensure proper care.",
        "For safety and security purposes, we maintain records of individuals authorized to pick up your child."
      ]
    },
    {
      title: "How We Use Your Information",
      icon: <Users className="w-6 h-6 text-[#009990]" />,
      content: [
        "Your information is used to provide and improve our childcare services, ensure the safety and well-being of children in our care.",
        "We use contact information to communicate about your child's activities, schedule changes, and emergency situations.",
        "Health and allergy information is shared with relevant staff members to ensure proper care and safety."
      ]
    },
    {
      title: "Information Sharing",
      icon: <Mail className="w-6 h-6 text-[#009990]" />,
      content: [
        "We do not sell, trade, or otherwise transfer your personal information to outside parties.",
        "Information may be shared with regulatory authorities as required by law.",
        "We may share information with service providers who assist in our operations, subject to confidentiality agreements."
      ]
    },
    {
      title: "Data Security",
      icon: <Lock className="w-6 h-6 text-[#009990]" />,
      content: [
        "We implement appropriate security measures to protect your personal information.",
        "Access to sensitive information is restricted to authorized personnel only.",
        "Regular security assessments are conducted to ensure data protection."
      ]
    },
    {
      title: "Your Privacy Rights",
      icon: <Shield className="w-6 h-6 text-[#009990]" />,
      content: [
        "You have the right to access, correct, or delete your personal information.",
        "You may opt-out of non-essential communications.",
        "Contact our privacy officer for any privacy-related concerns or requests."
      ]
    },
    {
      title: "Cookie Policy",
      icon: <Eye className="w-6 h-6 text-[#009990]" />,
      content: [
        "Our website uses cookies to enhance your browsing experience.",
        "You can control cookie settings through your browser preferences.",
        "Essential cookies are required for basic website functionality."
      ]
    }
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            {policies.map((policy, index) => (
              <PolicySection 
                key={index}
                title={policy.title}
                content={policy.content}
                icon={policy.icon}
              />
            ))}
          </div>

          {/* Contact Information */}
          <div className="mt-12 p-6 bg-gray-50 rounded-xl">
            <h4 className="text-lg font-semibold mb-4 text-gray-900">Contact Us</h4>
            <p className="text-gray-600 mb-4">
              If you have any questions about our Privacy Policy, please contact us:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
              <li>Address: 1159, Willow Ave

Sunnyvale</li>
            </ul>
            <p className="mt-4 text-sm text-gray-500">
              Last updated: January 2025
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
