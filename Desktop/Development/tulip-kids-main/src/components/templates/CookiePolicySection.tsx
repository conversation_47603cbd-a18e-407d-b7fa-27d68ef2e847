'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ToggleLeft } from 'lucide-react';

interface PolicySectionProps {
  title: string;
  content: string[];
  icon: React.ReactNode;
}

function PolicySection({ title, content, icon }: PolicySectionProps) {
  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="flex items-center gap-4 mb-6">
        <div className="p-3 rounded-xl bg-[#009990]/10">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      <div className="space-y-4">
        {content.map((paragraph, index) => (
          <p key={index} className="text-gray-600 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
}

export default function CookiePolicySection() {
  const policies = [
    {
      title: "What Are Cookies",
      icon: <Cookie className="w-6 h-6 text-[#009990]" />,
      content: [
        "Cookies are small text files that are placed on your device when you visit our website.",
        "They help us provide you with a better website experience by enabling us to monitor which pages you find useful and which you do not.",
        "A cookie does not give us access to your computer or any information about you, other than the data you choose to share with us."
      ]
    },
    {
      title: "How We Use Cookies",
      icon: <Settings className="w-6 h-6 text-[#009990]" />,
      content: [
        "Essential Cookies: These are necessary for the website to function properly. They enable core functionality such as security, network management, and accessibility.",
        "Analytics Cookies: Help us understand how visitors interact with our website by collecting and reporting information anonymously.",
        "Functionality Cookies: These cookies enable the website to provide enhanced functionality and personalization, such as remembering your preferences."
      ]
    },
    {
      title: "Cookie Duration",
      icon: <Clock className="w-6 h-6 text-[#009990]" />,
      content: [
        "Session Cookies: These cookies are temporary and expire once you close your browser.",
        "Persistent Cookies: These cookies remain on your device for a set period or until you delete them.",
        "We regularly review our cookie expiration periods to ensure they don't stay active longer than necessary."
      ]
    },
    {
      title: "Your Cookie Choices",
      icon: <ToggleLeft className="w-6 h-6 text-[#009990]" />,
      content: [
        "You can choose to accept or decline cookies. Most web browsers automatically accept cookies, but you can modify your browser settings to decline cookies if you prefer.",
        "You can delete existing cookies through your browser settings.",
        "Note that declining cookies may prevent you from taking full advantage of our website."
      ]
    },
    {
      title: "Third-Party Cookies",
      icon: <Bell className="w-6 h-6 text-[#009990]" />,
      content: [
        "We may use third-party services that also place cookies on your device.",
        "These services include analytics tools and various plugins that help enhance your browsing experience.",
        "We have no control over these third-party cookies. Please refer to the respective privacy policies of these services."
      ]
    },
    {
      title: "Cookie Security",
      icon: <Shield className="w-6 h-6 text-[#009990]" />,
      content: [
        "We implement appropriate security measures to protect the cookies we use.",
        "Our cookies do not store personally identifiable information without your explicit consent.",
        "We regularly review our cookie policy and practices to ensure compliance with data protection regulations."
      ]
    }
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8">
            {policies.map((policy, index) => (
              <PolicySection 
                key={index}
                title={policy.title}
                content={policy.content}
                icon={policy.icon}
              />
            ))}
          </div>

          {/* Cookie Settings Information */}
          <div className="mt-12 p-6 bg-gray-50 rounded-xl">
            <h4 className="text-lg font-semibold mb-4 text-gray-900">Managing Your Cookie Preferences</h4>
            <p className="text-gray-600 mb-4">
              To find out more about cookies, including how to see what cookies have been set and how to manage and delete them, visit:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• www.aboutcookies.org</li>
              <li>• www.allaboutcookies.org</li>
            </ul>
            <p className="mt-6 text-gray-600">
              For specific instructions on managing cookies in your browser, click the relevant link below:
            </p>
            <ul className="mt-2 space-y-2">
              <li>
                <a href="https://support.google.com/chrome/answer/95647" target="_blank" rel="noopener noreferrer" className="text-[#009990] hover:underline">
                  Google Chrome
                </a>
              </li>
              <li>
                <a href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop" target="_blank" rel="noopener noreferrer" className="text-[#009990] hover:underline">
                  Mozilla Firefox
                </a>
              </li>
              <li>
                <a href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471" target="_blank" rel="noopener noreferrer" className="text-[#009990] hover:underline">
                  Safari
                </a>
              </li>
              <li>
                <a href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09" target="_blank" rel="noopener noreferrer" className="text-[#009990] hover:underline">
                  Microsoft Edge
                </a>
              </li>
            </ul>
            <p className="mt-6 text-sm text-gray-500">
              Last updated: January 2025
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
