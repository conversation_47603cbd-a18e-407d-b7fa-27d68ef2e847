'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Bus,
  ArrowRight,
  GraduationCap,
  Users,
  Building2,
  ShieldCheck,
  Puzzle,
  Brain,
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Minus,
  Plus,
  Phone,
  Mail,
  MapPin,
  Award,
  Baby,
  School,
  LucideIcon
} from 'lucide-react';
import FAQSection from './FAQSection';
import ExcellenceSection from '../sections/ExcellenceSection';
import DirectorBio from '../sections/DirectorBio';
import PreSchoolProgram from '../sections/PreSchoolProgram';
import ProgramsSection from '../sections/ProgramsSection';
import EnrollmentCTA from '../shared/EnrollmentCTA';
import LocationEnrollCTA from '../shared/LocationEnrollCTA';
import LocationPageTracker from '../analytics/LocationPageTracker';

interface LocationData {
  name: string;
  badge: string;
  heading: string;
  description: string;
  heroImage: string;
  heroImageAlt: string;
  slug?: string;
  features: Array<{
    title: string;
    description: string;
    iconName?: string;
  }>;
  facilities: Array<{
    name: string;
    description: string;
    image?: string;
    iconName: string;
  }>;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  accessibility: {
    features: string[];
    additionalInfo: string;
  };
  safety: Array<{
    title: string;
    description: string;
  }>;
  community: Array<{
    title: string;
    description: string;
    image?: string;
  }>;
  faq: Array<{
    question: string;
    answer: string;
  }>;
  director?: {
    name: string;
    role: string;
    image: string;
    bio: string[];
  };
  country: 'usa' | 'india';
  ExcellenceSection?: React.ComponentType;
}

interface PreschoolTemplateProps {
  locationData: LocationData;
}

const iconMap: { [key: string]: LucideIcon } = {
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Bus,
  GraduationCap,
  Baby,
  School
};

const getDirectionsUrl = (address: { street: string; city: string; state: string }, name: string) => {
  if (!name) return '';

  if (name.toLowerCase().includes('bima')) {
    return 'https://maps.app.goo.gl/sMoAmrHxyLeKYfwH9';
  }
  const formattedAddress = encodeURIComponent(`${address.street}, ${address.city}, ${address.state}`);
  return `https://www.google.com/maps/dir/?api=1&destination=${formattedAddress}`;
};

const getIcon = (iconName?: string) => {
  if (!iconName || !iconMap[iconName]) {
    return School;
  }
  return iconMap[iconName];
};

export default function PreschoolTemplate({ locationData }: PreschoolTemplateProps) {
  if (!locationData) return null;

  // Redirect to IndiaLocationTemplate if country is india
  if (locationData.country === 'india') {
    throw new Error('PreschoolTemplate should only be used for USA locations. Use IndiaLocationTemplate for Indian locations.');
  }

  const {
    name,
    badge,
    heading,
    description,
    heroImage,
    heroImageAlt,
    facilities,
    address,
    accessibility,
    safety,
    community,
    faq,
    director,
    slug
  } = locationData;

  const locationSlug = slug || (name ? name.toLowerCase().replace(/\s+/g, '-') : '');

  return (
    <main className="other-pages-container min-h-screen">
      {/* Google Ads Location Tracking */}
      <LocationPageTracker locationId={locationSlug} locationName={name} />

      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src={heroImage}
            alt={heroImageAlt}
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations" className="hover:text-white transition-colors">
                    Locations
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations/usa" className="hover:text-white transition-colors">
                    USA
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">{name}</span>
                </li>
              </ol>
            </nav>

            <div className="max-w-3xl">
              <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                {badge}
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{heading}</h1>
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
                {description}
              </p>
              <div className="mt-8">
                <Link
                  href={`/contact?country=usa&location=${locationSlug}&program=preschool`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="bg-white">
        <ProgramsSection />
      </section>

      {/* Enrollment CTA */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-8">
          <LocationEnrollCTA
            locationSlug={locationSlug}
            programType="preschool"
            className="max-w-5xl mx-auto"
          />
        </div>
      </section>

      {/* Excellence Section */}
      {locationData.ExcellenceSection ? <locationData.ExcellenceSection /> : <ExcellenceSection />}

      {/* Director Bio Section */}
      {director && (
        <section className="py-24 bg-gradient-to-b from-white via-gray-50/50 to-white">
          <div className="container mx-auto px-4">
            <DirectorBio location={locationSlug} director={director} />
          </div>
        </section>
      )}

      {/* Facilities Section */}


      {/* Contact Section */}
      <section className="py-20 bg-gray-50" id="contact">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Contact Us</h2>
              <p className="text-gray-600 text-lg">
                Get in touch with us to learn more about our programs and enrollment opportunities.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Location Information */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Visit Our Location</h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <MapPin className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Address</h4>
                      <p className="text-gray-600">{address.street}</p>
                      <p className="text-gray-600">{address.city}, {address.state} {address.zip}</p>
                      <a
                        href={getDirectionsUrl(address, name)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-[#009990] hover:text-[#008880] mt-2 font-medium"
                      >
                        Get Directions
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <Phone className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                      <a
                        href={`tel:${address.phone}`}
                        className="text-gray-600 hover:text-[#009990]"
                      >
                        {address.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <Mail className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                      <a
                        href={`mailto:${address.email}`}
                        className="text-gray-600 hover:text-[#009990]"
                      >
                        {address.email}
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule a Tour */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Schedule a Tour</h3>
                <p className="text-gray-600 mb-6">
                  Visit our facility to experience our nurturing environment firsthand. Our team will be happy to show you around and answer any questions you may have.
                </p>
                <Link
                  href={`/contact?country=usa&location=${locationSlug}&program=preschool`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection faq={faq} />
    </main>
  );
}
