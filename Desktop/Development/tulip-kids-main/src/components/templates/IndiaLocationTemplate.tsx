'use client';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Bus,
  ArrowRight,
  GraduationCap,
  Users,
  Building2,
  Shield<PERSON>heck,
  Puzzle,
  Brain,
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Minus,
  Plus,
  Phone,
  Mail,
  MapPin,
  Award,
  Baby,
  School,
  LucideIcon
} from 'lucide-react';
import FAQSection from './FAQSection';
import ExcellenceSection from '../sections/ExcellenceSection';
import DirectorBio from '../sections/DirectorBio';
import IndiaExcellenceSection from '../sections/IndiaExcellenceSection';
import ProgramsSection from '../shared/ProgramsSection';
import LocationEnrollCTA from '../shared/LocationEnrollCTA';
import LocationPageTracker from '../analytics/LocationPageTracker';
import IndiaContactForm from '../shared/IndiaContactForm';

interface LocationData {
  name: string;
  badge: string;
  heading: string;
  description: string;
  heroImage: string;
  heroImageAlt: string;
  features: Array<{
    title: string;
    description: string;
    iconName?: string;
  }>;
  facilities: Array<{
    name: string;
    description: string;
    image?: string;
    iconName: string;
  }>;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    email: string;
  };
  accessibility: {
    features: string[];
    additionalInfo: string;
  };
  safety: Array<{
    title: string;
    description: string;
  }>;
  community: Array<{
    title: string;
    description: string;
    image?: string;
  }>;
  faq: Array<{
    question: string;
    answer: string;
  }>;
  director?: {
    name: string;
    role: string;
    image: string;
    bio: string[];
  };
  country: 'usa' | 'india';
  ExcellenceSection?: React.ComponentType;
}

interface IndiaLocationTemplateProps {
  locationData: LocationData;
}

const iconMap: { [key: string]: LucideIcon } = {
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Bus,
  GraduationCap,
  Baby,
  School
};

const getDirectionsUrl = (address: { street?: string; city?: string; state?: string } | undefined, name: string | undefined) => {
  if (!name) return '';

  if (name.toLowerCase().includes('bima')) {
    return 'https://maps.app.goo.gl/sMoAmrHxyLeKYfwH9';
  }

  if (name.toLowerCase().includes('nipania')) {
    return 'https://maps.app.goo.gl/Rae3WeBFha2S5rrd7';
  }

  if (!address) return '';

  const formattedAddress = encodeURIComponent(`${address.street || ''}, ${address.city || ''}, ${address.state || ''}`);
  return `https://www.google.com/maps/dir/?api=1&destination=${formattedAddress}`;
};

const getIcon = (iconName?: string) => {
  if (!iconName || !iconMap[iconName]) {
    return School;
  }
  return iconMap[iconName];
};

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex justify-between items-center w-full py-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg font-semibold">{question}</span>
        {isOpen ? (
          <Minus className="w-5 h-5 text-[#009990]" />
        ) : (
          <Plus className="w-5 h-5 text-[#009990]" />
        )}
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function IndiaLocationTemplate({ locationData }: IndiaLocationTemplateProps) {
  if (!locationData) return null;

  // Redirect to PreSchoolTemplate if country is usa
  if (locationData.country !== 'india') {
    throw new Error('IndiaLocationTemplate should only be used for Indian locations. Use PreSchoolTemplate for USA locations.');
  }

  console.log('Location Data:', locationData);
  console.log('Address Data:', locationData.address);

  const {
    name,
    badge,
    heading,
    description,
    heroImage,
    heroImageAlt,
    facilities,
    address,
    accessibility,
    safety,
    community,
    faq,
    director
  } = locationData;

  console.log('Destructured Address:', address);

  const locationSlug = name ? name.toLowerCase().replace(/\s+/g, '-') : '';

  return (
    <main className="page-container">
      {/* Google Ads Location Tracking */}
      <LocationPageTracker locationId={locationSlug} locationName={name || ''} />

      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src={heroImage}
            alt={heroImageAlt}
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations" className="hover:text-white transition-colors">
                    Locations
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations/india" className="hover:text-white transition-colors">
                    India
                  </Link>
                </li>
              </ol>
            </nav>

            <div className="max-w-3xl">
              <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                {badge}
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{heading}</h1>
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
                {description}
              </p>

              <div className="mt-8">
                <Link
                  href={`/contact?country=india&location=${locationSlug}&source=${locationSlug}_hero&medium=website&campaign=india_locations`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* India Excellence Section */}
      <IndiaExcellenceSection />

      {/* Programs Section - For Bima Nagar and Nipania */}
      {name && (name.toLowerCase().includes('bima') || name.toLowerCase().includes('nipania')) && (
        <ProgramsSection />
      )}

      {/* Facilities Section */}
      <section className="py-24 bg-white" id="facilities">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Our Facilities</h2>
            <p className="text-gray-600 text-lg">
              State-of-the-art facilities designed for your child's comfort and safety
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {facilities && facilities.length > 0 ? facilities.map((facility, index) => {
              const Icon = getIcon(facility.iconName);
              return (
                <div key={index} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Icon className="w-8 h-8 text-[#009990]" />
                  </div>
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-4 text-center">{facility.name}</h3>
                  <p className="text-gray-600 text-center">{facility.description}</p>
                </div>
              );
            }) : (
              <div className="col-span-3 text-center py-8">
                <p className="text-gray-600">Facility information coming soon.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4">
        <LocationEnrollCTA
          locationSlug={locationSlug}
          programType="preschool"
          name={name || ''}
          address={address || {
            street: '',
            city: '',
            state: '',
            zip: '',
            phone: '',
            email: ''
          }}
        />
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-50" id="contact">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Contact Us</h2>
              <p className="text-gray-600 text-lg">
                Get in touch with us to learn more about our programs and enrollment opportunities at {name}.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 items-start">
              {/* Contact Form */}
              <div className="order-2 md:order-1">
                <IndiaContactForm
                  locationId={locationSlug}
                  locationName={name || ''}
                  className="h-full"
                />
              </div>

              {/* Location Information */}
              <div className="order-1 md:order-2">
                <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Visit Our Location</h3>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <MapPin className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Address</h4>
                        <p className="text-gray-600">{address?.street}</p>
                        <p className="text-gray-600">{address?.city}, {address?.state} {address?.zip}</p>
                        <a
                          href={getDirectionsUrl(address || { street: '', city: '', state: '' }, name || '')}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-[#009990] hover:text-[#008880] mt-2 font-medium"
                        >
                          Get Directions
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Contact Information</h3>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <Phone className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                        <a
                          href={`tel:${address?.phone}`}
                          className="text-gray-600 hover:text-[#009990] transition-colors"
                        >
                          {address?.phone}
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <Mail className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                        <a
                          href={`mailto:${address?.email}`}
                          className="text-gray-600 hover:text-[#009990] transition-colors"
                        >
                          {address?.email}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Operating Hours */}
                <div className="bg-[#001A6E] rounded-2xl p-8 text-white">
                  <h3 className="text-2xl font-bold mb-6">Operating Hours</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Monday - Saturday</span>
                      <span>09:00 AM - 03:30 PM</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Sunday</span>
                      <span>Closed</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      {faq && faq.length > 0 ? (
        <FAQSection faq={faq} />
      ) : null}
    </main>
  );
}
