'use client';

import React from 'react';
import { 
  <PERSON><PERSON><PERSON>,
  CheckCircle2, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Book<PERSON>pen,
  <PERSON>lette,
  Music,
  HeartHandshake,
  School,
  GraduationCap,
  CalendarDays,
  Apple,
  Sparkles,
  BookOpenCheck,
  Lightbulb,
  Blocks,
  Users2,
  Pencil,
  Globe2
} from 'lucide-react';
import type { Program } from '@/data/programs/preschool';
import Link from 'next/link';
import Image from 'next/image';
import FAQAccordion from '@/components/sections/FAQAccordion';
import { usaPreschoolFAQs } from '@/data/faq/usa-preschool';

interface ProgramTemplateProps {
  program: Program;
}

type ProgramInfo = {
  icon: React.ComponentType<any>;
  title: string;
  ageRange: string;
  description: string;
  color: string;
  features: Array<{
    icon: React.ComponentType<any>;
    title: string;
    description: string;
  }>;
};

const PROGRAMS: ProgramInfo[] = [
  {
    icon: BabyIcon,
    title: "Infant/Toddler Care",
    ageRange: "6 months - 24 months",
    description: "Nurturing care and early development in a warm, secure environment. We focus on sensory experiences, motor skills, and emotional bonds.",
    color: "from-[#4F46E5] to-[#818CF8]",
    features: [
      {
        icon: Heart,
        title: "Nurturing Care",
        description: "Individual attention and emotional support"
      },
      {
        icon: Shapes,
        title: "Sensory Play",
        description: "Age-appropriate sensory experiences"
      },
      {
        icon: HeartHandshake,
        title: "Social Bonding",
        description: "Building trust and secure attachments"
      }
    ]
  },
  {
    icon: Puzzle,
    title: "Jr. Preschool",
    ageRange: "2 - 3 years",
    description: "Building independence and social skills through play-based learning and structured activities.",
    color: "from-[#059669] to-[#34D399]",
    features: [
      {
        icon: Palette,
        title: "Creative Expression",
        description: "Art, music, and movement activities"
      },
      {
        icon: Brain,
        title: "Cognitive Growth",
        description: "Problem-solving and critical thinking"
      },
      {
        icon: HeartHandshake,
        title: "Social Skills",
        description: "Peer interaction and sharing"
      }
    ]
  },
  {
    icon: School,
    title: "Preschool",
    ageRange: "3 - 4 years",
    description: "Comprehensive early education focusing on cognitive development, creativity, and social skills.",
    color: "from-[#DC2626] to-[#FB7185]",
    features: [
      {
        icon: BookOpen,
        title: "Early Literacy",
        description: "Reading readiness and language skills"
      },
      {
        icon: Music,
        title: "Creative Arts",
        description: "Music, dance, and dramatic play"
      },
      {
        icon: Sparkles,
        title: "Discovery Learning",
        description: "Hands-on exploration and experiments"
      }
    ]
  },
  {
    icon: GraduationCap,
    title: "Pre-Kindergarten",
    ageRange: "4 - 5 years",
    description: "Advanced preparation for kindergarten with focus on literacy, mathematics, and critical thinking.",
    color: "from-[#D97706] to-[#FBBF24]",
    features: [
      {
        icon: Brain,
        title: "Academic Skills",
        description: "Math, science, and literacy foundations"
      },
      {
        icon: Puzzle,
        title: "Problem Solving",
        description: "Critical thinking and reasoning"
      },
      {
        icon: HeartHandshake,
        title: "Social Development",
        description: "Leadership and collaboration skills"
      }
    ]
  }
];

export default function ProgramTemplate({ program }: ProgramTemplateProps) {
  return (
    <main className="page-container">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/programs/preschool/pre-school.jpg"
            alt="Program Background"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E] via-[#001A6E]/100 to-[#001A6E]/45"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li><Link href="/" className="hover:text-white transition-colors">Home</Link></li>
                <li><span className="mx-2">/</span></li>
                <li><Link href="/programs" className="hover:text-white transition-colors">Programs</Link></li>
              </ol>
            </nav>

            <div className="mb-8">
              {program.ageRange && (
                <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                  Ages {program.ageRange}
                </span>
              )}
              <h1 className="text-4xl md:text-5xl font-bold text-white">{program.title}</h1>
            </div>

            <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
              {program.description}
            </p>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-4">
                Our Educational Programs
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Comprehensive early childhood education programs designed to nurture your child's development at every stage.
              </p>
            </div>

            {/* Program Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {PROGRAMS.map((prog, index) => {
                const Icon = prog.icon;
                return (
                  <div key={index} className="group relative bg-white rounded-3xl shadow-sm overflow-hidden hover:shadow-xl transition-all duration-300">
                    {/* Icon Header */}
                    <div className={`h-32 bg-gradient-to-r ${prog.color} flex items-center justify-center relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <Icon className="w-16 h-16 text-white transform group-hover:scale-110 group-hover:rotate-6 transition-transform duration-300" />
                    </div>
                    
                    {/* Content */}
                    <div className="p-8">
                      <div className="mb-4">
                        <span className="inline-block bg-[#001A6E]/10 text-[#001A6E] px-4 py-1 rounded-full text-sm font-medium">
                          Ages {prog.ageRange}
                        </span>
                      </div>
                      <h3 className="text-2xl font-bold text-[#001A6E] mb-3">{prog.title}</h3>
                      <p className="text-gray-600 mb-6">{prog.description}</p>
                      
                      {/* Features */}
                      <div className="grid grid-cols-1 gap-4 mb-6">
                        {prog.features.map((feature, idx) => {
                          const FeatureIcon = feature.icon;
                          return (
                            <div key={idx} className="flex items-start gap-3">
                              <div className="w-8 h-8 rounded-lg bg-[#001A6E]/5 flex items-center justify-center">
                                <FeatureIcon className="w-5 h-5 text-[#001A6E]" />
                              </div>
                              <div>
                                <h4 className="font-medium text-[#001A6E]">{feature.title}</h4>
                                <p className="text-sm text-gray-600">{feature.description}</p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      
                      
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Schedule Cards */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-3xl p-8 shadow-sm hover:shadow-xl transition-all duration-300">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-2xl bg-[#E8F5E9] flex items-center justify-center">
                    <Clock className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#001A6E]">Full Time Program</h3>
                    <p className="text-gray-600">8:00 am - 6:00 pm</p>
                  </div>
                </div>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-gray-600">
                    <Apple className="w-5 h-5 text-[#009990]" />
                    <span>Midmorning Snack, Lunch and Evening Snack
                    </span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <Sparkles className="w-5 h-5 text-[#009990]" />
                    <span>Full Curriculum Activities</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white rounded-3xl p-8 shadow-sm hover:shadow-xl transition-all duration-300">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-2xl bg-[#E8F5E9] flex items-center justify-center">
                    <Clock className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-[#001A6E]">Core Day Program</h3>
                    <p className="text-gray-600">8:00 am - 3:00 pm</p>
                  </div>
                </div>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-gray-600">
                    <Apple className="w-5 h-5 text-[#009990]" />
                    <span>Midmorning Snack, Lunch and Evening Snack</span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <Sparkles className="w-5 h-5 text-[#009990]" />
                    <span>Core Curriculum Activities</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* FAQ Section */}
            <FAQAccordion faqs={usaPreschoolFAQs} />

            {/* Learning Outcomes */}
            <section className="py-20 bg-white">
              <div className="container mx-auto px-4">
                <div className="max-w-7xl mx-auto">
                  <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-4">
                      Learning Outcomes
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Our comprehensive curriculum is designed to help your child achieve key developmental milestones 
                      and build a strong foundation for lifelong learning.
                    </p>
                  </div>

                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {/* Left Column - Overview */}
                    <div className="space-y-8">
                      <div className="bg-[#F8FAFC] border border-[#E2E8F0] rounded-3xl p-8">
                        <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Program Overview</h3>
                        <div className="space-y-6">
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 rounded-xl bg-[#001A6E] flex items-center justify-center flex-shrink-0">
                              <Star className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-[#001A6E] mb-2">Personalized Learning Path</h4>
                              <p className="text-gray-600">
                                Each child receives individualized attention and support to ensure they progress at their own pace while meeting developmental milestones.
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 rounded-xl bg-[#001A6E] flex items-center justify-center flex-shrink-0">
                              <Brain className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-[#001A6E] mb-2">Holistic Development</h4>
                              <p className="text-gray-600">
                                Our program focuses on developing the whole child - intellectually, socially, emotionally, and physically.
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 rounded-xl bg-[#001A6E] flex items-center justify-center flex-shrink-0">
                              <Users2 className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-[#001A6E] mb-2">Collaborative Learning</h4>
                              <p className="text-gray-600">
                                Children learn through interaction with peers and teachers in a nurturing, supportive environment.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right Column - Specific Outcomes */}
                    <div className="bg-[#F8FAFC] border border-[#E2E8F0] rounded-3xl p-8">
                      <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Key Learning Areas</h3>
                      <div className="grid gap-6">
                        {program.learningOutcomes.map((outcome, index) => (
                          <div 
                            key={index}
                            className="flex items-start gap-4 p-4 bg-white rounded-xl border border-[#E2E8F0] hover:border-[#001A6E] transition-colors duration-300"
                          >
                            <div className="w-8 h-8 rounded-lg bg-[#001A6E]/10 flex items-center justify-center flex-shrink-0">
                              <CheckCircle2 className="w-5 h-5 text-[#001A6E]" />
                            </div>
                            <div>
                              <p className="text-gray-700">{outcome}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* CTA Section */}
                  <div className="my-20 relative">
                    <div className="bg-[#001A6E] rounded-3xl overflow-hidden">
                      <div className="relative z-10 px-8 py-16 md:px-16">
                        <div className="max-w-3xl mx-auto text-center">
                          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                            Ready to Begin Your Child's Journey?
                          </h3>
                          <p className="text-white/90 text-lg mb-8">
                            Secure your child's spot in our nurturing preschool program. 
                            Limited spaces available for the upcoming session.
                          </p>
                          <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link 
                              href="/locations/usa"
                              className="inline-flex items-center justify-center px-8 py-4 bg-white text-[#001A6E] rounded-xl font-semibold hover:bg-[#F8FAFC] transition-colors duration-300"
                            >
                              Find a Location
                              <ArrowRight className="w-5 h-5 ml-2" />
                            </Link>
                            <Link 
                              href="/contact"
                              className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white rounded-xl font-semibold hover:bg-white/10 transition-colors duration-300"
                            >
                              Schedule a Visit
                            </Link>
                          </div>
                        </div>
                      </div>
                      {/* Decorative Elements */}
                      <div className="absolute top-0 left-0 w-32 h-32 md:w-48 md:h-48 opacity-10">
                        <div className="absolute inset-0 bg-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
                      </div>
                      <div className="absolute bottom-0 right-0 w-40 h-40 md:w-64 md:h-64 opacity-10">
                        <div className="absolute inset-0 bg-white rounded-full transform translate-x-1/2 translate-y-1/2"></div>
                      </div>
                    </div>
                  </div>

                  {/* Bottom Section - Additional Information */}
                  <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div className="bg-[#F8FAFC] border border-[#E2E8F0] rounded-3xl p-6 text-center">
                      <div className="w-12 h-12 rounded-xl bg-[#001A6E] mx-auto flex items-center justify-center mb-4">
                        <BookOpenCheck className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-[#001A6E] mb-2">Progress Tracking</h4>
                      <p className="text-gray-600 text-sm">
                        Regular assessments and detailed progress reports to keep you informed of your child's development
                      </p>
                    </div>
                    <div className="bg-[#F8FAFC] border border-[#E2E8F0] rounded-3xl p-6 text-center">
                      <div className="w-12 h-12 rounded-xl bg-[#001A6E] mx-auto flex items-center justify-center mb-4">
                        <HeartHandshake className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-[#001A6E] mb-2">Parent Partnership</h4>
                      <p className="text-gray-600 text-sm">
                        Regular parent-teacher meetings and communication to support your child's learning journey
                      </p>
                    </div>
                    <div className="bg-[#F8FAFC] border border-[#E2E8F0] rounded-3xl p-6 text-center">
                      <div className="w-12 h-12 rounded-xl bg-[#001A6E] mx-auto flex items-center justify-center mb-4">
                        <GraduationCap className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-[#001A6E] mb-2">School Readiness</h4>
                      <p className="text-gray-600 text-sm">
                        Comprehensive preparation ensuring your child is ready for their next educational milestone
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </section>
    </main>
  );
}