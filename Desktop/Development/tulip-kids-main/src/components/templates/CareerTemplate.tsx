'use client';

import { Phone, Mail, MapPin, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const positions = [
  {
    title: "Preschool Teacher (Sunnyvale & San Jose)",
    location: "Sunnyvale & San Jose",
    type: "Full Time (9 AM – 6 PM M-F)",
    responsibilities: [
      "Instruct preschool-aged children in activities designed to promote intellectual and creative growth",
      "Create a fun and safe learning environment",
      "Develop schedules and routines to ensure adequate physical activity, rest, and playtime",
      "Establish and maintain positive relationships with students and parents",
      "Communicate with parents on students' growth and progress",
      "Maintain the health and safety of all students",
    ],
    qualifications: [
      "Previous experience in childcare, teaching, or other related fields",
      "Passionate about working with children",
      "Ability to build rapport with children",
      "Positive and patient demeanor",
      "Excellent written and verbal communication skills",
      "Required ECE units",
    ],
    benefits: ["Medical", "Dental", "Life Insurance"],
  },
  {
    title: "Infant Teacher",
    location: "Sunnyvale",
    type: "Full Time (9 AM – 6 PM M-F)",
    responsibilities: [
      "Plan both long and short range activities in accordance with curriculum objectives",
      "Meet the emotional, social, physical and cognitive needs of each child",
      "Prepare monthly plan charts",
      "Complete bi-annual assessment of children's development",
      "Report progress of children to parents",
      "Maintain daily open communication with parents",
      "Keep accident reports",
      "Maintain anecdotal records",
      "Maintain confidentiality",
      "Report any suspect abuse to supervisor",
      "Arrange a classroom environment in accordance to program goals",
      "Maintain a safe and healthy environment",
      "Inspect and replace damaged or lost materials",
      "Attend in-service and staff meetings",
      "Supervise assistants, aides and volunteers",
      "Keep all appropriate records",
      "Meet all applicable licensing regulations",
    ],
    qualifications: ["Infant ECE units required"],
    benefits: ["Excellent hourly rates with benefits"],
  },
  {
    title: "Infant/Toddler, Pre School Teachers",
    location: "Sunnyvale",
    type: "Full Time (8-5 or 9-6 M-F)",
    responsibilities: [
      "Plan both long and short range activities in accordance with curriculum objectives",
      "Meet the emotional, social, physical and cognitive needs of each child",
      "Prepare monthly plan charts",
      "Complete bi-annual assessment of children's development",
      "Report progress of children to parents",
      "Maintain daily open communication with parents",
      "Keep accident reports",
      "Maintain anecdotal records",
      "Maintain confidentiality",
      "Report any suspect abuse to supervisor",
      "Arrange a classroom environment in accordance to program goals",
      "Maintain a safe and healthy environment",
      "Inspect and replace damaged or lost materials",
      "Attend in-service and staff meetings",
      "Supervise assistants, aides and volunteers",
      "Keep all appropriate records",
      "Meet all applicable licensing regulations",
    ],
    qualifications: ["Necessary qualification and experience as required by licensing department", "For Infant Teachers: Infant ECE Units required"],
    benefits: ["Excellent remuneration with benefits"],
  },
];

export default function CareerTemplate() {
  return (
    <main className="page-container">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/career.webp"
            alt="Join our team at Tulip Kids Academy"
            fill
            className="object-cover opacity-30"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/98 via-[#001A6E]/95 to-[#001A6E]/90"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/about" className="hover:text-white transition-colors">
                    About
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">Careers</span>
                </li>
              </ol>
            </nav>
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl mb-4">
              Join Our Team
            </h1>
            <p className="mt-6 text-lg leading-8 text-white/90 max-w-3xl">
              Shape the future with Tulip Kids Academy. We're looking for passionate educators who want to make a difference in children's lives.
            </p>
          </div>
        </div>
      </section>

      {/* Job Listings Section */}
      <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
        <div className="mx-auto max-w-3xl">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Tulip is accepting applications for the following positions
          </h2>
          <div className="space-y-12">
            {positions.map((position, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md p-6 border border-gray-200"
              >
                <h3 className="text-xl font-semibold text-blue-600 mb-2">
                  {position.title}
                </h3>
                <div className="mb-4">
                  <p className="text-gray-600 flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    <span className="font-medium">Location:</span>{" "}
                    {position.location}
                  </p>
                  <p className="text-gray-600">
                    <span className="font-medium">Type:</span> {position.type}
                  </p>
                </div>

                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Responsibilities:
                  </h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {position.responsibilities.map((resp, idx) => (
                      <li key={idx} className="text-gray-600">
                        {resp}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Qualifications:
                  </h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {position.qualifications.map((qual, idx) => (
                      <li key={idx} className="text-gray-600">
                        {qual}
                      </li>
                    ))}
                  </ul>
                </div>

                {position.benefits && (
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      Benefits:
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {position.benefits.map((benefit, idx) => (
                        <li key={idx} className="text-gray-600">
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="mt-6 flex items-center gap-2">
                  <Mail className="h-5 w-5 text-gray-600" />
                  <p className="text-gray-600">
                    To apply, please send your resume to{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
