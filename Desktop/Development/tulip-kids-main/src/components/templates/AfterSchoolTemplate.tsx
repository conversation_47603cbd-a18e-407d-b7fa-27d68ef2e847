'use client';

import Image from 'next/image';
import Link from 'next/link';
import {
  Bus,
  ArrowRight,
  GraduationCap,
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Minus,
  Plus,
  Phone,
  Mail,
  MapPin,
  Award,
  Baby,
  School,
  LucideIcon,
  ShieldCheck,
  Building2,
  <PERSON>,
  Brain
} from 'lucide-react';
import FAQSection from './FAQSection';
import ExcellenceSection from '../sections/ExcellenceSection';
import DirectorBio from '../sections/DirectorBio';
import AfterSchoolSection from '../sections/AfterSchoolSection';
import AfterSchoolProgram from '../sections/AfterSchoolProgram';
import LocationEnrollCTA from '../shared/LocationEnrollCTA';
import EnrollmentCTA from '../shared/EnrollmentCTA';
import LocationPageTracker from '../analytics/LocationPageTracker';
import { useState } from 'react';
import { Location } from '@/data/locations/index';

interface AfterSchoolTemplateProps {
  locationData: Location;
}

const iconMap: { [key: string]: LucideIcon } = {
  Trees,
  Utensils,
  Gamepad2,
  BookOpen,
  HeartPulse,
  Bus,
  GraduationCap,
  Baby,
  School
};

const getDirectionsUrl = (address: { street: string; city: string; state: string }, name: string) => {
  if (!name) return '';

  if (name.toLowerCase().includes('bima')) {
    return 'https://maps.app.goo.gl/sMoAmrHxyLeKYfwH9';
  }
  const formattedAddress = encodeURIComponent(`${address.street}, ${address.city}, ${address.state}`);
  return `https://www.google.com/maps/dir/?api=1&destination=${formattedAddress}`;
};

const getIcon = (iconName?: string) => {
  if (!iconName || !iconMap[iconName]) {
    return School;
  }
  return iconMap[iconName];
};

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex justify-between items-center w-full py-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg font-semibold">{question}</span>
        {isOpen ? (
          <Minus className="w-5 h-5 text-[#009990]" />
        ) : (
          <Plus className="w-5 h-5 text-[#009990]" />
        )}
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function AfterSchoolTemplate({ locationData }: AfterSchoolTemplateProps) {
  if (!locationData) return null;

  // Redirect to IndiaLocationTemplate if country is india
  if (locationData.country === 'india') {
    throw new Error('AfterSchoolTemplate should only be used for USA locations. Use IndiaLocationTemplate for Indian locations.');
  }

  const {
    name,
    badge,
    heading,
    description,
    heroImage,
    heroImageAlt,
    facilities,
    address,
    accessibility,
    safety,
    community,
    faq,
    director,
    slug,
    features,
    country
  } = locationData;

  const locationSlug = slug || (name ? name.toLowerCase().replace(/\s+/g, '-') : '');

  return (
    <main className="other-pages-container min-h-screen">
      {/* Google Ads Location Tracking */}
      <LocationPageTracker locationId={locationSlug} locationName={name} />

      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src={heroImage}
            alt={heroImageAlt}
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations" className="hover:text-white transition-colors">
                    Locations
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations/usa" className="hover:text-white transition-colors">
                    USA
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li className="text-white font-medium">{name}</li>
              </ol>
            </nav>
            <div className="space-y-6 relative">
              <span className="inline-block bg-[#009990]/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium">
                {badge}
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white leading-tight drop-shadow-sm">
                {heading}
              </h1>
              <p className="text-lg text-white/95 max-w-2xl drop-shadow-sm">
                {description}
              </p>
              <div className="pt-4">
                <Link
                  href={`/contact?country=${country}&location=${locationSlug}&program=afterSchool`}
                  className="inline-flex items-center bg-white/95 text-[#001A6E] text-base px-6 py-3 rounded-lg font-medium hover:bg-[#009990] hover:text-white transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-[42px] font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#074799] to-[#009990]">
                About Our After School Program
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover a nurturing environment where children thrive through engaging activities and personalized attention.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src={heroImage}
                  alt="Children engaged in after school activities"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-4">
                    A Safe and Enriching Environment
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    Our after school program provides a perfect blend of academic support and recreational activities. We create an environment where children can learn, grow, and have fun in a safe and supervised setting.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-4">
                    Comprehensive Program Features
                  </h3>
                  <ul className="space-y-4">
                    {features.map((feature, index) => {
                      const Icon = getIcon(feature.iconName);
                      return (
                        <li key={index} className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                              <Icon className="w-5 h-5 text-[#009990]" />
                            </div>
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1">{feature.title}</h4>
                            <p className="text-gray-600">{feature.description}</p>
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* After School Section */}
      <section className="bg-white" id="programs">
        <AfterSchoolSection />
      </section>

      {/* Enrollment CTA */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-8">
          <LocationEnrollCTA
            locationSlug={locationSlug}
            programType="afterSchool"
            className="max-w-5xl mx-auto"
          />
        </div>
      </section>



      {/* Director Bio Section */}
      {locationData.director && (
        <section className="py-24 bg-gradient-to-b from-white to-gray-50">
          <div className="container mx-auto px-8">
            <DirectorBio
              location={locationData.name}
              director={locationData.director}
            />
          </div>
        </section>
      )}



      {/* Facilities Section */}
      <section className="py-24 bg-gradient-to-b from-white via-gray-50/50 to-white" id="facilities">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">


            {/* Section Header */}
            <div className="text-center mb-20">
              <div className="inline-flex flex-col items-center">
                <span className="px-4 py-1 bg-[#E6FFF9] text-[#009990] font-medium text-sm rounded-full">
                  Our Campus
                </span>
                <h2 className="mt-6 text-4xl lg:text-5xl font-bold text-[#001A6E]">
                  World-Class Facilities
                </h2>
                <div className="mt-4 w-20 h-1.5 bg-gradient-to-r from-[#074799] to-[#009990] rounded-full"></div>
                <p className="mt-6 text-xl text-gray-600 max-w-3xl">
                  Experience our state-of-the-art facilities designed to nurture learning, creativity, and growth
                </p>
              </div>
            </div>

            {/* Facilities Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {facilities.map((facility, index) => {
                const Icon = getIcon(facility.iconName);
                return (
                  <div
                    key={index}
                    className="group relative bg-white rounded-3xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-full"
                  >
                    {/* Card Header with Icon */}
                    <div className="relative p-6">
                      {/* Background Pattern */}
                      <div className="absolute top-0 right-0 w-24 h-24 bg-[#E6FFF9] rounded-full opacity-20 transform translate-x-12 -translate-y-12"></div>

                      {/* Icon Container */}
                      <div className="relative">
                        <div className="w-14 h-14 bg-[#E6FFF9] rounded-2xl flex items-center justify-center transform rotate-[10deg] group-hover:rotate-0 transition-transform duration-300">
                          <Icon className="w-7 h-7 text-[#009990] transform -rotate-[10deg] group-hover:rotate-0 transition-transform duration-300" />
                        </div>
                      </div>

                      {/* Facility Name */}
                      <h3 className="text-xl font-bold text-[#001A6E] mt-4 group-hover:text-[#074799] transition-colors duration-300 line-clamp-1">
                        {facility.name}
                      </h3>
                    </div>

                    {/* Card Content */}
                    <div className="p-6 pt-0">
                      {/* Description */}
                      <p className="text-gray-600 leading-relaxed text-sm line-clamp-3">
                        {facility.description}
                      </p>

                      {/* Features List */}
                      <ul className="mt-4 space-y-2">
                        {[...Array(2)].map((_, i) => (
                          <li key={i} className="flex items-center gap-2 text-gray-600">
                            <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                            <span className="text-sm">
                              {i === 0 ? 'Modern Equipment' : 'Safe Environment'}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Hover Effect Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-[#E6FFF9]/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                );
              })}
            </div>

            {/* Additional Features */}
            <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { icon: ShieldCheck, title: 'Safety First', desc: 'Advanced security systems' },
                { icon: Building2, title: 'Modern Campus', desc: 'State-of-the-art infrastructure' },
                { icon: Users, title: 'Small Groups', desc: 'Personalized attention' },
                { icon: Brain, title: 'Learning Spaces', desc: 'Designed for development' }
              ].map((feature, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-[#E6FFF9] rounded-xl flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#001A6E]">{feature.title}</h4>
                      <p className="text-sm text-gray-600">{feature.desc}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Section */}
            <div className="mt-16 text-center">
              <Link
                href={`/contact?country=usa&location=${locationSlug}&program=afterschool`}
                className="inline-flex items-center px-8 py-4 text-white bg-gradient-to-r from-[#074799] to-[#009990] rounded-full hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
              >
                Schedule a Campus Tour
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-50" id="contact">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Contact Us</h2>
              <p className="text-gray-600 text-lg">
                Get in touch with us to learn more about our programs and enrollment opportunities.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Location Information */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Visit Our Location</h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <MapPin className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Address</h4>
                      <p className="text-gray-600">{address.street}</p>
                      <p className="text-gray-600">{address.city}, {address.state} {address.zip}</p>
                      <a
                        href={getDirectionsUrl(address, name)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-[#009990] hover:text-[#008880] mt-2 font-medium"
                      >
                        Get Directions
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <Phone className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                      <a
                        href={`tel:${address.phone}`}
                        className="text-gray-600 hover:text-[#009990]"
                      >
                        {address.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                        <Mail className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                      <a
                        href={`mailto:${address.email}`}
                        className="text-gray-600 hover:text-[#009990]"
                      >
                        {address.email}
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule a Tour */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Schedule a Tour</h3>
                <p className="text-gray-600 mb-6">
                  Visit our facility to experience our nurturing environment firsthand. Our team will be happy to show you around and answer any questions you may have.
                </p>
                <Link
                  href={`/contact?country=${country}&location=${locationSlug}&program=afterSchool`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}

    </main>
  );
}
