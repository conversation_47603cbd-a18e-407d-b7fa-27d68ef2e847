import Script from 'next/script'
import { GA_MEASUREMENT_ID, GTM_ID, GOOGLE_ADS_ID } from '../utils/analytics'

export default function GoogleAnalyticsScript() {
  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-init"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
          `,
        }}
      />
      <Script
        id="google-tag-manager"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `,
        }}
      />

      {/* Google Analytics */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
              send_page_view: true
            });
          `,
        }}
      />

      {/* Google Ads Tag */}
      <Script
        id="google-ads-tag"
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GOOGLE_ADS_ID}`}
      />
      <Script
        id="google-ads-config"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GOOGLE_ADS_ID}');
          `,
        }}
      />

      {/* Route change tracking script */}
      <Script
        id="gtm-route-change"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Track Next.js route changes
            if (typeof window !== 'undefined') {
              const handleRouteChange = (url) => {
                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                  event: 'pageview',
                  page: url,
                });

                if (window.gtag) {
                  window.gtag('config', '${GA_MEASUREMENT_ID}', {
                    page_path: url,
                  });
                }
              };

              // Initial page load
              handleRouteChange(window.location.pathname + window.location.search);

              // Listen for route changes
              document.addEventListener('nextjs:route-change-complete', (e) => {
                handleRouteChange(window.location.pathname + window.location.search);
              });
            }
          `,
        }}
      />
    </>
  )
}
