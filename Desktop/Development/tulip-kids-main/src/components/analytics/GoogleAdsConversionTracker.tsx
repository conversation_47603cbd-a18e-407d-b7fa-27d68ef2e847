'use client';

import { useEffect } from 'react';
import { GOOGLE_ADS_ID } from '@/utils/analytics';

interface GoogleAdsConversionTrackerProps {
  conversionId?: string;
  conversionLabel?: string;
  value?: number;
  currency?: string;
}

/**
 * Component to track Google Ads conversions
 * This component should be included on pages where you want to track conversions
 */
export default function GoogleAdsConversionTracker({
  conversionId = GOOGLE_ADS_ID,
  conversionLabel = 'contact',
  value = 1.0,
  currency = 'USD'
}: GoogleAdsConversionTrackerProps) {
  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      try {
        // Make sure gtag is available
        if (window.gtag) {
          console.log(`Tracking Google Ads conversion: ${conversionId}/${conversionLabel}`);
          
          // Track the conversion
          window.gtag('event', 'conversion', {
            send_to: `${conversionId}/${conversionLabel}`,
            value: value,
            currency: currency,
            transaction_id: ''
          });
        } else {
          console.warn('Google Ads conversion tracking failed: gtag not available');
        }
      } catch (error) {
        console.error('Error tracking Google Ads conversion:', error);
      }
    }
  }, [conversionId, conversionLabel, value, currency]);

  return null; // This component doesn't render anything
}
