'use client'

import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  MapPin, 
  Phone, 
  Mail, 
  Menu, 
  X, 
  ChevronDown, 
  ChevronRight 
} from 'lucide-react'
import { usePathname, useRouter } from 'next/navigation'
import { contactInfo, navigationItems } from '@/data/navigation'

export default function HeaderSection() {
  const [isOpen, setIsOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)
  const pathname = usePathname()
  const router = useRouter()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const currentLocation = pathname?.includes('/india') ? 'india' : 'usa'

  useEffect(() => {
    // Close dropdown when pathname changes
    setActiveDropdown(null)
    setIsOpen(false)
  }, [pathname])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setActiveDropdown(null)
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEscape)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [])

  const handleMouseEnter = (title: string) => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
    }
    setActiveDropdown(title)
  }

  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setActiveDropdown(null)
    }, 200)
    setHoverTimeout(timeout)
  }

  const toggleMenu = () => {
    setIsOpen(!isOpen)
    setActiveDropdown(null)
  }

  const handleDropdownClick = (title: string) => {
    setActiveDropdown(activeDropdown === title ? null : title)
  }

  const handleLinkClick = () => {
    setActiveDropdown(null)
    setIsOpen(false)
  }

  const handleEnrollNow = () => {
    router.push('/enroll')
    setActiveDropdown(null)
    setIsOpen(false)
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-[100] bg-white shadow-sm">
      {/* Top Bar */}
      <div className="bg-[#001A6E] text-white h-10">
        <div className="container mx-auto px-4">
          {/* Mobile Top Bar */}
          <div className="lg:hidden">
            <div className="flex flex-col py-2 text-sm">
              <div className="flex items-center justify-center space-x-6 mb-2">
                <Link 
                  href="/locations/usa" 
                  className={`flex items-center transition-colors ${pathname?.includes('/usa') ? 'text-white' : 'text-gray-300 hover:text-white'}`}
                >
                  <MapPin className="w-4 h-4 mr-1" />
                  <span className="text-sm">USA</span>
                </Link>
                <span className="text-gray-400">|</span>
                <Link 
                  href="/locations/india" 
                  className={`flex items-center transition-colors ${pathname?.includes('/india') ? 'text-white' : 'text-gray-300 hover:text-white'}`}
                >
                  <MapPin className="w-4 h-4 mr-1" />
                  <span className="text-sm">India</span>
                </Link>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <span className="text-sm">US: {contactInfo.usa.phone}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <span className="text-sm">IN: {contactInfo.india.phone}</span>
                </div>
                <a href={`mailto:${contactInfo[currentLocation].email}`} className="flex items-center hover:text-gray-200">
                  <Mail className="w-4 h-4 mr-1" />
                  <span className="text-xs truncate max-w-[150px]">{contactInfo[currentLocation].email}</span>
                </a>
              </div>
            </div>
          </div>

          {/* Desktop Top Bar */}
          <div className="hidden lg:flex items-center justify-end h-10 text-sm">
            <div className="flex items-center space-x-6 mr-8">
              <Link 
                href="/locations/usa" 
                className={`flex items-center transition-colors ${pathname?.includes('/usa') ? 'text-white' : 'text-gray-300 hover:text-white'}`}
              >
                <MapPin className="w-4 h-4 mr-1" />
                United States
              </Link>
              <span className="text-gray-400">|</span>
              <Link 
                href="/locations/india" 
                className={`flex items-center transition-colors ${pathname?.includes('/india') ? 'text-white' : 'text-gray-300 hover:text-white'}`}
              >
                <MapPin className="w-4 h-4 mr-1" />
                India
              </Link>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-6">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  <span>US: {contactInfo.usa.phone}</span>
                </div>
                <span className="text-gray-400">|</span>
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  <span>IN: {contactInfo.india.phone}</span>
                </div>
              </div>
              <span className="text-gray-400">|</span>
              <a href={`mailto:${contactInfo[currentLocation].email}`} className="flex items-center hover:text-gray-200">
                <Mail className="w-4 h-4 mr-1" />
                {contactInfo[currentLocation].email}
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="relative bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-[94px]">
            {/* Logo */}
            <div className="relative -ml-4 lg:-ml-8">
              <Link href="/">
                <Image
                  src="/images/tulip-logo.png"
                  alt="Tulip Logo"
                  width={180}
                  height={180}
                  className="w-[90px] h-[90px] lg:w-[120px] lg:h-[120px] -translate-y-1"
                  priority
                />
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden p-2 rounded-md text-gray-700 hover:text-[#074799] focus:outline-none"
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center justify-end flex-1" ref={dropdownRef}>
              <div className="flex items-center gap-8">
                {navigationItems.map((item) => (
                  <div 
                    key={item.title} 
                    className="relative"
                    onMouseEnter={() => item.children && handleMouseEnter(item.title)}
                    onMouseLeave={handleMouseLeave}
                  >
                    {item.children ? (
                      <div className="relative">
                        <button
                          onClick={() => handleDropdownClick(item.title)}
                          className={`flex items-center py-8 px-2 text-gray-700 hover:text-[#074799] font-medium transition-all duration-200 ${
                            activeDropdown === item.title ? 'text-[#074799]' : ''
                          }`}
                          aria-expanded={activeDropdown === item.title}
                          aria-haspopup="true"
                        >
                          {item.title}
                          <ChevronDown 
                            className={`w-4 h-4 ml-1.5 transition-transform duration-200 ${
                              activeDropdown === item.title ? 'rotate-180' : ''
                            }`} 
                          />
                        </button>
                        <div 
                          className={`absolute top-full left-0 mt-0 w-72 bg-white rounded-lg shadow-lg py-2 transition-all duration-200 ${
                            activeDropdown === item.title 
                              ? 'opacity-100 visible translate-y-0' 
                              : 'opacity-0 invisible -translate-y-2'
                          }`}
                        >
                          {item.children.map((child) => (
                            <div key={child.title}>
                              {child.children ? (
                                <div className="relative group">
                                  <div 
                                    className="flex items-center justify-between w-full px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-[#074799] transition-all duration-200"
                                    role="button"
                                    tabIndex={0}
                                  >
                                    <div>
                                      <div className="font-medium text-base">{child.title}</div>
                                      {child.subText && (
                                        <div className="text-sm text-gray-500">{child.subText}</div>
                                      )}
                                    </div>
                                    <ChevronRight className="w-4 h-4 opacity-75" />
                                  </div>
                                  <div className="absolute left-full top-0 w-72 bg-white rounded-lg shadow-lg py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                    {child.children.map((subChild) => (
                                      <Link
                                        key={subChild.title}
                                        href={subChild.path}
                                        onClick={handleLinkClick}
                                        className="block px-6 py-3 hover:bg-gray-50 hover:text-[#074799] transition-all duration-200"
                                      >
                                        <div className="font-medium text-base">{subChild.title}</div>
                                        {subChild.subText && (
                                          <div className="text-sm text-gray-500">{subChild.subText}</div>
                                        )}
                                      </Link>
                                    ))}
                                  </div>
                                </div>
                              ) : (
                                <Link
                                  href={child.path}
                                  onClick={handleLinkClick}
                                  className="block px-6 py-3 hover:bg-gray-50 hover:text-[#074799] transition-all duration-200"
                                >
                                  <div className="font-medium text-base">{child.title}</div>
                                  {child.subText && (
                                    <div className="text-sm text-gray-500">{child.subText}</div>
                                  )}
                                </Link>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <Link
                        href={item.path}
                        onClick={handleLinkClick}
                        className={`py-8 px-2 text-gray-700 hover:text-[#074799] font-medium transition-all duration-200 ${
                          pathname === item.path ? 'text-[#074799]' : ''
                        }`}
                      >
                        {item.title}
                      </Link>
                    )}
                  </div>
                ))}
                {/* Enroll Now Button */}
                <button
                  onClick={handleEnrollNow}
                  className="px-6 py-2 bg-[#074799] text-white rounded-lg hover:bg-[#053d88] transition-colors duration-300"
                >
                  Enroll Now
                </button>
              </div>
            </nav>

            {/* Mobile Navigation */}
            <div
              className={`lg:hidden fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ${
                isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
              }`}
              onClick={toggleMenu}
            >
              <div
                className={`fixed inset-y-0 right-0 w-full max-w-sm bg-white shadow-xl transform transition-transform duration-300 ${
                  isOpen ? 'translate-x-0' : 'translate-x-full'
                }`}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between p-4 border-b">
                    <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
                    <button
                      onClick={toggleMenu}
                      className="p-2 rounded-md text-gray-700 hover:text-[#074799] focus:outline-none"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="flex-1 overflow-y-auto">
                    <nav className="px-2 py-4">
                      {navigationItems.map((item) => (
                        <div key={item.title}>
                          {item.children ? (
                            <div>
                              <button
                                onClick={() => handleDropdownClick(item.title)}
                                className="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-[#074799] rounded-lg transition-all duration-200"
                                aria-expanded={activeDropdown === item.title}
                              >
                                <span className="font-medium">{item.title}</span>
                                <ChevronDown
                                  className={`w-5 h-5 transition-transform duration-200 ${
                                    activeDropdown === item.title ? 'rotate-180' : ''
                                  }`}
                                />
                              </button>
                              {activeDropdown === item.title && (
                                <div className="ml-4 mt-2 space-y-2">
                                  {item.children.map((child) => (
                                    <div key={child.title}>
                                      {child.children ? (
                                        <div>
                                          <div className="px-4 py-2">
                                            <div className="font-medium text-gray-900">{child.title}</div>
                                            {child.subText && (
                                              <div className="text-sm text-gray-500">{child.subText}</div>
                                            )}
                                          </div>
                                          <div className="ml-4 space-y-2">
                                            {child.children.map((subChild) => (
                                              <Link
                                                key={subChild.title}
                                                href={subChild.path}
                                                onClick={handleLinkClick}
                                                className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-[#074799] rounded-lg transition-all duration-200"
                                              >
                                                <div className="font-medium">{subChild.title}</div>
                                                {subChild.subText && (
                                                  <div className="text-sm text-gray-500">{subChild.subText}</div>
                                                )}
                                              </Link>
                                            ))}
                                          </div>
                                        </div>
                                      ) : (
                                        <Link
                                          href={child.path}
                                          onClick={handleLinkClick}
                                          className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-[#074799] rounded-lg transition-all duration-200"
                                        >
                                          <div className="font-medium">{child.title}</div>
                                          {child.subText && (
                                            <div className="text-sm text-gray-500">{child.subText}</div>
                                          )}
                                        </Link>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ) : (
                            <Link
                              href={item.path}
                              onClick={handleLinkClick}
                              className={`block px-4 py-3 rounded-lg transition-all duration-200 ${
                                pathname === item.path
                                  ? 'bg-gray-50 text-[#074799]'
                                  : 'text-gray-700 hover:bg-gray-50 hover:text-[#074799]'
                              }`}
                            >
                              {item.title}
                            </Link>
                          )}
                        </div>
                      ))}
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
