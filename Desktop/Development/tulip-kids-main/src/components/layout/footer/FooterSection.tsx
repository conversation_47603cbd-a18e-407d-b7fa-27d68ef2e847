'use client'

import React, { useState } from 'react';
import Image from 'next/image';
import { Facebook, Instagram,  LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { socialLinks, programLinks, resourceLinks, legalLinks } from '@/constants/links';

const locations = {
  USA: [
    'Sunnyvale',
    'Santa Clara',
    'San Jose',
    'Mountain House',
    'Dublin'
  ],
  India: 
     [
      'Indore'
     ]
  };

const iconMap: Record<string, LucideIcon> = {
  Facebook,
  Instagram
};

const Footer = () => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus('loading');
    
    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email,
          to: '<EMAIL>' 
        }),
      });

      if (!response.ok) throw new Error('Subscription failed');
      
      setStatus('success');
      setEmail('');
    } catch (error) {
      setStatus('error');
    }
  };

  return (
    <footer className="relative bg-gradient-to-br from-[#001A6E] via-[#002699] to-[#001A6E] overflow-hidden">
      <div className="absolute inset-0 bg-[url('/assets/pattern-bg.png')] opacity-5"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 relative">
        {/* Top Section with Logo and Newsletter */}
        <div className="py-10 border-b border-white/10">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-3 text-white">Tulip Kids</h2>
              <p className="text-gray-300 text-base leading-relaxed max-w-lg">
                Nurturing young minds for a brighter tomorrow through quality early education.
              </p>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold mb-4 text-white">Stay Updated</h3>
              <form onSubmit={handleSubmit} className="flex flex-col gap-3">
                <div className="flex gap-3">
                  <input 
                    type="email" 
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email" 
                    required
                    className="flex-1 bg-white/10 border border-white/10 rounded-lg px-4 py-2 text-white placeholder:text-gray-400 focus:outline-none focus:border-[#009990] transition-all duration-300"
                  />
                  <button 
                    type="submit"
                    disabled={status === 'loading'}
                    className="bg-[#009990] hover:bg-[#00b3a8] text-white px-6 py-2 rounded-lg transition-all duration-300 disabled:opacity-50"
                  >
                    {status === 'loading' ? 'Subscribing...' : 'Subscribe'}
                  </button>
                </div>
                {status === 'success' && (
                  <p className="text-green-400 text-sm">Thank you for subscribing!</p>
                )}
                {status === 'error' && (
                  <p className="text-red-400 text-sm">Failed to subscribe. Please try again.</p>
                )}
              </form>
            </div>
          </div>
        </div>

        {/* Main Links Section */}
        <div className="py-10 grid md:grid-cols-12 gap-8">
          {/* Company Info & Social */}
          <div className="md:col-span-4">
            <h3 className="text-lg font-semibold mb-4 text-white">Connect With Us</h3>
            <div className="flex gap-3 mb-6">
              {socialLinks.map(({ id, Icon, href, label }) => {
                const SocialIcon = iconMap[Icon];
                return (
                  <a
                    key={id}
                    href={href}
                    aria-label={label}
                    className="w-10 h-10 rounded-lg bg-white/5 hover:bg-[#009990] backdrop-blur-sm flex items-center justify-center transition-all duration-300 border border-white/10 group"
                  >
                    {SocialIcon && (
                      <SocialIcon 
                        size={20} 
                        className="text-gray-300 group-hover:text-white transition-colors" 
                      />
                    )}
                  </a>
                );
              })}
            </div>
            <div className="border-l-2 border-[#009990]/30 pl-4">
              <p className="text-[#009990] text-sm font-medium italic">
                "Education is not preparation for life; education is life itself."
              </p>
              <p className="text-gray-400 text-xs mt-1">- John Dewey</p>
            </div>
          </div>

          {/* Resources */}
          <div className="md:col-span-3">
            <h3 className="text-lg font-semibold mb-4 text-white">Resources</h3>
            <ul className="space-y-2">
              {resourceLinks.map((link) => (
                <li key={link.id}>
                  <Link 
                    href={link.href} 
                    className="text-gray-300 hover:text-white transition-all duration-300 text-sm flex items-center group"
                  >
                    <span className="h-px w-0 bg-[#009990] group-hover:w-3 transition-all duration-300 mr-0 group-hover:mr-2"></span>
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Global Offices */}
          <div className="md:col-span-5">
            <h3 className="text-lg font-semibold mb-4 text-white">Global Presence</h3>
            <div className="grid grid-cols-2 gap-6">
              {/* USA Locations */}
              <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h4 className="flex items-center gap-2 mb-3">
                  <span className="text-lg">🇺🇸</span>
                  <span className="text-[#009990] font-semibold text-base">USA</span>
                </h4>
                <ul className="space-y-1">
                  {locations.USA.map((city, index) => (
                    <li 
                      key={index} 
                      className="text-gray-300 hover:text-white transition-all duration-300 text-sm flex items-center gap-2"
                    >
                      <span className="w-1 h-1 rounded-full bg-[#009990]"></span>
                      {city}
                    </li>
                  ))}
                </ul>
              </div>

              {/* India Locations */}
              <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h4 className="flex items-center gap-2 mb-3">
                  <span className="text-lg">🇮🇳</span>
                  <span className="text-[#009990] font-semibold text-base">India</span>
                </h4>
                <ul className="space-y-1">
                  {locations.India.map((city, index) => (
                    <li 
                      key={index} 
                      className="text-gray-300 hover:text-white transition-all duration-300 text-sm flex items-center gap-2"
                    >
                      <span className="w-1 h-1 rounded-full bg-[#009990]"></span>
                      {city}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="border-t border-white/10 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-sm">
              &copy; {new Date().getFullYear()} Tulip Kids Inc. All rights reserved.
            </p>
            <ul className="flex flex-wrap gap-6">
              {legalLinks.map((link) => (
                <li key={link.id}>
                  <Link 
                    href={link.href} 
                    className="text-gray-400 hover:text-white transition-all duration-300 text-sm"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;