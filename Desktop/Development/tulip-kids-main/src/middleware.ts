import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Cache configuration for different routes
const CACHE_CONFIG = {
  // Static pages with longer cache duration
  static: {
    paths: ['/about', '/programs'],
    maxAge: 60 * 60 * 24 * 7, // 7 days
  },
  // Dynamic pages with shorter cache duration
  dynamic: {
    paths: ['/blog', '/locations'],
    maxAge: 60 * 60, // 1 hour
  },
  // API routes with very short cache duration
  api: {
    paths: ['/api/'],
    maxAge: 60, // 1 minute
  },
} as const;

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Skip caching for non-GET requests
  if (request.method !== 'GET') {
    return response;
  }

  // Track page views for client-side navigation
  if (request.headers.get('x-middleware-prefetch') !== '1') {
    response.headers.set('x-ga-page-view', request.nextUrl.pathname);
  }

  // Get the pathname
  const pathname = new URL(request.url).pathname;

  // Function to check if path matches any patterns
  const matchesPath = (paths: readonly string[]) =>
    paths.some(path => pathname.startsWith(path));

  // Set cache headers based on route type
  if (matchesPath(CACHE_CONFIG.static.paths)) {
    response.headers.set(
      'Cache-Control',
      `public, s-maxage=${CACHE_CONFIG.static.maxAge}, stale-while-revalidate`
    );
  } else if (matchesPath(CACHE_CONFIG.dynamic.paths)) {
    response.headers.set(
      'Cache-Control',
      `public, s-maxage=${CACHE_CONFIG.dynamic.maxAge}, stale-while-revalidate=60`
    );
  } else if (matchesPath(CACHE_CONFIG.api.paths)) {
    response.headers.set(
      'Cache-Control',
      `public, s-maxage=${CACHE_CONFIG.api.maxAge}, stale-while-revalidate=30`
    );
  } else {
    // Default cache policy for other routes
    response.headers.set(
      'Cache-Control',
      'public, s-maxage=3600, stale-while-revalidate'
    );
  }

  // Add security headers
  response.headers.set('X-DNS-Prefetch-Control', 'on');
  response.headers.set('Strict-Transport-Security', 'max-age=63072000');
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');

  return response;
}

// Configure which routes should be processed by the middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images/ (public images)
     */
    '/((?!_next/static|_next/image|favicon.ico|images/).*)',
  ],
};
