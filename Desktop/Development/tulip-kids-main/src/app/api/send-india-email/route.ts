import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Create transporter with Gmail credentials
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'lbxg lnmz dtyt nuuz'
  },
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('Received India form data:', body);

    const {
      name,
      email,
      phone,
      childAge,
      program,
      preferredTime,
      message,
      locationId,
      source
    } = body;

    // Format location name for display
    const formattedLocation = locationId === 'nipania'
      ? 'Nipania, Indore'
      : locationId === 'bima-nagar'
        ? 'Bima Nagar, Indore'
        : 'India';

    // Always send to the India email
    const recipientEmail = '<EMAIL>'; // Production email
    // const recipientEmail = '<EMAIL>'; // Testing email

    console.log('Sending India inquiry email to:', recipientEmail);

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .header {
          background-color: #001A6E;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: #fff;
          border: 1px solid #ddd;
        }
        .section {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .section h2 {
          color: #001A6E;
          margin-top: 0;
          font-size: 1.2em;
          border-bottom: 2px solid #001A6E;
          padding-bottom: 5px;
        }
        .field {
          margin-bottom: 10px;
        }
        .label {
          font-weight: bold;
          color: #555;
        }
        .footer {
          text-align: center;
          padding: 15px;
          background-color: #f5f5f5;
          font-size: 0.9em;
          color: #666;
          border-radius: 0 0 5px 5px;
        }
        .divider {
          border-top: 1px solid #eee;
          margin: 20px 0;
        }
        .tracking {
          background-color: #fffbea;
          padding: 10px;
          border-radius: 5px;
          margin-top: 20px;
          font-size: 0.9em;
          color: #775500;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>New India Enrollment Inquiry</h1>
        <p>${formattedLocation}</p>
      </div>

      <div class="content">
        <div class="section">
          <h2>Student Information</h2>
          <div class="field">
            <span class="label">Name:</span> ${name}
          </div>
          <div class="field">
            <span class="label">Child's Age:</span> ${childAge || 'Not specified'}
          </div>
          <div class="field">
            <span class="label">Program:</span> ${program || 'Not specified'}
          </div>
        </div>

        <div class="section">
          <h2>Contact Information</h2>
          <div class="field">
            <span class="label">Email:</span> ${email}
          </div>
          <div class="field">
            <span class="label">Phone:</span> +91 ${phone}
          </div>
          <div class="field">
            <span class="label">Location:</span> ${formattedLocation}
          </div>
          <div class="field">
            <span class="label">Preferred Time:</span> ${preferredTime || 'Not specified'}
          </div>
        </div>

        <div class="section">
          <h2>Additional Message</h2>
          <p>${message || 'No additional message provided'}</p>
        </div>

        <div class="tracking">
          <h3>Tracking Information</h3>
          <div class="field">
            <span class="label">Source:</span> ${source || 'Direct'}
          </div>
          <div class="field">
            <span class="label">Location ID:</span> ${locationId}
          </div>
        </div>

        <div class="divider"></div>

        <div class="footer">
          <p>Received: ${new Date().toLocaleString('en-US', {
      timeZone: 'Asia/Kolkata',
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })}</p>
          <p>© ${new Date().getFullYear()} Tulip Kids India. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Verify SMTP connection before sending
    console.log('Verifying SMTP connection...');
    await transporter.verify();
    console.log('SMTP connection verified successfully');

    // Send email and await the result
    const info = await transporter.sendMail({
      from: 'Tulip Kids India <<EMAIL>>',
      to: recipientEmail,
      subject: `India Inquiry - ${formattedLocation} - ${source || 'Website'}`,
      html: htmlContent
    });

    console.log('India inquiry email sent successfully:', info?.messageId || 'No message ID');

    return NextResponse.json(
      { message: 'Email sent successfully', messageId: info?.messageId || null },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending India inquiry email:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to send email' },
      { status: 500 }
    );
  }
}
