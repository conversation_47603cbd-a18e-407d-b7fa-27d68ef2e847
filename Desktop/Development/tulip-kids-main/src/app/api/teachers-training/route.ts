import { NextResponse } from 'next/server';
import { sendEmail } from '@/utils/emailService';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, email, phone, city, state, country, experience, qualification, currentSchool, message } = body;

    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #001A6E; color: white; padding: 20px; text-align: center;">
          <h1>New Teachers Training Registration</h1>
        </div>

        <div style="padding: 20px;">
          <h2 style="color: #001A6E;">Personal Information</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Phone:</strong> ${phone}</p>

          <h2 style="color: #001A6E;">Location</h2>
          <p><strong>City:</strong> ${city}</p>
          <p><strong>State:</strong> ${state}</p>
          <p><strong>Country:</strong> ${country}</p>

          <h2 style="color: #001A6E;">Professional Details</h2>
          <p><strong>Teaching Experience:</strong> ${experience}</p>
          <p><strong>Qualification:</strong> ${qualification}</p>
          <p><strong>Current School:</strong> ${currentSchool || 'Not specified'}</p>

          <h2 style="color: #001A6E;">Additional Message</h2>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
            ${message || 'No additional message provided'}
          </div>
        </div>

        <div style="text-align: center; padding: 20px; color: #666; font-size: 12px; border-top: 1px solid #eee;">
          <p>Received: ${new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })}</p>
          <p>© ${new Date().getFullYear()} Tulip Kids</p>
        </div>
      </div>
    `;

    const result = await sendEmail({
      subject: `New Teachers Training Registration from ${name}`,
      senderName: name,
      senderEmail: "<EMAIL>",
      htmlContent: emailContent,
      to: [{
        email: "<EMAIL>", // Production email
        // email: "<EMAIL>", // Testing email
        name: "Tulip Kids"
      }],
    });

    return NextResponse.json({ success: true, message: 'Email sent successfully' });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process request' },
      { status: 500 }
    );
  }
}
