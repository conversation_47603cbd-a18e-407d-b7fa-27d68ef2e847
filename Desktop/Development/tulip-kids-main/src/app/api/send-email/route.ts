import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { getLocationDetails } from '@/data/locationMappings';

// Create transporter with Gmail credentials
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'lbxg lnmz dtyt nuuz'
  },
});

// Convert string to title case
function toTitleCase(str: string) {
  return str.split(' ').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ');
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('Received form data:', body);

    const { country, location, name, email, phone, childAge, program, preferredTime, message } = body;

    // Get full location details
    const locationDetails = getLocationDetails(location.toLowerCase().replace(/\s+/g, '-'), country.toLowerCase());

    // Format location address to only include street and city
    const formattedLocation = locationDetails
      ? locationDetails.fullAddress.replace(/,\s*(?:[A-Z]{2}\s+)?\d{5,6}(?:[-\s]\d{4})?,\s*(?:United States|India)$/, '')
        .replace(/,\s*(?:CA|Madhya Pradesh)(?=,|$)/, '')
      : location ? toTitleCase(location) : 'Not specified';

    // Determine recipient email based on country selection
    let recipientEmail;
    if (country.toLowerCase() === 'usa') {
      recipientEmail = '<EMAIL>';
      // recipientEmail = '<EMAIL>'; // Use a different email <NAME_EMAIL>';
    } else if (country.toLowerCase() === 'india') {
      recipientEmail = '<EMAIL>'; // Production email
      // recipientEmail = '<EMAIL>'; // Testing email
    } else {
      throw new Error('Invalid country selection');
    }

    console.log('Sending email to:', recipientEmail);

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .header {
          background-color: #001A6E;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: #fff;
          border: 1px solid #ddd;
        }
        .section {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .section h2 {
          color: #001A6E;
          margin-top: 0;
          font-size: 1.2em;
          border-bottom: 2px solid #001A6E;
          padding-bottom: 5px;
        }
        .field {
          margin-bottom: 10px;
        }
        .label {
          font-weight: bold;
          color: #555;
        }
        .footer {
          text-align: center;
          padding: 15px;
          background-color: #f5f5f5;
          font-size: 0.9em;
          color: #666;
          border-radius: 0 0 5px 5px;
        }
        .divider {
          border-top: 1px solid #eee;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>New Enrollment Inquiry</h1>

      </div>

      <div class="content">
        <div class="section">
          <h2>Student Information</h2>
          <div class="field">
            <span class="label">Name:</span> ${name}
          </div>
          <div class="field">
            <span class="label">Child's Age:</span> ${childAge}
          </div>
          <div class="field">
            <span class="label">Program:</span> ${program}
          </div>
        </div>

        <div class="section">
          <h2>Contact Information</h2>
          <div class="field">
            <span class="label">Email:</span> ${email}
          </div>
          <div class="field">
            <span class="label">Phone:</span> ${phone}
          </div>

          <div class="field">
            <span class="label">Location:</span> ${formattedLocation}
          </div>
          <div class="field">
            <span class="label">Preferred Time:</span> ${preferredTime}
          </div>
        </div>

        <div class="section">
          <h2>Additional Message</h2>
          <p>${message || 'No additional message provided'}</p>
        </div>

        <div class="divider"></div>

        <div class="footer">
          <p>Received: ${new Date().toLocaleString('en-US', {
      timeZone: 'Asia/Kolkata',
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })}</p>
          <p> ${new Date().getFullYear()} Tulip Kids. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Verify SMTP connection before sending
    console.log('Verifying SMTP connection...');
    await transporter.verify();
    console.log('SMTP connection verified successfully');

    // Send email and await the result
    const info = await transporter.sendMail({
      from: 'Tulip Kids <<EMAIL>>',
      to: recipientEmail,
      subject: country.toLowerCase() === 'india'
        ? `India Inquiry - ${formattedLocation}`
        : `New Enrollment Inquiry - ${formattedLocation}`,
      html: htmlContent
    });

    console.log('Email sent successfully:', info?.messageId || 'No message ID');

    return NextResponse.json(
      { message: 'Email sent successfully', messageId: info?.messageId || null },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to send email' },
      { status: 500 }
    );
  }
}