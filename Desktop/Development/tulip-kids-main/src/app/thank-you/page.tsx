'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import ContactFormConversionTracker from '@/components/analytics/ContactFormConversionTracker';
import GoogleAdsConversionTracker from '@/components/analytics/GoogleAdsConversionTracker';

export default function ThankYou() {
  const searchParams = useSearchParams();
  const country = searchParams.get('country') || 'usa';
  const location = searchParams.get('location') || '';

  // Track conversion when the thank-you page loads
  useEffect(() => {
    // Make sure we're on the client side
    if (typeof window !== 'undefined' && window.gtag) {
      console.log('Thank you page loaded - tracking conversion');

      // Track the conversion
      window.gtag('event', 'conversion', {
        send_to: 'AW-16615339344/contact',
        value: 1.0,
        currency: 'USD'
      });
    }
  }, []);

  // Contact information based on country and location
  const contactInfo = {
    usa: {
      email: '<EMAIL>',
      phone: '+****************'
    },
    india: {
      default: {
        email: '<EMAIL>',
        phone: '+91 9575545200'
      },
      'nipania': {
        email: '<EMAIL>',
        phone: '+91 9575545200'
      },
      'bima-nagar': {
        email: '<EMAIL>',
        phone: '+91 731-4999788'
      }
    }
  };

  // Get the appropriate contact info based on country and location
  let info;
  if (country.toLowerCase() === 'india') {
    // Check if we have specific location info for India
    if (location && contactInfo.india[location]) {
      info = contactInfo.india[location];
    } else {
      info = contactInfo.india.default;
    }
  } else {
    info = contactInfo.usa;
  }

  return (
    <div className="container mx-auto">
      {/* Google Ads Conversion Tracking */}
      <ContactFormConversionTracker />
      <GoogleAdsConversionTracker />

      <div className="min-h-[50vh] flex items-center justify-center px-4 py-16 md:py-24 mt-24">
        <div className="max-w-md w-full text-center">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          <h1 className="text-3xl font-bold text-[#001A6E] mb-4">
            Thank You!
          </h1>

          <p className="text-gray-600 mb-8 text-lg">
            Your message has been successfully sent. Our team will get back to you within 24-48 hours.
          </p>

          <div className="space-y-6">
            <Link
              href={country.toLowerCase() === 'india' ? '/locations/india' : '/'}
              className="inline-block bg-[#001A6E] text-white px-8 py-3 rounded-xl font-medium
                       hover:bg-[#002288] transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-[#001A6E]/50
                       shadow-lg shadow-[#001A6E]/20"
            >
              {country.toLowerCase() === 'india' ? 'Explore India Locations' : 'Return to Home'}
            </Link>

            <div className="text-sm text-gray-500 space-y-2">
              <div>
                Have questions? Contact us at{' '}
                <a
                  href={`mailto:${info.email}`}
                  className="text-[#001A6E] hover:underline"
                >
                  {info.email}
                </a>
              </div>
              <div>
                Or call us at{' '}
                <a
                  href={`tel:${info.phone.replace(/\s/g, '')}`}
                  className="text-[#001A6E] hover:underline"
                >
                  {info.phone}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
