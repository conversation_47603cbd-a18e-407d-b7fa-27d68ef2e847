'use client';

import Image from 'next/image';
import Link from 'next/link';
import { 
  <PERSON>R<PERSON>, 
  Award, 
  Lightbulb,
  Users,
  HeartHandshake,
  ShieldCheck,
  Sprout
} from 'lucide-react';

export default function VisionPage() {
  return (
    <main className="page-container min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/about-us.jpg"
            alt="Tulip Kids Vision and Mission"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <nav className="mb-6" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm text-white/90">
              <li>
                <Link href="/" className="hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <span className="text-white/60 mx-2">→</span>
              </li>
              <li>
                <Link href="/about" className="hover:text-white transition-colors">
                  About
                </Link>
              </li>
              <li>
                <span className="text-white/60 mx-2">→</span>
              </li>
              <li>
                <span className="text-white">Vision & Mission</span>
              </li>
            </ol>
          </nav>
          <div className="max-w-4xl">
            <span className="inline-block px-3 py-1 bg-[#009990] text-white text-sm rounded-full mb-4">
              About Us
            </span>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Vision & Mission
            </h1>
            <p className="text-xl text-white/90 max-w-2xl">
              Building a foundation for lifelong learning and success through innovative early childhood education.
            </p>
          </div>
        </div>
      </section>

      {/* Vision Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">Our Vision</h2>
                <div className="prose max-w-none text-lg space-y-6">
                  <p className="text-gray-600 leading-relaxed">
                    To be the leading provider of innovative early childhood education, nurturing every child's potential to become confident, creative, and compassionate leaders of tomorrow.
                  </p>
                  <ul className="space-y-4 text-gray-600">
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Creating innovative learning environments that inspire curiosity and creativity</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Fostering a love for learning that lasts a lifetime</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Developing future leaders with strong values and global perspectives</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="relative h-[400px] rounded-3xl overflow-hidden transform transition-transform hover:scale-[1.02] duration-300">
                <div className="absolute inset-0 shadow-[0_20px_50px_rgba(8,_112,_184,_0.7)] rounded-3xl"></div>
                <Image
                  src="/images/vision.webp"
                  alt="Our Vision at Tulip Kids"
                  fill
                  className="object-cover hover:opacity-90 transition-opacity duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#001A6E]/10 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="order-2 md:order-1 relative h-[400px] rounded-3xl overflow-hidden transform transition-transform hover:scale-[1.02] duration-300">
                <div className="absolute inset-0 shadow-[0_20px_50px_rgba(8,_112,_184,_0.7)] rounded-3xl"></div>
                <Image
                  src="/images/mission.webp"
                  alt="Our Mission at Tulip Kids"
                  fill
                  className="object-cover hover:opacity-90 transition-opacity duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#001A6E]/10 to-transparent"></div>
              </div>
              <div className="order-1 md:order-2">
                <h2 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">Our Mission</h2>
                <div className="prose max-w-none text-lg space-y-6">
                  <p className="text-gray-600 leading-relaxed">
                    To provide exceptional early childhood education through innovative teaching methods, nurturing environments, and a commitment to developing the whole child.
                  </p>
                  <ul className="space-y-4 text-gray-600">
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Delivering comprehensive, age-appropriate educational programs</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Employing and developing exceptional educators</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Creating strong partnerships with families and communities</span>
                    </li>
                    <li className="flex items-start">
                      <ArrowRight className="w-5 h-5 text-[#009990] mt-1 mr-3 flex-shrink-0" />
                      <span>Maintaining the highest standards of safety and care</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <span className="inline-block px-3 py-1 bg-[#009990] text-white text-sm rounded-full mb-4">
                Our Foundation
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-[#001A6E]">Our Core Values</h2>
              <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
                The principles that guide us in nurturing tomorrow's leaders
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  title: "Excellence",
                  description: "Striving for the highest standards in education and care, continuously improving our methods and practices.",
                  icon: Award
                },
                {
                  title: "Innovation",
                  description: "Embracing new ideas and technologies to enhance learning experiences and educational outcomes.",
                  icon: Lightbulb
                },
                {
                  title: "Inclusivity",
                  description: "Creating a welcoming environment that celebrates diversity and promotes understanding among all children.",
                  icon: Users
                },
                {
                  title: "Partnership",
                  description: "Working closely with families and communities to create a supportive learning ecosystem.",
                  icon: HeartHandshake
                },
                {
                  title: "Safety",
                  description: "Maintaining secure and nurturing environments where children can learn and grow with confidence.",
                  icon: ShieldCheck
                },
                {
                  title: "Growth",
                  description: "Fostering continuous development in children, staff, and our educational programs.",
                  icon: Sprout
                }
              ].map((value, index) => (
                <div 
                  key={index} 
                  className="group bg-white border border-gray-100 rounded-3xl p-8 hover:bg-gradient-to-br from-[#001A6E]/5 to-[#009990]/5 transition-all duration-300 hover:shadow-[0_20px_50px_rgba(8,_112,_184,_0.1)] hover:-translate-y-1"
                >
                  <div className="flex flex-col items-start">
                    <div className="mb-6 p-3 rounded-2xl bg-[#001A6E]/5 group-hover:bg-[#001A6E]/10 transition-colors duration-300">
                      {value.icon && <value.icon className="w-8 h-8 text-[#001A6E] group-hover:text-[#009990] transition-colors duration-300" />}
                    </div>
                    <h3 className="text-xl font-bold text-[#001A6E] mb-4 group-hover:text-[#009990] transition-colors duration-300">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
