import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://tulipkidsinc.com'
  
  const routes = [
    '',
    '/locations',
    '/programs',
    '/about',
    '/blog',
    '/contact',
    '/us',
    '/india'
  ].map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date().toISOString().split('T')[0],
    changeFrequency: 'weekly' as const,
    priority: route === '' ? 1 : 0.8,
  }))

  // Add dynamic routes for locations
  const locationRoutes = [
    'sunnyvale-willow',
    'sunnyvale-lawrence',
    'santa-clara',
    'san-jose',
    'mountain-house',
    'dublin'
  ].map((slug) => ({
    url: `${baseUrl}/us/${slug}`,
    lastModified: new Date().toISOString().split('T')[0],
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))

  return [...routes, ...locationRoutes]
}
