import IndiaLocationTemplate from '@/components/templates/IndiaLocationTemplate';
import { indiaLocations } from '@/data/locations/index';
import { Metadata } from 'next';

export const metadata: Metadata = {
  
    title: 'Best Preschool in Anand Bazaar, Indore | Play School and Nursery School',
  description: 'Tulip Kids Preschools in Indore, is the leading choice for Play School, Nursery, and Kindergarten education for kids. We have 15 years of excellence and 8+ schools across 2 countries. ',
  keywords: ['preschool Anand Bazaar', 'Bima Nagar preschool', 'kindergarten Indore', 'Tulip Kids Indore', 'nursery Anand Bazaar'],
  openGraph: {
    title: 'Best Preschool in Anand Bazaar, Indore | Play School and Nursery School',
    description: 'Tulip Kids Preschools in Indore, is the leading choice for Play School, Nursery, and Kindergarten education for kids. We have 15 years of excellence and 8+ schools across 2 countries. ',
    url: 'https://www.tulipkidsinc.com/locations/india/bima-nagar',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};

export default function BimaNagarPage() {
  const locationData = {
    ...indiaLocations['bima-nagar'],
    country: 'india' as const
  };

  return (
    <IndiaLocationTemplate 
      locationData={locationData}
    />
  );
}
