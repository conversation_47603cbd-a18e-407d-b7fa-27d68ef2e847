'use client';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  ArrowRight,
  GraduationCap,
  Building2,
  Puzzle,
  Trees,
  Phone,
  Mail,
  MapPin,
  Baby,
  School,
  Plus,
  Minus
} from 'lucide-react';
import { indiaLocations } from '@/data/locations/index';
import IndiaExcellenceSection from '@/components/sections/IndiaExcellenceSection';
import LocationEnrollCTA from '@/components/shared/LocationEnrollCTA';
import IndiaContactForm from '@/components/shared/IndiaContactForm';

// FAQ Item Component
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200 py-4">
      <button
        className="flex justify-between items-center w-full text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-lg font-semibold text-gray-900">{question}</h3>
        {isOpen ? (
          <Minus className="w-5 h-5 text-[#009990]" />
        ) : (
          <Plus className="w-5 h-5 text-[#009990]" />
        )}
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

// Get icon component by name
const getIcon = (iconName: string | undefined) => {
  const icons: Record<string, any> = {
    Baby,
    School,
    GraduationCap,
    Building2,
    Puzzle,
    Trees
  };

  return icons[iconName || 'School'] || School;
};

// Get directions URL
const getDirectionsUrl = (address: { street?: string; city?: string; state?: string } | undefined) => {
  // Direct link for Nipania
  return 'https://maps.app.goo.gl/Rae3WeBFha2S5rrd7';
};

export default function NipaniaPage() {
  const locationData = indiaLocations['nipania'] || {};

  // Provide default values for all properties to prevent undefined errors
  const {
    name = 'Indore - Nipania',
    badge = 'PreSchool',
    heading = 'Welcome to Tulip Kids Academy - Nipania',
    description = 'Discover excellence in early childhood education with our comprehensive approach to learning and development.',
    heroImage = '/images/locations/nipania.jpg',
    heroImageAlt = 'Tulip Kids Academy Nipania Location',
    features = [],
    facilities = [],
    address = {
      street: '27, Samar Park Colony, Nipania',
      city: 'Indore',
      state: 'MP',
      zip: '452010',
      phone: '+91 9575545200',
      email: '<EMAIL>'
    },
    faq = []
  } = locationData;

  const locationSlug = 'nipania';

  return (
    <main className="page-container">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/locations/nipania.jpg"
            alt="Tulip Kids Academy Nipania Location"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations" className="hover:text-white transition-colors">
                    Locations
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations/india" className="hover:text-white transition-colors">
                    India
                  </Link>
                </li>
              </ol>
            </nav>

            <div className="max-w-3xl">
              <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                {badge}
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{heading}</h1>
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
                {description}
              </p>

              <div className="mt-8">
                <Link
                  href={`/contact?country=india&location=${locationSlug}&source=nipania_hero&medium=website&campaign=india_locations`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* India Excellence Section */}
      <IndiaExcellenceSection />

      {/* Programs Section */}
      <section className="py-24 bg-gray-50" id="programs">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Our Programs</h2>
            <p className="text-gray-600 text-lg">
              Comprehensive educational programs designed for each developmental stage
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {/* Hardcoded program items to ensure they always appear */}
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <Baby className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-xl font-bold text-[#001A6E] mb-4 text-center">Junior Pre-School (2-3 years)</h3>
              <p className="text-gray-600 text-center">Nurturing environment with specialized caregivers and age-appropriate activities.</p>
            </div>
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <School className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-xl font-bold text-[#001A6E] mb-4 text-center">Preschool Program (3-4 years)</h3>
              <p className="text-gray-600 text-center">Focus on early learning, social skills, and developmental milestones.</p>
            </div>
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <School className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-xl font-bold text-[#001A6E] mb-4 text-center">Pre-K Program (4-5 years)</h3>
              <p className="text-gray-600 text-center">Comprehensive curriculum including STEAM, language, and creative arts.</p>
            </div>
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <GraduationCap className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-xl font-bold text-[#001A6E] mb-4 text-center">Kindergarten (5-6 years)</h3>
              <p className="text-gray-600 text-center">Advanced learning preparation for school success.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Facilities Section */}
      <section className="py-24 bg-white" id="facilities">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Our Facilities</h2>
            <p className="text-gray-600 text-lg">
              State-of-the-art facilities designed for your child's comfort and safety
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {/* Hardcoded facility items to ensure they always appear */}
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <Building2 className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-2xl font-bold text-[#001A6E] mb-4 text-center">Modern Classrooms</h3>
              <p className="text-gray-600 text-center">Spacious, well-lit classrooms equipped with age-appropriate learning materials.</p>
            </div>
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <Trees className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-2xl font-bold text-[#001A6E] mb-4 text-center">Outdoor Play Area</h3>
              <p className="text-gray-600 text-center">Safe and engaging outdoor space for physical activities and nature exploration.</p>
            </div>
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6">
              <div className="w-16 h-16 bg-[#E6FFF9] rounded-full flex items-center justify-center mb-6 mx-auto">
                <Puzzle className="w-8 h-8 text-[#009990]" />
              </div>
              <h3 className="text-2xl font-bold text-[#001A6E] mb-4 text-center">Activity Center</h3>
              <p className="text-gray-600 text-center">Dedicated space for arts, crafts, and interactive learning activities.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4">
        <LocationEnrollCTA
          locationSlug={locationSlug}
          programType="preschool"
          name={name || ''}
          address={address || {
            street: '',
            city: '',
            state: '',
            zip: '',
            phone: '',
            email: ''
          }}
        />
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-50" id="contact">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Contact Us</h2>
              <p className="text-gray-600 text-lg">
                Get in touch with us to learn more about our programs and enrollment opportunities at Nipania.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 items-start">
              {/* Contact Form */}
              <div className="order-2 md:order-1">
                <IndiaContactForm
                  locationId={locationSlug}
                  locationName={name}
                  className="h-full"
                />
              </div>

              {/* Location Information */}
              <div className="order-1 md:order-2">
                <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Visit Our Location</h3>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <MapPin className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Address</h4>
                        <p className="text-gray-600">{address?.street}</p>
                        <p className="text-gray-600">{address?.city}, {address?.state} {address?.zip}</p>
                        <a
                          href={getDirectionsUrl(address)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-[#009990] hover:text-[#008880] mt-2 font-medium"
                        >
                          Get Directions
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-6">Contact Information</h3>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <Phone className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                        <a
                          href={`tel:${address?.phone}`}
                          className="text-gray-600 hover:text-[#009990] transition-colors"
                        >
                          {address?.phone}
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                          <Mail className="w-6 h-6 text-[#009990]" />
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                        <a
                          href={`mailto:${address?.email}`}
                          className="text-gray-600 hover:text-[#009990] transition-colors"
                        >
                          {address?.email}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Operating Hours */}
                <div className="bg-[#001A6E] rounded-2xl p-8 text-white">
                  <h3 className="text-2xl font-bold mb-6">Operating Hours</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Monday - Saturday</span>
                      <span>09:00 AM - 03:30 PM</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Sunday</span>
                      <span>Closed</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      {faq && faq.length > 0 && (
        <section className="py-24 bg-white" id="faq">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-[#001A6E] mb-4">Frequently Asked Questions</h2>
                <p className="text-gray-600 text-lg">
                  Find answers to common questions about our programs and facilities
                </p>
              </div>

              <div className="space-y-2">
                {faq.map((item, index) => (
                  <FAQItem
                    key={index}
                    question={item.question}
                    answer={item.answer}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </main>
  );
}
