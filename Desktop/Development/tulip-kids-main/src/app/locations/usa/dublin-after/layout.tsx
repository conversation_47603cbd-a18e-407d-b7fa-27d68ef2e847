import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Tulip Kids Inc After School Program - Dublin After School, USA',
  description: 'Discover the Tulip Kids Inc After School Program at our Dublin location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/dublin-after'
  },
  openGraph: {
    title: 'Tulip Kids Inc After School Program - Dublin After School, USA',
    description: 'Discover the Tulip Kids Inc After School Program at our Dublin location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!',
    url: 'https://www.tulipkidsinc.com/locations/usa/dublin-after',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tulip Kids Inc After School Program - Dublin Location, USA',
    description: 'Discover the Tulip Kids Inc After School Program at our Dublin location. Offering safe, engaging, and fun after-school care for children in the USA. Learn more today!'
  }
};

export default function DublinAfterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
