'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  MapPin,
  Phone,
  Mail,
  ArrowRight,
  Clock,
  Users,
  GraduationCap,
  BookOpen,
  Palette,
  Music,
  Calculator,
  Globe,
  Zap,
  Trophy,
  Target,
  Code,
  Heart,
  ShieldCheck,
  Building2,
  User<PERSON>heck
} from 'lucide-react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';

export default function GraftonStAfterLocation() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative">
        <Image
          src="/images/locations/Dublin.jpg"
          alt="Grafton St After School Location"
          width={1200}
          height={600}
          className="w-full h-[400px] object-cover"
          priority
        />

        {/* Breadcrumb Navigation */}
        <div className="container mx-auto px-4 py-6">
          <nav>
            <ol className="flex items-center space-x-2 text-sm text-gray-600">
              <li><Link href="/" className="hover:text-[#009990] transition-colors">Home</Link></li>
              <li>/</li>
              <li><Link href="/locations" className="hover:text-[#009990] transition-colors">Locations</Link></li>
              <li>/</li>
              <li><Link href="/locations/usa" className="hover:text-[#009990] transition-colors">USA</Link></li>
              <li>/</li>
              <li className="text-gray-900">Grafton St After School</li>
            </ol>
          </nav>
        </div>

        {/* Hero Content */}
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl">
            <div className="inline-block px-4 py-2 mb-6 bg-[#009990]/10 rounded-full">
              <span className="text-[#009990] font-medium">After School Program</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-[#074799]">
              Welcome to Grafton St After School
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-3xl">
              Our Grafton St After School program provides a dynamic learning environment where students can thrive academically and socially. We offer comprehensive after-school care with a focus on homework support, enrichment activities, and creative development.
            </p>
            <Link
              href="/contact?country=usa&location=grafton-st-after&program=afterSchool"
              className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Schedule a Tour
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* About Our After School Program */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#074799]">
                About Our After School Program
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Discover a nurturing environment where children thrive through engaging activities and personalized attention.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <Image
                  src="/images/locations/Dublin.jpg"
                  alt="Children engaged in after school activities"
                  width={600}
                  height={400}
                  className="rounded-2xl shadow-lg"
                />
              </div>
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold mb-4 text-[#074799]">A Safe and Enriching Environment</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Our after school program provides a perfect blend of academic support and recreational activities. We create an environment where children can learn, grow, and have fun in a safe and supervised setting.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <BookOpen className="w-8 h-8 text-[#009990]" />
                    </div>
                    <h4 className="font-semibold text-[#074799] mb-2">Homework Support</h4>
                    <p className="text-sm text-gray-600">Homework Support</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="w-8 h-8 text-[#009990]" />
                    </div>
                    <h4 className="font-semibold text-[#074799] mb-2">STEAM Activities</h4>
                    <p className="text-sm text-gray-600">STEAM Activities</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Heart className="w-8 h-8 text-[#009990]" />
                    </div>
                    <h4 className="font-semibold text-[#074799] mb-2">Physical Education</h4>
                    <p className="text-sm text-gray-600">Physical Education</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* After School Program Details */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#074799]">
                After School Program
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Tulip After School (TAS) offers a comprehensive enrichment program designed to nurture academic excellence and creative growth in a safe, stimulating environment.
              </p>
            </div>

            <div className="grid lg:grid-cols-4 gap-8 mb-16">
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-8 h-8 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-[#074799]">Program Hours</h3>
                <p className="text-gray-600">After School - 6:30 PM</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-[#074799]">Transportation</h3>
                <p className="text-gray-600">Available from local schools</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-[#074799]">Age Group</h3>
                <p className="text-gray-600">5 - 12 years</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="w-8 h-8 text-[#009990]" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-[#074799]">Core Activities</h3>
                <p className="text-gray-600">Comprehensive enrichment</p>
              </div>
            </div>

            {/* Core Activities */}
            <div className="mb-16">
              <h3 className="text-2xl font-bold mb-8 text-[#074799] text-center">Core Activities</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <BookOpen className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Homework Help</h4>
                  <p className="text-sm text-gray-600">Dedicated support for daily assignments</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Calculator className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Math and Language</h4>
                  <p className="text-sm text-gray-600">Enrichment in core academic subjects</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Heart className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Yoga</h4>
                  <p className="text-sm text-gray-600">Physical and mental wellness activities</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Palette className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Arts and Crafts</h4>
                  <p className="text-sm text-gray-600">Creative expression and skill development</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Music className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Cultural Dance</h4>
                  <p className="text-sm text-gray-600">Bollywood, Folk, and Fusion styles</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Globe className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Spanish</h4>
                  <p className="text-sm text-gray-600">Language learning through immersion</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Zap className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">STEAM</h4>
                  <p className="text-sm text-gray-600">Hands-on science and technology projects</p>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-md">
                  <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center mb-4">
                    <Trophy className="w-6 h-6 text-[#009990]" />
                  </div>
                  <h4 className="font-semibold text-[#074799] mb-2">Spelling Bee</h4>
                  <p className="text-sm text-gray-600">Vocabulary and language competitions</p>
                </div>
              </div>
            </div>

            {/* Additional Activities & Clubs */}
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold mb-6 text-[#074799]">Additional Activities</h3>
                <div className="space-y-4">
                  <div className="bg-white rounded-xl p-4 shadow-md flex items-center">
                    <div className="w-10 h-10 bg-[#009990]/10 rounded-lg flex items-center justify-center mr-4">
                      <Target className="w-5 h-5 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#074799]">Chess</h4>
                      <p className="text-sm text-gray-600">Strategic thinking and planning</p>
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-4 shadow-md flex items-center">
                    <div className="w-10 h-10 bg-[#009990]/10 rounded-lg flex items-center justify-center mr-4">
                      <Heart className="w-5 h-5 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#074799]">Basketball</h4>
                      <p className="text-sm text-gray-600">Team sports and physical activity</p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold mb-6 text-[#074799]">Clubs</h3>
                <div className="space-y-4">
                  <div className="bg-white rounded-xl p-4 shadow-md flex items-center">
                    <div className="w-10 h-10 bg-[#009990]/10 rounded-lg flex items-center justify-center mr-4">
                      <Users className="w-5 h-5 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#074799]">Public Speaking</h4>
                      <p className="text-sm text-gray-600">Communication skills development</p>
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-4 shadow-md flex items-center">
                    <div className="w-10 h-10 bg-[#009990]/10 rounded-lg flex items-center justify-center mr-4">
                      <Code className="w-5 h-5 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#074799]">Coding Club</h4>
                      <p className="text-sm text-gray-600">Introduction to programming</p>
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-4 shadow-md flex items-center">
                    <div className="w-10 h-10 bg-[#009990]/10 rounded-lg flex items-center justify-center mr-4">
                      <Heart className="w-5 h-5 text-[#009990]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#074799]">Community Service</h4>
                      <p className="text-sm text-gray-600">Giving back to our community</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Visit Our Location */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-[#074799] text-center">
              Visit Our Location
            </h2>

            <div className="grid lg:grid-cols-2 gap-12">
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6 text-[#074799]">Address</h3>
                <div className="space-y-4">
                  <p className="text-gray-600">4078 Grafton St</p>
                  <p className="text-gray-600">Dublin, CA 94568</p>
                  <Link
                    href="https://www.google.com/maps/dir/?api=1&destination=4078%20Grafton%20St%2C%20Dublin%2C%20CA"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-[#009990] hover:text-[#008880] transition-colors duration-200"
                  >
                    Get Directions
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </div>
              </div>

              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6 text-[#074799]">Contact Information</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-[#009990] mr-3" />
                    <span className="font-semibold text-gray-900">Phone:</span>
                    <a href="tel:(*************" className="ml-2 text-[#009990] hover:text-[#008880]">
                      (*************
                    </a>
                  </div>
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 text-[#009990] mr-3" />
                    <span className="font-semibold text-gray-900">Email:</span>
                    <a href="mailto:<EMAIL>" className="ml-2 text-[#009990] hover:text-[#008880]">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                <div className="mt-8">
                  <Link
                    href="/contact?country=usa&location=grafton-st-after&program=afterSchool"
                    className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Schedule a Tour
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* World-Class Facilities */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#074799]">
                Our Campus
              </h2>
              <h3 className="text-2xl font-semibold mb-6 text-[#074799]">
                World-Class Facilities
              </h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Experience our state-of-the-art facilities designed to nurture learning, creativity, and growth
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ShieldCheck className="w-8 h-8 text-[#009990]" />
                </div>
                <h4 className="text-lg font-bold mb-2 text-[#074799]">Safety First</h4>
                <p className="text-gray-600">Advanced security systems</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="w-8 h-8 text-[#009990]" />
                </div>
                <h4 className="text-lg font-bold mb-2 text-[#074799]">Modern Campus</h4>
                <p className="text-gray-600">State-of-the-art infrastructure</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UserCheck className="w-8 h-8 text-[#009990]" />
                </div>
                <h4 className="text-lg font-bold mb-2 text-[#074799]">Small Groups</h4>
                <p className="text-gray-600">Personalized attention</p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-16 h-16 bg-[#009990]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="w-8 h-8 text-[#009990]" />
                </div>
                <h4 className="text-lg font-bold mb-2 text-[#074799]">Learning Spaces</h4>
                <p className="text-gray-600">Designed for development</p>
              </div>
            </div>

            <div className="text-center">
              <Link
                href="/contact?country=usa&location=grafton-st-after&program=afterschool"
                className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Schedule a Campus Tour
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Us */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#074799]">
                Contact Us
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Get in touch with us to learn more about our programs and enrollment opportunities.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Visit Our Location */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-8 text-[#074799]">Visit Our Location</h3>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center">
                        <MapPin className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Address</h4>
                      <p className="text-gray-600">4078 Grafton St</p>
                      <p className="text-gray-600">Dublin, CA 94568</p>
                      <Link
                        href="https://www.google.com/maps/dir/?api=1&destination=4078%20Grafton%20St%2C%20Dublin%2C%20CA"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-[#009990] hover:text-[#008880] transition-colors duration-200 mt-2"
                      >
                        Get Directions
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Link>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center">
                        <Phone className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                      <a href="tel:(*************" className="text-[#009990] hover:text-[#008880] transition-colors">
                        (*************
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#009990]/10 rounded-lg flex items-center justify-center">
                        <Mail className="w-6 h-6 text-[#009990]" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                      <a href="mailto:<EMAIL>" className="text-[#009990] hover:text-[#008880] transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule a Tour */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6 text-[#074799]">Schedule a Tour</h3>
                <p className="text-gray-600 mb-8">
                  Visit our facility to experience our nurturing environment firsthand. Our team will be happy to show you around and answer any questions you may have.
                </p>
                <Link
                  href="/contact?country=usa&location=grafton-st-after&program=afterSchool"
                  className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Schedule a Tour
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-4 bg-[#009990]/10 backdrop-blur-sm rounded-full">
                <span className="text-[#009990] font-medium">FAQ</span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#074799]">
                Common Questions About Our Grafton St After School Program
              </h2>
              <p className="text-lg text-gray-600">
                Get answers to frequently asked questions about our after school activities, schedules, and enrichment programs
              </p>
            </div>

            <div className="space-y-8">
              {usaProgramsFAQs.afterSchool.slice(0, 2).map((category, index) => (
                <div key={index}>
                  <h3 className="text-2xl font-semibold text-[#074799] mb-6">
                    {category.category}
                  </h3>
                  <Accordion type="single" collapsible className="space-y-4">
                    {category.items.map((faq, faqIndex) => (
                      <AccordionItem
                        key={faqIndex}
                        value={`item-${index}-${faqIndex}`}
                        className="bg-white rounded-lg border border-gray-200 shadow-sm"
                      >
                        <AccordionTrigger className="px-6 py-4 text-left hover:no-underline">
                          <span className="font-medium text-[#074799]">{faq.question}</span>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 pb-4">
                          <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              ))}
            </div>

            <div className="mt-12 text-center">
              <Link
                href="/faq"
                className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Browse All FAQs
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
