'use client';

import React from 'react';
import Image from 'next/image';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowR<PERSON>, MapPin, Phone, Mail } from 'lucide-react';
import AfterSchoolSection from '@/components/sections/AfterSchoolSection';
import LocationEnrollCTA from '@/components/shared/LocationEnrollCTA';
import { usaLocations } from '@/data/locations';

export default function GraftonStAfterLocation() {
  const locationData = usaLocations['grafton-st-after'];
  const locationSlug = 'grafton-st-after';

  return (
    <main className="other-pages-container min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src={locationData.heroImage}
            alt={locationData.heroImageAlt}
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations" className="hover:text-white transition-colors">
                    Locations
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <Link href="/locations/usa" className="hover:text-white transition-colors">
                    USA
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li className="text-white font-medium">{locationData.name}</li>
              </ol>
            </nav>
            <div className="space-y-6 relative">
              <span className="inline-block bg-[#009990]/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium">
                {locationData.badge}
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white leading-tight drop-shadow-sm">
                {locationData.heading}
              </h1>
              <p className="text-lg text-white/95 max-w-2xl drop-shadow-sm">
                {locationData.description}
              </p>
              <div className="pt-4">
                <Link
                  href={`/contact?country=usa&location=${locationSlug}&program=afterSchool`}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-[#009990] rounded-full hover:bg-[#074799] transition-colors"
                >
                  Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* After School Program Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-[42px] font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#074799] to-[#009990]">
                About Our After School Program
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover a nurturing environment where children thrive through engaging activities and personalized attention.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src={locationData.heroImage}
                  alt="Children engaged in after school activities"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-4">
                    A Safe and Enriching Environment
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    Our after school program provides a perfect blend of academic support and recreational activities. We create an environment where children can learn, grow, and have fun in a safe and supervised setting.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-[#001A6E] mb-4">
                    Comprehensive Program Features
                  </h3>
                  <ul className="space-y-4">
                    {locationData.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-[#E6FFF9] rounded-lg flex items-center justify-center">
                            <ArrowRight className="w-5 h-5 text-[#009990]" />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">{feature}</h4>
                          <p className="text-gray-600">{feature}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* After School Section */}
      <section className="bg-white" id="programs">
        <AfterSchoolSection />
      </section>

      {/* Enrollment CTA */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-8">
          <LocationEnrollCTA
            locationSlug={locationSlug}
            programType="afterSchool"
            className="max-w-5xl mx-auto"
          />
        </div>
      </section>

      {/* Location Card Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-8">
          <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="p-8">
              <h2 className="text-3xl font-bold mb-6">Visit Our Location</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Address</h3>
                  {locationData.address && (
                    <>
                      <p className="text-gray-600 mb-2">{locationData.address.street}</p>
                      <p className="text-gray-600">{locationData.address.city}, {locationData.address.state} {locationData.address.zip}</p>
                      <div className="mt-6">
                        <Link
                          href={`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(`${locationData.address.street}, ${locationData.address.city}, ${locationData.address.state}`)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-[#009990] hover:text-[#008880] transition-colors duration-200"
                          aria-label={`Get directions to ${locationData.name}`}
                        >
                          <MapPin className="w-5 h-5 mr-2" />
                          Get Directions <ArrowRight className="w-5 h-5 ml-2" />
                        </Link>
                      </div>
                    </>
                  )}
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Contact Information</h3>
                  {locationData.address && (
                    <>
                      <p className="text-gray-600 mb-2">
                        <strong>Phone:</strong> {locationData.address.phone}
                      </p>
                      <p className="text-gray-600 mb-6">
                        <strong>Email:</strong> {locationData.address.email}
                      </p>
                    </>
                  )}
                  <Link
                    href={`/contact?country=usa&location=${locationSlug}&program=afterSchool`}
                    className="inline-flex items-center bg-[#009990] text-white px-6 py-3 rounded-full hover:bg-[#074799] transition-colors"
                  >
                    Schedule a Tour <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <span className="inline-block px-4 py-2 mb-4 bg-[#009990]/10 backdrop-blur-sm rounded-full">
              <span className="text-[#009990] font-medium">FAQ</span>
            </span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-[#074799]">
              Common Questions About Our Dublin After School Program
            </h2>
            <p className="text-lg text-gray-600">
              Get answers to frequently asked questions about our after school activities, schedules, and enrichment programs
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {usaProgramsFAQs.afterSchool.slice(0, 2).map((category, index) => (
              <div key={index} className="mb-8">
                <h3 className="text-2xl font-semibold text-[#074799] mb-6">
                  {category.category}
                </h3>
                <div className="space-y-4">
                  {category.items.map((item, itemIndex) => (
                    <Accordion key={itemIndex} type="single" collapsible className="bg-white rounded-xl">
                      <AccordionItem value={`${index}-${itemIndex}`} className="border-none">
                        <AccordionTrigger className="px-6 py-4 text-left hover:no-underline group">
                          <span className="text-gray-800 font-medium group-hover:text-[#009990] transition-colors">
                            {item.question}
                          </span>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 pb-4">
                          <p className="text-gray-600 leading-relaxed">
                            {item.answer}
                          </p>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link
              href="/faq"
              className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Browse All FAQs
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}