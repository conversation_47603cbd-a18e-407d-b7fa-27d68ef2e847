'use client';

import React from 'react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import AfterSchoolTemplate from '@/components/templates/AfterSchoolTemplate';
import { usaLocations } from '@/data/locations/index';

export default function GraftonStAfterLocation() {
  const baseLocationData = usaLocations['grafton-st-after'];

  // Transform location data to match the required type
  const locationData = {
    ...baseLocationData,
    slug: 'grafton-st-after',
    country: 'usa' as const,
    type: baseLocationData.type || ['after-school']
  };

  return (
    <>
      <AfterSchoolTemplate locationData={locationData} />


    </>
  );
}
