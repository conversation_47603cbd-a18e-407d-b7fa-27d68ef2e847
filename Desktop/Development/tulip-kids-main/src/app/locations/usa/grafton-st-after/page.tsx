'use client';

import React from 'react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import AfterSchoolTemplate from '@/components/templates/AfterSchoolTemplate';
import { usaLocations } from '@/data/locations';

export default function GraftonStAfterLocation() {
  const baseLocationData = usaLocations['grafton-st-after'];
  
  // Transform features array to match the required type and ensure required fields
  const locationData = {
    ...baseLocationData,
    slug: 'grafton-st-after',
    country: 'usa' as const,
    type: baseLocationData.type || ['after-school'],
    features: baseLocationData.features.map(feature => ({
      title: feature,
      description: feature,
    }))
  };

  return (
    <>
      <AfterSchoolTemplate locationData={locationData} />

      {/* After School Program Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <span className="inline-block px-4 py-2 mb-4 bg-[#009990]/10 backdrop-blur-sm rounded-full">
              <span className="text-[#009990] font-medium">After School Program</span>
            </span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-[#074799]">
              Comprehensive After School Care
            </h2>
            <p className="text-lg text-gray-600">
              Our after school program provides a safe, engaging environment where children can complete homework, participate in enrichment activities, and develop social skills.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <span className="inline-block px-4 py-2 mb-4 bg-[#009990]/10 backdrop-blur-sm rounded-full">
              <span className="text-[#009990] font-medium">FAQ</span>
            </span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-[#074799]">
              Common Questions About Our Grafton St After School Program
            </h2>
            <p className="text-lg text-gray-600">
              Get answers to frequently asked questions about our after school activities, schedules, and enrichment programs
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {usaProgramsFAQs.afterSchool.slice(0, 2).map((category, index) => (
              <div key={index} className="mb-8">
                <h3 className="text-2xl font-semibold text-[#074799] mb-6">
                  {category.category}
                </h3>
                <Accordion type="single" collapsible className="space-y-4">
                  {category.items.map((faq, faqIndex) => (
                    <AccordionItem
                      key={faqIndex}
                      value={`item-${index}-${faqIndex}`}
                      className="bg-white rounded-lg border border-gray-200 shadow-sm"
                    >
                      <AccordionTrigger className="px-6 py-4 text-left hover:no-underline">
                        <span className="font-medium text-[#074799]">{faq.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pb-4">
                        <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 px-8 py-4 rounded-full bg-gradient-to-r from-[#074799] to-[#009990] text-white font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Have More Questions? Contact Us
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
