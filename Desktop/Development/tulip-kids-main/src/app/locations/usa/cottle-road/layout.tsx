import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Best Preschools in San Jose, CA | Tulip Kids',
  description: 'Discover Tulip Kids, a nurturing play-based daycare and Preschools in San Jose, CA. Our preschool program is designed to help children acquire social skills in a fun way.',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/locations/usa/cottle-road'
  },
  openGraph: {
    title: 'Best Preschools in San Jose, CA | Tulip Kids',
    description: 'Discover Tulip Kids, a nurturing play-based daycare and Preschools in San Jose, CA. Our preschool program is designed to help children acquire social skills in a fun way.',
    url: 'https://www.tulipkidsinc.com/locations/usa/cottle-road',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Preschools in San Jose, CA | Tulip Kids',
    description: 'Discover Tulip Kids, a nurturing play-based daycare and Preschools in San Jose, CA. Our preschool program is designed to help children acquire social skills in a fun way.'
  }
};

export default function CottleRoadLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
