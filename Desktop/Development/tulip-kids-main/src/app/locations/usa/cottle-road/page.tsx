'use client';

import React from 'react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import { usaProgramsFAQs } from '@/data/faq/usa-programs';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import PreschoolTemplate from '@/components/templates/PreschoolTemplate';
import { usaLocations } from '@/data/locations';

export default function CottleRoadLocation() {
  const baseLocationData = usaLocations['cottle-road'];
  
  // Transform features array to match the required type
  const locationData = {
    ...baseLocationData,
    slug: 'cottle-road',
    country: 'usa' as const,
    features: baseLocationData.features.map(feature => ({
      title: feature,
      description: feature,
    }))
  };

  return (
    <>
      <PreschoolTemplate locationData={locationData} />

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white via-gray-50/30 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block px-4 py-2 rounded-full bg-[#009990]/10 backdrop-blur-sm mb-6">
              <span className="text-[#009990] font-medium">FAQ</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[#074799] to-[#009990] bg-clip-text text-transparent">
              Common Questions About Our Preschool
            </h2>
            <p className="text-lg text-gray-600">
              Find answers to frequently asked questions about our preschool program at Cottle Road
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            {usaProgramsFAQs.preschool.slice(0, 2).map((category, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-[#074799] mb-4">
                  {category.category}
                </h3>
                <Accordion type="single" collapsible className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <AccordionItem
                      key={itemIndex}
                      value={`${index}-${itemIndex}`}
                      className="border border-gray-100 rounded-xl px-4 hover:border-[#009990]/30 transition-colors duration-300"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        <span className="text-gray-800 font-medium">{item.question}</span>
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600">
                        {item.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link 
              href="/faq" 
              className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-[#074799] to-[#009990] text-white hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl"
            >
              Browse All FAQs
              <ArrowRight size={20} />
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
