import { Metadata } from 'next';
import PreschoolTemplate from '@/components/templates/PreschoolTemplate';
import { usaLocations } from '@/data/locations/index';

export const metadata: Metadata = {
  title: 'Tulip Kids Sunnyvale - Willow Ave | Premium Early Childhood Education',
  description: 'Our Willow Avenue center offers premium childcare and early education programs for children aged 6 weeks to 6 years in Sunnyvale, California.',
};

export default function SunnyvaleWillowPage() {
  const locationData = {
    ...usaLocations['willow-ave'],
    country: 'usa' as const
  };
  
  return (
    <PreschoolTemplate 
      locationData={locationData}
    />
  );
}
