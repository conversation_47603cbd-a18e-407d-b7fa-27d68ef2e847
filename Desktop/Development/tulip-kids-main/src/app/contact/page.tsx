import { Metadata } from 'next'
import { MapPin, Phone, Mail, Clock, MessageCircle, ArrowRight } from 'lucide-react'
import ContactForm from '@/components/shared/ContactForm'
import Image from 'next/image'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Contact Us | Tulip Kids Preschool in India',
  description: 'Reach out to Tulip Kids Preschool in India for admissions, franchise inquiries, or corporate partnerships. We\'re here to help you nurture young minds. Contact us today!',
  keywords: ['contact Tulip Kids', 'preschool admissions', 'franchise inquiries', 'corporate partnerships', 'Tulip Kids contact'],
  openGraph: {
    title: 'Contact Us | Tulip Kids Preschool in India',
    description: 'Reach out to Tulip Kids Preschool in India for admissions, franchise inquiries, or corporate partnerships. Contact us today!',
    url: 'https://www.tulipkidsinc.com/contact',
    siteName: 'Tulip Kids',
    type: 'website',
  },
}

export default function ContactPage() {
  const usaContactInfo = {
    phone: '(*************',
    email: '<EMAIL>',
    hours: 'Monday - Friday: 8:00 AM - 6:00 PM PST',
    address: '1159 Willow Ave, Sunnyvale, CA 94086'
  }

  const indiaContactInfo = {
    phone: '+91 9575545200',
    email: '<EMAIL>',
    hours: 'Monday - Friday: 9:00 AM - 3:30 PM IST',
    address: '11, Bima Nagar, Anand Bazaar, Indore, MP 452001'
  }

  return (
    <div className="page-container">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 mx-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/contact-us.jpg"
            alt="Contact Us"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-4 md:px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <ArrowRight className="w-4 h-4" />
                </li>
                <li className="text-white">Contact</li>
              </ol>
            </nav>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Contact Us
            </h1>
            <p className="text-lg text-white/90 max-w-2xl">
              Have questions about enrollment, our programs, or anything else? We'd love to hear from you. Get in touch with our team today.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="page-section">
        <div className="container mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="relative">
              {/* Decorative Elements */}
              <div className="absolute -top-12 -right-12 w-24 h-24 bg-[#009990]/10 rounded-full blur-2xl" />
              <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-[#001A6E]/10 rounded-full blur-2xl" />

              <div className="relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 p-8 lg:p-12">
                <div className="max-w-3xl mx-auto">
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-[#001A6E] to-[#009990] bg-clip-text text-transparent mb-2 text-center">Send Us a Message</h2>
                  <p className="text-gray-600 mb-8 text-center">Fill out the form below and we'll get back to you within 24 hours.</p>
                  <ContactForm />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information Section */}
      <section className="pb-16 bg-gray-50/50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-[#001A6E] text-center mb-12">Our Locations</h2>

            <div className="grid md:grid-cols-2 gap-8">
              {/* USA Office */}
              <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-14 h-14 rounded-2xl bg-[#001A6E]/10 flex items-center justify-center">
                    <MapPin className="w-7 h-7 text-[#001A6E]" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-[#001A6E]">USA Office</h3>
                    <p className="text-gray-600">Main Headquarters</p>
                  </div>
                </div>

                <div className="grid sm:grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <MapPin className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Address</p>
                        <p className="text-gray-600">{usaContactInfo.address}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Phone className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Phone</p>
                        <a href={`tel:${usaContactInfo.phone}`} className="text-[#009990] hover:text-[#001A6E] transition-colors">
                          {usaContactInfo.phone}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Mail className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Email</p>
                        <a href={`mailto:${usaContactInfo.email}`} className="text-[#009990] hover:text-[#001A6E] transition-colors">
                          {usaContactInfo.email}
                        </a>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Clock className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Hours</p>
                        <p className="text-gray-600">{usaContactInfo.hours}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* India Office */}
              <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-14 h-14 rounded-2xl bg-[#001A6E]/10 flex items-center justify-center">
                    <MapPin className="w-7 h-7 text-[#001A6E]" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-[#001A6E]">India Office</h3>
                    <p className="text-gray-600">Regional Branch</p>
                  </div>
                </div>

                <div className="grid sm:grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <MapPin className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Address</p>
                        <p className="text-gray-600">{indiaContactInfo.address}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Phone className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Phone</p>
                        <a href={`tel:${indiaContactInfo.phone}`} className="text-[#009990] hover:text-[#001A6E] transition-colors">
                          {indiaContactInfo.phone}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Mail className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Email</p>
                        <a href={`mailto:${indiaContactInfo.email}`} className="text-[#009990] hover:text-[#001A6E] transition-colors">
                          {indiaContactInfo.email}
                        </a>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-xl bg-[#009990]/10 flex items-center justify-center">
                        <Clock className="w-5 h-5 text-[#009990]" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Hours</p>
                        <p className="text-gray-600">{indiaContactInfo.hours}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
