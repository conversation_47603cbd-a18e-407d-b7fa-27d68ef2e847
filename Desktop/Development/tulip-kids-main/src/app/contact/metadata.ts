import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us | Tulip Kids Preschool in India',
  description: 'Reach out to Tulip Kids Preschool in India for admissions, franchise inquiries, or corporate partnerships. We\'re here to help you nurture young minds. Contact us today!',
  keywords: ['contact Tulip Kids', 'preschool admissions', 'franchise inquiries', 'corporate partnerships', 'Tulip Kids contact'],
  openGraph: {
    title: 'Contact Us | Tulip Kids Preschool in India',
    description: 'Reach out to Tulip Kids Preschool in India for admissions, franchise inquiries, or corporate partnerships. We\'re here to help you nurture young minds. Contact us today!',
    url: 'https://www.tulipkidsinc.com/contact',
    siteName: 'Tulip Kids',
    type: 'website',
  },
};
