import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Playschool & Playschool Franchise | Partner with Tulip Kids',
  description: 'Join <PERSON><PERSON> as a Partner! Explore preschool franchise opportunities and inspire young minds. Begin your journey to educational excellence today!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/franchise'
  },
  openGraph: {
    title: 'Playschool & Playschool Franchise | Partner with Tulip Kids',
    description: 'Join Tulip Kids as a Partner! Explore preschool franchise opportunities and inspire young minds. Begin your journey to educational excellence today!',
    url: 'https://www.tulipkidsinc.com/franchise',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Playschool & Playschool Franchise | Partner with Tulip Kids',
    description: 'Join Tulip Kids as a Partner! Explore preschool franchise opportunities and inspire young minds. Begin your journey to educational excellence today!'
  }
};

export default function FranchiseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
