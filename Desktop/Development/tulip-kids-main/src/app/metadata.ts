import { Metadata } from 'next';

export const defaultMetadata: Metadata = {
  verification: {
    google: 'EVTgthboqrXa4EzM68vdp-zSr5ErQ22s7vtid5W6Xjg',
  },
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: {
    default: 'Best Preschool & PlaySchool for Kids - Nursery & Kindergarten',
    template: '%s | Tulip Kids'
  },
  description: 'Tulip Kids is one of the best preschools. We offer Playgroup, Nursery & Kindergarten for kids. Visit our nearest Play school for your child\'s admission.',
  keywords: [
    'preschool',
    'early childhood education',
    'daycare',
    'child care',
    'after school program',
    'kindergarten',
    'early learning',
    'child development',
    'Bay Area preschool',
    'India preschool'
  ],
  authors: [{ name: 'Tulip Kids' }],
  creator: 'Tulip Kids',
  publisher: 'Tulip Kids',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://tulipkidsinc.com',
    siteName: 'Tulip Kids',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Tulip Kids - Early Childhood Education & Care'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    site: '@tulipkids',
    creator: '@tulipkids',
    images: ['/images/twitter-image.jpg']
  }
}
