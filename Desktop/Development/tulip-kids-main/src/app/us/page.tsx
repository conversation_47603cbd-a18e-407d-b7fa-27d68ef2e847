import React from 'react'
import { Metadata } from 'next'
import PreschoolUSAPage from '@/components/pages/PreschoolUSAPage'

export const metadata: Metadata = {
  title: 'Tulip Kids USA - Premier Preschool Education in California',
  description: 'Discover Tulip Kids preschools across California. Our locations offer comprehensive early education programs including Junior Pre-School, Preschool, Pre-K, and Kindergarten programs.',
  keywords: [
    'California preschool',
    'Sunnyvale preschool',
    'Santa Clara childcare',
    'Bay Area kindergarten',
    'early education California',
    'preschool programs',
    'child development center',
    'daycare California'
  ],
  openGraph: {
    title: 'Tulip Kids USA - Premier Preschool Education in California',
    description: 'Discover Tulip Kids preschools across California. Our locations offer comprehensive early education programs.',
    images: ['/images/locations/usa-locations-hero.jpg']
  }
}

export default function USPage() {
  return <PreschoolUSAPage />
}
