import { Metadata } from 'next';
import ProgramTemplate from '@/components/templates/ProgramTemplate';
import { preschoolPrograms } from '@/data/programs/preschool';

export const metadata: Metadata = {
  title: 'Pre-Kindergarten Program | Tulip Kids',
  description: 'Our Pre-K Program prepares children ages 4-5 for kindergarten success through a comprehensive curriculum including math, science, phonics, and social studies.',
  keywords: ['pre-kindergarten', 'pre-k program', 'kindergarten preparation', 'early education'],
};

export default function PreKindergartenPage() {
  const program = preschoolPrograms.find(p => p.id === 'pre-kindergarten')!;
  
  return <ProgramTemplate program={program} />;
}
