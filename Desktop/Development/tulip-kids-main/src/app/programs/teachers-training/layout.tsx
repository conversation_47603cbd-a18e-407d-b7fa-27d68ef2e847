import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  metadataBase: new URL('https://tulipkidsinc.com'),
  title: 'Teacher Training Program | Tulip Kids - Build a Rewarding Teaching Career',
  description: 'Join <PERSON>lip Kids Teacher Training Program to enhance your skills and build a rewarding career in early childhood education. Contact us Teacher Training Program!',
  alternates: {
    canonical: 'https://www.tulipkidsinc.com/programs/teachers-training'
  },
  openGraph: {
    title: 'Teacher Training Program | Tulip Kids - Build a Rewarding Teaching Career',
    description: 'Join <PERSON>lip Kids Teacher Training Program to enhance your skills and build a rewarding career in early childhood education. Contact us Teacher Training Program!',
    url: 'https://www.tulipkidsinc.com/programs/teachers-training',
    siteName: 'Tulip Kids',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Teacher Training Program | Tulip Kids - Build a Rewarding Teaching Career',
    description: 'Join Tulip Kids Teacher Training Program to enhance your skills and build a rewarding career in early childhood education. Contact us Teacher Training Program!'
  }
};

export default function TeacherTrainingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
