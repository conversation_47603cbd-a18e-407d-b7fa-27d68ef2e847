'use client';

import Link from 'next/link';
import { Phone, MapPin, ArrowRight } from 'lucide-react';

interface Location {
  id: string;
  name: string;
  badge: string;
  phone: string;
  email?: string;
  programs: ('preschool' | 'afterSchool')[];
  enrollmentLink: string;
  afterSchoolPhone?: string;
  afterSchoolEmail?: string;
  afterSchoolEnrollmentLink?: string;
}

// USA locations data
const usaLocations: Location[] = [
  {
    id: 'willow-ave',
    name: '1159 Willow Ave, Sunnyvale, CA 94086',
    badge: 'PreSchool & After School',
    phone: '(*************',
    email: '<EMAIL>',
    afterSchoolPhone: '(*************',
    afterSchoolEmail: '<EMAIL>',
    programs: ['preschool', 'afterSchool'],
    enrollmentLink: 'https://schools.procareconnect.com/register/tulip-kids-willow-ave-sunnyvale-94086',
    afterSchoolEnrollmentLink: 'https://schools.procareconnect.com/form/7b1b76e9-4d5b-4810-96a7-96f7fb4dc13c'
  },
  {
    id: 'lawrence-station',
    name: '1279 Lawrence Station, Sunnyvale, CA 94086',
    badge: 'PreSchool',
    phone: '(*************',
    programs: ['preschool'],
    enrollmentLink: 'https://schools.procareconnect.com/register/tulip-kids-ls-sunnyvale-94086'
  },
  {
    id: 'el-camino',
    name: '2280 East El Camino Real, Santa Clara, CA 95050',
    badge: 'PreSchool',
    phone: '(*************',
    email: '<EMAIL>',
    programs: ['preschool'],
    enrollmentLink: 'https://schools.procareconnect.com/register/2596080c-cc3c-43bc-8d2b-3835b893d805'
  },
  {
    id: 'cottle-road',
    name: '6097 Cottle Rd, San Jose, CA 95123',
    badge: 'PreSchool',
    phone: '(*************',
    email: '<EMAIL>',
    programs: ['preschool'],
    enrollmentLink: 'https://schools.procareconnect.com/register/673683fa-25cf-4328-a931-4d6c5d4ebfad'
  },
  {
    id: 'grafton-st-after',
    name: '4078 Grafton St, Dublin, CA 94568',
    badge: 'After School',
    phone: '(*************',
    email: '<EMAIL>',
    programs: ['afterSchool'],
    enrollmentLink: '/contact'
  },
  {
    id: 'mountain-house',
    name: '768 N Montebello St, Mountain House, CA 95391',
    badge: 'PreSchool',
    phone: '(*************',
    email: '<EMAIL>',
    programs: ['preschool'],
    enrollmentLink: 'https://schools.procareconnect.com/register/3ea394f5-2b40-4150-85d1-2ce734c856ae'
  }
];

const indiaLocations: Location[] = [
  {
    id: 'bima-nagar',
    name: '11, Bima Nagar, Indore',
    badge: 'PreSchool',
    phone: '+91 9575545952',
    email: '<EMAIL>',
    programs: ['preschool'],
    enrollmentLink: '/contact'
  },
  {
    id: 'nipania',
    name: '27, Samar Park Colony, Nipania, Indore',
    badge: 'PreSchool',
    phone: '+91 9575545200',
    email: '<EMAIL>',
    programs: ['preschool'],
    enrollmentLink: '/contact'
  }
];

export default function EnrollPage() {
  return (
    <div className="other-pages-container min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4 py-16 pt-32">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-[#001A6E] mb-3">
              Begin Your Journey with Tulip Kids
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose your preferred location to start the enrollment process
            </p>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-bold text-[#001A6E] mb-6">United States</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {usaLocations.map((location) => (
                <LocationCard key={location.id} location={location} />
              ))}
            </div>
          </div>

          <div>
            <h2 className="text-2xl font-bold text-[#001A6E] mb-6">India</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {indiaLocations.map((location) => (
                <LocationCard key={location.id} location={location} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function LocationCard({ location }: { location: Location }) {
  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-200 hover:border-gray-300">
      <div className="p-5">
        <div className="flex items-start justify-between mb-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-[#001A6E]" />
              <h2 className="text-lg font-semibold text-gray-900">
                {location.name}
              </h2>
            </div>
            <span className="inline-block px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-50 text-blue-700">
              {location.badge}
            </span>
          </div>
        </div>

        <div className="space-y-2 mb-4 text-sm">
          <div className="flex items-center text-gray-600">
            <Phone className="w-3.5 h-3.5 mr-2 flex-shrink-0" />
            <div>
              <a
                href={`tel:${location.phone.replace(/[^0-9]/g, '')}`}
                className="hover:text-[#001A6E] transition-colors"
              >
                {location.phone}
              </a>
              {location.programs.includes('afterSchool') && location.programs.includes('preschool') && (
                <span className="text-xs text-gray-500 ml-1">(Preschool)</span>
              )}
            </div>
          </div>
          {location.programs.includes('afterSchool') && location.afterSchoolPhone && (
            <div className="flex items-center text-gray-600">
              <Phone className="w-3.5 h-3.5 mr-2 flex-shrink-0" />
              <div>
                <a
                  href={`tel:${location.afterSchoolPhone.replace(/[^0-9]/g, '')}`}
                  className="hover:text-[#001A6E] transition-colors"
                >
                  {location.afterSchoolPhone}
                </a>
                <span className="text-xs text-gray-500 ml-1">(After School)</span>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-2">
          {location.programs.includes('preschool') && (
            <Link
              href={location.enrollmentLink}
              className="inline-flex justify-between items-center px-4 py-2 rounded-lg text-white bg-[#009990] hover:bg-[#00b3a8] transition-all duration-300 group text-sm"
            >
              <span className="font-medium">Enroll in Preschool</span>
              <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
            </Link>
          )}
          {location.programs.includes('afterSchool') && (
            <Link
              href={location.afterSchoolEnrollmentLink || location.enrollmentLink}
              className="inline-flex justify-between items-center px-4 py-2 rounded-lg text-white bg-[#074799] hover:bg-[#0957bd] transition-all duration-300 group text-sm"
            >
              <span className="font-medium">Enroll in After School</span>
              <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
