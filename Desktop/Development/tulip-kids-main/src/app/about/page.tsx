'use client';

import Image from 'next/image';
import { 
  Heart,
  BookOpen,
  Lightbulb,
  ArrowRight,
  GraduationCap,
  Puzzle,
  Award,
  Users,
  Brain
} from 'lucide-react';
import Link from 'next/link';




const features = [
  {
    title: "15+ Years Experience",
    description: "Our founders bring over 15 years of combined experience in childcare and education in the San Francisco Bay Area.",
    icon: Award
  },
  {
    title: "Whole Child Development",
    description: "We focus on nurturing every aspect of child development - cognitive, academic, physical, social, aesthetic, and emotional.",
    icon: Heart
  },
  {
    title: "Research-Based Practices",
    description: "We implement the latest research-based educational practices to develop essential 21st Century skills.",
    icon: Brain
  },
  {
    title: "Balanced Learning",
    description: "Our programs balance structured lessons, independent projects, and fun group activities.",
    icon: Puzzle
  }
];

const values = [
  {
    title: "Life-Long Learning",
    description: "We make learning fun and engaging to foster a lifelong love of knowledge.",
    icon: BookOpen
  },
  {
    title: "Innovation",
    description: "We continuously adapt and improve our methods based on the latest educational research.",
    icon: Lightbulb
  },
  {
    title: "Individual Attention",
    description: "We maintain low teacher-to-student ratios to ensure personalized attention.",
    icon: Users
  },
  {
    title: "Academic Excellence",
    description: "We maintain high standards while making learning enjoyable and accessible.",
    icon: GraduationCap
  }
];




export default function AboutPage() {
  return (
    <main className="page-container min-h-screen">
     
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/about-us.jpg"
            alt="Children learning and playing at Tulip Kids"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">About Us</span>
                </li>
              </ol>
            </nav>
            <div className="space-y-6">
              <div className="inline-block px-4 py-1.5 rounded-full bg-[#009990] text-sm text-white font-medium">
                About Tulip Kids
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                Shaping Future Leaders Through Early Education
              </h1>
              <p className="text-lg text-white/90 max-w-2xl">
                At Tulip Kids, we're dedicated to nurturing young minds through innovative education and comprehensive care, creating a foundation for lifelong success.
              </p>
              <div className="flex flex-wrap gap-8 pt-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/20 flex items-center justify-center">
                    <Award className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">15+ Years</h3>
                    <p className="text-white/70 text-sm">Experience</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/20 flex items-center justify-center">
                    <Users className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">1000+ Parents</h3>
                    <p className="text-white/70 text-sm">Trust Us</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-2xl bg-[#009990]/20 flex items-center justify-center">
                    <GraduationCap className="w-6 h-6 text-[#009990]" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">100% Success</h3>
                    <p className="text-white/70 text-sm">Rate</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              {/* Image Side */}
              <div className="relative">
                <div className="relative h-[500px] rounded-3xl overflow-hidden">
                  <Image
                    src="/images/about-o1.webp"
                    alt="Children engaged in learning activities at Tulip Kids"
                    fill
                    className="object-cover"
                  />
                </div>
                {/* Stats Card */}
                <div className="absolute -bottom-8 -right-8 bg-white rounded-2xl shadow-xl p-6 max-w-xs">
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-3xl font-bold text-[#001A6E]">15+</h4>
                      <p className="text-sm text-gray-600">Years Experience</p>
                    </div>
                    <div>
                      <h4 className="text-3xl font-bold text-[#001A6E]">1000+</h4>
                      <p className="text-sm text-gray-600">Happy Parents</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Side */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Our Journey</h2>
                  <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                    A Legacy of Excellence in Early Education
                  </h3>
                </div>

                <div className="space-y-6 text-gray-600">
                  <p className="leading-relaxed">
                    Our Founders have more than 15 years of combined experience caring for and teaching children in formal settings in the San Francisco Bay Area. Fulfilling the needs of families, a high level of parental satisfaction, and the efficacy of our programs have resulted in our amazing growth.
                  </p>
                  <p className="leading-relaxed">
                    Virtually all of our growth has come from "word of mouth" as parents tell other parents about us and children tell their friends about "the awesome place they go to after school".
                  </p>
                  <p className="leading-relaxed">
                    What sets us apart from other child care, schools, and enrichment centers is our dedication to nurturing "the whole child". We believe that children should be encouraged to learn as much as they can, and in order for them to enjoy learning, and therefore more likely to become life-long learners, it's our responsibility to make it fun for them.
                  </p>
                </div>

                {/* Timeline */}
                <div className="pt-8 space-y-6">
                  <h4 className="text-lg font-semibold text-[#001A6E]">Key Milestones</h4>
                  <div className="space-y-4">
                    <div className="flex gap-4">
                      <div className="w-16 shrink-0">
                        <span className="text-lg font-bold text-[#009990]">2008</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">First Center Opened</h5>
                        <p className="text-sm text-gray-600">Started our journey in Sunnyvale, CA</p>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <div className="w-16 shrink-0">
                        <span className="text-lg font-bold text-[#009990]">2015</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Expanded to Multiple Locations</h5>
                        <p className="text-sm text-gray-600">Opened centers across Bay Area</p>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <div className="w-16 shrink-0">
                        <span className="text-lg font-bold text-[#009990]">2020</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">International Expansion</h5>
                        <p className="text-sm text-gray-600">Launched our first center in India</p>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <div className="w-16 shrink-0">
                        <span className="text-lg font-bold text-[#009990]">2023</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Award-Winning Programs</h5>
                        <p className="text-sm text-gray-600">Recognized for educational excellence</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What Makes Us Different */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Our Unique Approach</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                What Makes Us Different
              </h3>
              <p className="text-gray-600 leading-relaxed">
                At Tulip Kids, we take a comprehensive approach to early childhood education, focusing on developing well-rounded individuals through our innovative methods and dedicated care.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div 
                    key={feature.title}
                    className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow group"
                  >
                    <div className="mb-4 w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center group-hover:bg-[#009990]/10 transition-colors">
                      <Icon className="w-6 h-6 text-[#001A6E] group-hover:text-[#009990] transition-colors" />
                    </div>
                    <h4 className="text-lg font-semibold text-[#001A6E] mb-2">
                      {feature.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Values Section */}
            <div className="mt-20">
              <div className="text-center max-w-3xl mx-auto mb-16">
                <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Our Core Values</h2>
                <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                  Values That Drive Us
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our values shape everything we do, from our teaching methods to our interaction with children and parents.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {values.map((value, index) => {
                  const Icon = value.icon;
                  return (
                    <div 
                      key={value.title}
                      className="relative bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#001A6E] to-[#009990] rounded-t-2xl"></div>
                      <div className="pt-4">
                        <div className="mb-4 w-12 h-12 rounded-xl bg-[#001A6E]/5 flex items-center justify-center">
                          <Icon className="w-6 h-6 text-[#009990]" />
                        </div>
                        <h4 className="text-lg font-semibold text-[#001A6E] mb-2">
                          {value.title}
                        </h4>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {value.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* CTA Section */}
            <div className="mt-20 text-center">
              <div className="max-w-3xl mx-auto bg-[#001A6E] rounded-3xl p-12 relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/pattern.png')] opacity-10"></div>
                <div className="relative z-10">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Ready to Join the Tulip Kids Family?
                  </h3>
                  <p className="text-white/80 mb-8 max-w-xl mx-auto">
                    Experience our unique approach to early childhood education and see the difference for yourself.
                  </p>
                  <Link href="/contact" className="inline-flex items-center px-6 py-3 bg-[#009990] text-white rounded-full font-medium hover:bg-[#009990]/90 transition-colors">
                    Schedule a Visit
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Educational Approach */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-sm font-medium text-[#009990] uppercase tracking-wider mb-3">Educational Philosophy</h2>
              <h3 className="text-3xl md:text-4xl font-bold text-[#001A6E] mb-6">
                Our Educational Approach
              </h3>
              <p className="text-gray-600 leading-relaxed">
                We believe in nurturing each child's unique potential through a balanced blend of academic excellence, creative exploration, and social development.
              </p>
            </div>

            {/* Approach Grid */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Academic Excellence */}
              <div className="relative bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all group">
                <div className="absolute top-0 left-0 w-full h-1 bg-[#009990] rounded-t-2xl"></div>
                <div className="mb-6">
                  <div className="w-14 h-14 rounded-2xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                    <GraduationCap className="w-7 h-7 text-[#009990] group-hover:scale-110 transition-transform" />
                  </div>
                  <h4 className="text-xl font-semibold text-[#001A6E] mb-3">Academic Excellence</h4>
                  <p className="text-gray-600 leading-relaxed">
                    Our curriculum is designed to challenge and inspire, fostering critical thinking and problem-solving skills through engaging, age-appropriate activities.
                  </p>
                </div>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Structured learning programs</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Hands-on activities</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Regular progress assessments</span>
                  </li>
                </ul>
              </div>

              {/* Creative Development */}
              <div className="relative bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all group">
                <div className="absolute top-0 left-0 w-full h-1 bg-[#009990] rounded-t-2xl"></div>
                <div className="mb-6">
                  <div className="w-14 h-14 rounded-2xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                    <Puzzle className="w-7 h-7 text-[#009990] group-hover:scale-110 transition-transform" />
                  </div>
                  <h4 className="text-xl font-semibold text-[#001A6E] mb-3">Creative Development</h4>
                  <p className="text-gray-600 leading-relaxed">
                    We encourage creative expression through art, music, and imaginative play, helping children develop their unique voices and perspectives.
                  </p>
                </div>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Arts and crafts sessions</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Music and movement</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Dramatic play opportunities</span>
                  </li>
                </ul>
              </div>

              {/* Social Development */}
              <div className="relative bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all group">
                <div className="absolute top-0 left-0 w-full h-1 bg-[#009990] rounded-t-2xl"></div>
                <div className="mb-6">
                  <div className="w-14 h-14 rounded-2xl bg-[#001A6E]/5 flex items-center justify-center mb-4">
                    <Heart className="w-7 h-7 text-[#009990] group-hover:scale-110 transition-transform" />
                  </div>
                  <h4 className="text-xl font-semibold text-[#001A6E] mb-3">Social Development</h4>
                  <p className="text-gray-600 leading-relaxed">
                    We create a nurturing environment where children learn to collaborate, communicate, and build meaningful relationships with peers and teachers.
                  </p>
                </div>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Group activities</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Social skills development</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-[#009990]"></div>
                    <span>Emotional intelligence focus</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Additional Info */}
            <div className="mt-16 bg-[#001A6E]/5 rounded-3xl p-8 md:p-12">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <h4 className="text-2xl font-bold text-[#001A6E]">
                    Our Commitment to Excellence
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    At Tulip Kids, we're committed to providing the highest quality early education. Our approach combines proven teaching methods with innovative practices, ensuring each child receives the attention and support they need to thrive.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                        <Award className="w-5 h-5 text-[#009990]" />
                      </div>
                      <span className="font-medium text-[#001A6E]">Certified Teachers</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                        <Users className="w-5 h-5 text-[#009990]" />
                      </div>
                      <span className="font-medium text-[#001A6E]">Small Class Sizes</span>
                    </div>
                  </div>
                </div>
                <div className="relative h-64 rounded-2xl overflow-hidden">
                  <Image
                    src="/images/about-o1.webp"
                    alt="Children learning at Tulip Kids"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      

      
    </main>
  );
}
