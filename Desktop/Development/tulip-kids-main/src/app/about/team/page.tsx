'use client';

import Image from 'next/image';
import Link from 'next/link';

interface FounderProfile {
  name: string;
  role: string;
  bio: string[];
  videoId: string;
  imageUrl: string;
}

const founders: Founder<PERSON><PERSON><PERSON><PERSON>[] = [
  {
    name: "<PERSON><PERSON>",
    role: "Co-Founder",
    bio: [
      "Ms<PERSON>'s undergraduate degree (Liberal arts) and postgraduate diploma (interior design) were accomplished in Mumbai, India. After arriving in the USA, Ms<PERSON> decided to pursue her life-long interest and passion for child care and education. She began her formal ECE training and soon after, jumped into the first of many volunteer services, with a Child Care Center in San Jose, CA.",
      "Convinced she'd found her true calling, Ms<PERSON> founded her first proprietary venture, \"Tulip Family Day Care\", eight years ago, which was an immediate success. This led her and her partner, <PERSON><PERSON>, to embrace the challenge of a second venture, \"Tulip After School\", which has also grown by leaps and bounds, due to their unwavering commitment to provide the highest level of quality family services, along with their talents for creating warmly nurturing, richly varied, challenging, but fun learning environments for children.",
      "Meanwhile, <PERSON>lip Family Day Care, A.K.A. Tulip Kids has been perennially full to capacity, with a long waiting list, so the Directors purchased a larger facility in Sunnyvale, which is renovated to transform the site into their own custom-designed learning and child development center. \"Tulip Kids Academy\" opened in 2013.",
      "Ms. <PERSON>ti, along with her <PERSON>sband <PERSON>, are raising their two sons and highly enjoying the process of watching their own children grow, learn, and changing incrementally into the amazing adult that each of their sons will become.",
      "In 2008, Ms. <PERSON>ti was presented with an award from the <PERSON>, <PERSON> of <PERSON>vale, for her expertise, sound business practices, and a proven track record of outstanding local contributions in the field of \"child care and development\". Ms. <PERSON>ti is currently a nominee for the \"<PERSON> Citizen Medal\", which is the second highest civilian award which can be presented to an American citizen, created for the recognition of individuals who perform exemplary service in their community (or elsewhere) for the benefit of fellow humans."
    ],
    videoId: "meoKLzu_Qi8",
    imageUrl: "/images/team/deepti-tulip.jpg"
  },
  {
    name: "Sneha Vedula",
    role: "Co-Founder",
    bio: [
      "Ms. Sneha received her undergraduate degree (accounting) in India and subsequently earned her MS in Human Resources Management & Psychology from Golden Gate University, San Francisco.",
      "Ms.Sneha has become a well-recognized figure among local educational professionals, following five years of volunteer work with Dougherty Elementary School, service as a Board Member for their Site Council Team, after-school programs teaching, other after-school site involvements in San Jose, extensive participation & promotion of multicultural events either as a sponsor and/or a performer/ choreographer, and finally, as a teacher in a diverse array of summer camps. Ms. Sneha has also served for several years as a Team Manager of \"Destination Imagination\" student groups.",
      "Ms. Sneha's indomitable enthusiasm for interacting with and educating youngsters of all ages is only equaled by her passion for dance and artistic expression through dance forms.She is an extensively-trained, highly polished performer in multiple genres, and also an accomplished choreographer. Ms. Sneha works with many of the most well-known dance groups in the Bay Area. As such, she maintains an exciting annual schedule of dance events as a performer and makes a significant contribution to the sharing of heritage traditions and cultural art forms among the diverse peoples of the Bay Area. Ms.Sneha is actively involved in several non-profit organizations in the Bay Area and initiates an astounding level of fundraising activities, as does Ms. Deepti.",
      "Ms. Sneha believes that children should have an opportunity to learn about, and then to actively explore the widest possible range of positive human experiences, and that a well-balanced educational plan should aspire to achieve more than academic growth; That great learning environments will also address children's needs to develop physically, creatively, aesthetically, socially, and emotionally, hopefully in ways which will be inspiring and FUN for the children."
    ],
    videoId: "vz4-hURJi38",
    imageUrl: "/images/team/sneha-tulip.jpg"
  }
];

function YouTubeEmbed({ videoId }: { videoId: string }) {
  return (
    <div className="relative w-full aspect-video rounded-xl overflow-hidden shadow-lg">
      <iframe
        src={`https://www.youtube.com/embed/${videoId}`}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="absolute top-0 left-0 w-full h-full"
      />
    </div>
  );
}

function FounderCard({ profile, index }: { profile: FounderProfile; index: number }) {
  const isEven = index % 2 === 0;
  
  return (
    <div className={`grid md:grid-cols-2 gap-8 ${isEven ? '' : 'md:grid-flow-dense'}`}>
      {/* Image and Video Section */}
      <div className="space-y-8">
        <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-lg">
          <Image
            src={profile.imageUrl}
            alt={profile.name}
            fill
            className="object-cover"
          />
        </div>
        <YouTubeEmbed videoId={profile.videoId} />
      </div>

      {/* Content Section */}
      <div className={`space-y-6 ${isEven ? 'md:pl-8' : 'md:pr-8'}`}>
        <div>
          <h3 className="text-3xl font-bold mb-2">{profile.name}</h3>
          <p className="text-[#009990] text-xl font-semibold">{profile.role}</p>
        </div>
        <div className="prose max-w-none space-y-4">
          {profile.bio.map((paragraph, index) => (
            <p key={index} className="text-gray-600 leading-relaxed">
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function TeamPage() {
  return (
    <main className="page-container min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 m-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/about-us.jpg"
            alt="Our Team at Tulip Kids"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <nav className="mb-6" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm text-white/90">
              <li>
                <Link href="/" className="hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <span className="mx-2">/</span>
              </li>
              <li>
                <Link href="/about" className="hover:text-white transition-colors">
                  About
                </Link>
              </li>
              <li>
                <span className="mx-2">/</span>
              </li>
              <li>
                <span className="text-white">Our Team</span>
              </li>
            </ol>
          </nav>
          <div className="max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Founders
            </h1>
            <p className="text-xl text-white/90">
              Meet the visionary leaders behind Tulip Kids who have dedicated their lives to transforming early childhood education through innovation, passion, and unwavering commitment.
            </p>
          </div>
        </div>
      </section>

      {/* Founders Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="space-y-32">
            {founders.map((founder, index) => (
              <FounderCard key={index} profile={founder} index={index} />
            ))}
          </div>
        </div>
      </section>
    </main>
  );
}
