'use client';

import CookiePolicySection from '@/components/templates/CookiePolicySection';
import Image from 'next/image';
import Link from 'next/link';

export default function CookiePolicyPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative min-h-[400px] flex items-center bg-[#001A6E]/90 mt-20 mx-4 overflow-hidden rounded-3xl">
        <div className="absolute inset-0">
          <Image
            src="/images/programs/hero-programs.jpg"
            alt="Tulip Kids Cookie Policy"
            fill
            className="object-cover opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#001A6E]/90 via-[#001A6E]/75 to-[#001A6E]/40"></div>
        </div>
        <div className="container mx-auto px-8 py-16 relative z-10">
          <div className="max-w-4xl">
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-white/90">
                <li>
                  <Link href="/" className="hover:text-white transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <span className="mx-2">/</span>
                </li>
                <li>
                  <span className="text-white">Cookie Policy</span>
                </li>
              </ol>
            </nav>

            <div className="max-w-3xl">
              <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
                Legal
              </span>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Cookie Policy
              </h1>
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
                Understanding how and why we use cookies to improve your experience on our website
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Policy Content */}
      <CookiePolicySection />
    </main>
  );
}
