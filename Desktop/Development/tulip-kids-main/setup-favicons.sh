#!/bin/bash

# Define source and destination paths
SOURCE_DIR="/Users/<USER>/Desktop/Backup-Drvie/01 - PROJECTS/Website Design/tulip-kids/public/images"
DEST_DIR="/Users/<USER>/Desktop/Backup-Drvie/01 - PROJECTS/Website Design/tulip-kids/public"

# Create destination directory if it doesn't exist
mkdir -p "$DEST_DIR"

# Copy favicon files to the root public directory
cp "$SOURCE_DIR/web-app-manifest-192x192.png" "$DEST_DIR/favicon-192x192.png"
cp "$SOURCE_DIR/web-app-manifest-512x512.png" "$DEST_DIR/favicon-512x512.png"

# If you have other favicon files, copy them too
if [ -f "$SOURCE_DIR/favicon-for-app/favicon.ico" ]; then
  cp "$SOURCE_DIR/favicon-for-app/favicon.ico" "$DEST_DIR/"
fi

if [ -f "$SOURCE_DIR/favicon-for-app/favicon-16x16.png" ]; then
  cp "$SOURCE_DIR/favicon-for-app/favicon-16x16.png" "$DEST_DIR/"
fi

if [ -f "$SOURCE_DIR/favicon-for-app/favicon-32x32.png" ]; then
  cp "$SOURCE_DIR/favicon-for-app/favicon-32x32.png" "$DEST_DIR/"
fi

if [ -f "$SOURCE_DIR/favicon-for-app/apple-touch-icon.png" ]; then
  cp "$SOURCE_DIR/favicon-for-app/apple-touch-icon.png" "$DEST_DIR/"
fi

# Create or update the site.webmanifest file
cat > "$DEST_DIR/site.webmanifest" << EOL
{
  "name": "Tulip Kids",
  "short_name": "Tulip Kids",
  "icons": [
    {
      "src": "/favicon-16x16.png",
      "sizes": "16x16",
      "type": "image/png"
    },
    {
      "src": "/favicon-32x32.png",
      "sizes": "32x32",
      "type": "image/png"
    },
    {
      "src": "/apple-touch-icon.png",
      "sizes": "180x180",
      "type": "image/png"
    },
    {
      "src": "/favicon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable"
    },
    {
      "src": "/favicon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable"
    }
  ],
  "theme_color": "#ffffff",
  "background_color": "#ffffff",
  "display": "standalone",
  "start_url": "/"
}
EOL

echo "Favicon files have been set up successfully!"