#!/bin/bash

# Create directory for favicon files if it doesn't exist
mkdir -p public

# Copy favicon files from the source directory to the public directory
cp public/images/favicon-for-app/favicon.ico public/
cp public/images/favicon-for-app/favicon-16x16.png public/
cp public/images/favicon-for-app/favicon-32x32.png public/
cp public/images/favicon-for-app/apple-touch-icon.png public/
cp public/images/favicon-for-app/web-app-manifest-192x192.png public/
cp public/images/favicon-for-app/web-app-manifest-512x512.png public/

# Copy the webmanifest file if it exists
if [ -f "public/images/favicon-for-app/site.webmanifest" ]; then
  cp public/images/favicon-for-app/site.webmanifest public/
else
  # Create the webmanifest file if it doesn't exist
  echo '{
  "name": "Tulip Kids",
  "short_name": "Tulip Kids",
  "icons": [
    {
      "src": "/favicon-16x16.png",
      "sizes": "16x16",
      "type": "image/png"
    },
    {
      "src": "/favicon-32x32.png",
      "sizes": "32x32",
      "type": "image/png"
    },
    {
      "src": "/apple-touch-icon.png",
      "sizes": "180x180",
      "type": "image/png"
    },
    {
      "src": "/web-app-manifest-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable"
    },
    {
      "src": "/web-app-manifest-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable"
    }
  ],
  "theme_color": "#ffffff",
  "background_color": "#ffffff",
  "display": "standalone",
  "start_url": "/"
}' > public/site.webmanifest
fi

echo "Favicon files organized successfully!"