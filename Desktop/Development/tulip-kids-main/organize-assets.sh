#!/bin/bash

# Create directory structure
mkdir -p public/images/programs/{preschool,after-school,summer-camp}/{hero,activities,gallery}
mkdir -p public/images/locations/{usa,india}/{gallery,facilities}
mkdir -p public/images/{team,accreditation,hero}
mkdir -p public/assets/{fonts,icons}

# Move existing images to appropriate directories
mv public/images/pre-school.jpg public/images/programs/preschool/hero/hero.jpg
mv public/images/after-school.webp public/images/programs/after-school/hero/hero.jpg
mv public/images/summer-camp.jpg public/images/programs/summer-camp/hero/hero.jpg

# Move location images
if [ -d "public/images/usa" ]; then
  mv public/images/usa/* public/images/locations/usa/ 2>/dev/null
fi

if [ -d "public/images/india" ]; then
  mv public/images/india/* public/images/locations/india/ 2>/dev/null
fi

# Move team images
if [ -d "public/images/team" ]; then
  mv public/images/team/* public/images/team/ 2>/dev/null
fi

# Move accreditation images
if [ -d "public/images/accreditation" ]; then
  mv public/images/accreditation/* public/images/accreditation/ 2>/dev/null
fi

# Clean up empty directories
rm -rf public/images/usa public/images/india public/images/other\ images

# Make sure all directories have proper permissions
chmod -R 755 public/images
chmod -R 755 public/assets
