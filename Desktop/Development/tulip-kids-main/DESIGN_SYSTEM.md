# Tulip Kids Website Design System

## Color Palette
- Primary Blue: #074799
- Primary Green: #009990
- Light Green: #E1FFBB
- White: #FFFFFF
- Gray: #F9FAFB (backgrounds)

## Layout Guidelines

### Hero Section
- Full-width gradient background (blue to green)
- Centered content with max-width constraints
- Large heading with descriptive subtext
- Search functionality with modern styling

### Why Choose Tulip Section
- 3-column grid layout for benefits
- Modern card design with hover effects
- Icon colors and backgrounds:
  - Expert Educators: #E1FFBB
  - Innovative Curriculum: #FFE4D6
  - Safe Environment: #D5F5FF
  - Personalized Care: #FFD6E8
  - Social Development: #E6E6FF
  - Creative Learning: #FFDFD6

### Location Directory
- Grid layout with responsive columns
- Location cards with:
  - Image display
  - "Get Directions" button overlay
  - Program tags
  - Contact information
  - Status badges (e.g., "Opening Soon")
- Search and filter functionality
- Country-specific grouping

## Component Styles

### Cards
- Rounded corners (2xl)
- Shadow with hover effect
- White background
- Padding: 1.5rem
- Transition effects on hover

### Buttons
- Primary: Blue background with white text
- Secondary: White background with blue text
- Rounded design
- Hover effects with color transitions

### Icons
- Lucide React icons
- Size consistency (w-4 h-4 for small, w-6 h-6 for medium)
- Color transitions on hover

### Typography
- Headings: Bold, primary blue color
- Body text: Regular weight, gray-600 color
- Font sizes:
  - H1: text-4xl/text-5xl
  - H2: text-3xl
  - H3: text-xl
  - Body: text-base/text-lg
  - Small: text-sm

### Spacing
- Section padding: py-20
- Component gaps: gap-6/gap-8
- Content margins: mb-4/mb-6/mb-8

## Interactive Elements

### Hover States
- Card shadow enlargement
- Color transitions
- Scale transforms where applicable
- Opacity changes for overlays

### Transitions
- Duration: 300ms/500ms
- Properties: all, colors, transform
- Timing: ease-in-out

## Responsive Design
- Mobile-first approach
- Breakpoints:
  - md: 768px
  - lg: 1024px
  - xl: 1280px
- Grid adjustments at breakpoints
- Font size scaling
- Spacing adaptations

## Special Features
- "Opening Soon" badges
- Location-specific map links
- Program type indicators
- Contact information display
- Search functionality with clear button
- Country/program type filters

## Accessibility
- Semantic HTML structure
- ARIA labels where needed
- Keyboard navigation support
- Sufficient color contrast
- Focus states for interactive elements

## Notes
This design system should be strictly followed for all future updates unless explicitly requested to change. Any modifications should maintain consistency with these established patterns and visual hierarchy.
